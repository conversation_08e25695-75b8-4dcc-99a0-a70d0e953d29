# 双引擎驱动低代码平台配置
vibe:
  meta:
    # 启用双引擎模式
    dual-engine-enabled: true

    # 默认元数据来源
    default-source: ANNOTATION

    # 元数据合并策略
    # PRIORITY_BASED: 基于优先级合并
    # ANNOTATION_FIRST: 注解优先
    # CONFIG_FIRST: 配置优先
    # DEEP_MERGE: 深度合并
    merge-strategy: PRIORITY_BASED

    # 缓存配置
    cache-enabled: true
    cache-expire-seconds: 3600

    # 版本控制
    version-control-enabled: false

    # 配置文件路径映射
    config-paths:
      demo: "classpath*:meta/demo/**/*.json"
      system: "classpath*:meta/system/**/*.json"

    # 实体特定配置
    entities:
      # 产品实体 - 注解驱动
      product:
        source: ANNOTATION
        cache-enabled: true
        properties:
          searchable: true
          exportable: true

      # 分类实体 - 配置驱动
      category:
        source: CONFIG
        config-path: "classpath:meta/demo/category.json"
        cache-enabled: true
        properties:
          tree-structure: true
          parent-field: "parent_id"

      # 用户实体 - 混合模式
      user:
        source: HYBRID
        cache-enabled: true
        properties:
          sensitive: true
          audit-enabled: true

# Spring Boot 配置
spring:
  profiles:
    active: dual-engine

  # 数据源配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Jackson配置
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL

# 日志配置
logging:
  level:
    com.edc.vibe_engine.meta: DEBUG
    com.edc.vibe_engine.meta.manager: DEBUG
    com.edc.vibe_engine.meta.provider: DEBUG
    com.edc.vibe_engine.meta.register: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,meta
  endpoint:
    health:
      show-details: always

  # 自定义端点
  info:
    vibe:
      meta:
        dual-engine-enabled: ${vibe.meta.dual-engine-enabled}
        merge-strategy: ${vibe.meta.merge-strategy}
        cache-enabled: ${vibe.meta.cache-enabled}
