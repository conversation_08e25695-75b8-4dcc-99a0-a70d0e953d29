{"id": "demo_category", "name": "category", "displayName": "产品分类", "module": "demo", "description": "产品分类管理", "source": "CONFIG", "priority": 50, "version": "1.0.0", "entity": {"type": "TABLE", "tableName": "demo_category", "softDelete": true, "auditable": true, "cacheable": true, "cacheExpireSeconds": 3600}, "fields": [{"id": "category_name", "name": "name", "displayName": "分类名称", "dataType": "TEXT", "size": 100, "flags": ["REQUIRED", "UNIQUE"], "validationRules": [{"type": "REQUIRED", "message": "分类名称不能为空"}, {"type": "MAX", "params": {"max": 100}, "message": "分类名称不能超过100个字符"}], "uiControl": {"type": "input", "placeholder": "请输入分类名称", "helpText": "分类的显示名称", "group": "基本信息", "order": 1}}, {"id": "category_code", "name": "code", "displayName": "分类编码", "dataType": "TEXT", "size": 50, "flags": ["REQUIRED", "UNIQUE"], "validationRules": [{"type": "REQUIRED", "message": "分类编码不能为空"}, {"type": "PATTERN", "params": {"pattern": "^[A-Z0-9_]{3,20}$"}, "message": "分类编码必须是3-20位大写字母、数字和下划线组合"}], "uiControl": {"type": "input", "placeholder": "请输入分类编码", "helpText": "分类的唯一标识码", "group": "基本信息", "order": 2}}, {"id": "category_description", "name": "description", "displayName": "分类描述", "dataType": "TEXT", "size": 500, "uiControl": {"type": "textarea", "props": {"rows": 3}, "placeholder": "请输入分类描述", "group": "基本信息", "order": 3}}, {"id": "category_parent_id", "name": "parent_id", "displayName": "上级分类", "dataType": "TEXT", "uiControl": {"type": "tree-select", "props": {"allowClear": true, "treeDefaultExpandAll": false}, "placeholder": "请选择上级分类", "helpText": "留空表示顶级分类", "group": "层级信息", "order": 1}}, {"id": "category_sort_order", "name": "sort_order", "displayName": "排序", "dataType": "INT", "defaultValue": 0, "validationRules": [{"type": "MIN", "params": {"min": 0}, "message": "排序值不能小于0"}], "uiControl": {"type": "number", "props": {"min": 0}, "placeholder": "请输入排序值", "helpText": "数值越小排序越靠前", "group": "层级信息", "order": 2}}, {"id": "category_is_active", "name": "is_active", "displayName": "是否启用", "dataType": "BOOLEAN", "defaultValue": true, "uiControl": {"type": "switch", "helpText": "禁用后该分类及其子分类将不可选择", "group": "状态信息", "order": 1}}, {"id": "category_icon", "name": "icon", "displayName": "图标", "dataType": "TEXT", "size": 100, "uiControl": {"type": "icon-picker", "placeholder": "请选择图标", "helpText": "分类的显示图标", "group": "显示设置", "order": 1}}, {"id": "category_color", "name": "color", "displayName": "颜色", "dataType": "TEXT", "size": 20, "validationRules": [{"type": "PATTERN", "params": {"pattern": "^#[0-9A-Fa-f]{6}$"}, "message": "请输入有效的颜色值（如：#FF0000）"}], "uiControl": {"type": "color-picker", "placeholder": "请选择颜色", "helpText": "分类的主题颜色", "group": "显示设置", "order": 2}}], "relations": [{"id": "category_parent", "name": "parent", "displayName": "上级分类", "type": "REF_ONE", "module": "demo", "entity": "category", "refField": "parent_id", "joinMapping": [{"field": "_id", "valExp": "self.parent_id"}]}, {"id": "category_children", "name": "children", "displayName": "子分类", "type": "JOIN_MANY", "module": "demo", "entity": "category", "joinMapping": [{"field": "parent_id", "valExp": "self._id"}]}, {"id": "category_products", "name": "products", "displayName": "产品列表", "type": "JOIN_MANY", "module": "demo", "entity": "product", "joinMapping": [{"field": "category_id", "valExp": "self._id"}]}], "rules": [{"id": "category_validation", "name": "category_validation", "type": "validation", "point": "before_save", "validationRules": {"parent_id": [{"type": "CUSTOM", "params": {"expression": "self.parent_id !== self._id"}, "message": "不能选择自己作为上级分类"}]}}], "extensions": {"searchable": true, "exportable": true, "importable": true, "treeStructure": true, "parentField": "parent_id", "orderField": "sort_order"}}