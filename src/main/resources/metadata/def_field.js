export default {
    metadata: {
        name: "def_field",
        display_name: "字段",
        module: "meta",
        fields: [
            {
                name: "name",
                display_name: "编码",
                type: "VARCHA<PERSON>",
                flags: []
            },
            {
                name: "display_name",
                display_name: "名称",
                type: "VARCHAR",
                flags: []
            },
            {
                name: "type",
                display_name: "数据类型",
                type: "VARCHAR",
                flags: []
            },
            {
                name: "description",
                display_name: "描述",
                type: "VARCHAR",
                flags: []
            },
            {
                name: "_id",
                display_name: "主键",
                type: "VARCHAR",
                flags: [
                    "PRIMARY"
                ]
            },
            {
                name: "_create_by",
                display_name: "创建人",
                type: "VARCHAR",
                flags: [
                    "NULLABLE"
                ]
            },
            {
                name: "_create_at",
                display_name: "创建时间",
                type: "DATE_TIME",
                flags: []
            },
            {
                name: "_update_by",
                display_name: "修改人",
                type: "VARCHA<PERSON>",
                flags: [
                    "NULL<PERSON><PERSON>"
                ]
            },
            {
                name: "_update_at",
                display_name: "修改时间",
                type: "DATE_TIME",
                flags: []
            }
        ],
        relations: [
            {
                name: "_creator",
                display_name: "创建人",
                field: "_create_by",
                entity: "user",
                module: "base",
                type: "ONE"
            },
            {
                name: "_updater",
                display_name: "修改人",
                field: "_update_by",
                entity: "user",
                module: "base",
                type: "ONE"
            }
        ],
        rules: []
    }
}
