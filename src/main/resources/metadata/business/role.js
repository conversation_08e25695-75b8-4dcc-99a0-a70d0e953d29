export default {
    metadata: {
        name: 'role',
        display_name: '角色',
        module: 'base',
        description: '系统角色定义',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '角色编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9_]{2,30}$',
                        message: '角色编号必须为2-30位大写字母、数字或下划线',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '角色名称不能为空',
                    },
                ],
            },
            {
                name: 'role_type',
                display_name: '角色类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '系统角色', value: 'SYSTEM'},
                        {label: '业务角色', value: 'BUSINESS'},
                        {label: '部门角色', value: 'DEPARTMENT'},
                        {label: '自定义角色', value: 'CUSTOM'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                    ],
                },
            },
            {
                name: 'description',
                display_name: '描述',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'data_scope',
                display_name: '数据范围',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '全部数据', value: 'ALL'},
                        {label: '本部门数据', value: 'DEPT'},
                        {label: '本部门及下级数据', value: 'DEPT_AND_CHILD'},
                        {label: '仅本人数据', value: 'SELF'},
                        {label: '自定义数据', value: 'CUSTOM'},
                    ],
                },
            },
            {
                name: 'data_scope_custom',
                display_name: '自定义数据范围',
                type: 'TEXT',
                flags: ['NULLABLE'],
                description: '自定义数据范围的JSON配置，仅当data_scope为CUSTOM时有效',
            },
            {
                name: 'dept_id',
                display_name: '所属部门',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '部门角色所属的部门，仅当role_type为DEPARTMENT时有效',
            },
            {
                name: 'org_id',
                display_name: '所属机构',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'sort_order',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'dept',
                display_name: '所属部门',
                field: 'dept_id',
                entity: 'department',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'org',
                display_name: '所属机构',
                field: 'org_id',
                entity: 'organization',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'users',
                display_name: '用户列表',
                field: 'role_id',
                entity: 'employee_role',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'permissions',
                display_name: '权限列表',
                field: 'role_id',
                entity: 'role_permission',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_role_code',
                fields: ['code'],
            },
            {
                name: 'idx_role_name',
                fields: ['name'],
            },
            {
                name: 'idx_role_type',
                fields: ['role_type'],
            },
            {
                name: 'idx_role_status',
                fields: ['status'],
            },
            {
                name: 'idx_role_dept_id',
                fields: ['dept_id'],
            },
            {
                name: 'idx_role_org_id',
                fields: ['org_id'],
            },
        ],
    },
}
