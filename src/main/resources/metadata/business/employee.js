export default {
    metadata: {
        name: 'employee',
        display_name: '雇员',
        module: 'base',
        description: '企业人员信息及系统用户账号',
        fields: [
            // 基本信息
            {
                name: 'code',
                display_name: '工号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '工号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9]{2,20}$',
                        message: '工号必须为2-20位大写字母和数字',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '姓名',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '姓名不能为空',
                    },
                ],
            },
            {
                name: 'nick_name',
                display_name: '昵称',
                type: 'VARCHAR',
                flags: ['KEY_INFO', 'NULLABLE'],
            },
            {
                name: 'name_pinyin',
                display_name: '姓名拼音',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '用于拼音搜索',
            },
            {
                name: 'name_en',
                display_name: '英文名',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'avatar',
                display_name: '头像',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '头像图片URL',
            },
            {
                name: 'sex',
                display_name: '性别',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '男', value: 'MALE'},
                        {label: '女', value: 'FEMALE'},
                        {label: '未知', value: 'UNKNOWN'},
                    ],
                },
            },
            {
                name: 'birth_date',
                display_name: '出生日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'id_type',
                display_name: '证件类型',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '身份证', value: 'ID_CARD'},
                        {label: '护照', value: 'PASSPORT'},
                        {label: '军官证', value: 'MILITARY_ID'},
                        {label: '港澳通行证', value: 'HK_MO_PASS'},
                        {label: '台湾通行证', value: 'TW_PASS'},
                        {label: '其他', value: 'OTHER'},
                    ],
                },
            },
            {
                name: 'id_number',
                display_name: '证件号码',
                type: 'VARCHAR',
                flags: ['NULLABLE', 'INTERNAL'],
                description: '身份证或其他证件号码，内部字段',
            },
            {
                name: 'nationality',
                display_name: '国籍',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'native_place',
                display_name: '籍贯',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'marital_status',
                display_name: '婚姻状况',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '未婚', value: 'UNMARRIED'},
                        {label: '已婚', value: 'MARRIED'},
                        {label: '离异', value: 'DIVORCED'},
                        {label: '丧偶', value: 'WIDOWED'},
                    ],
                },
            },
            {
                name: 'political_status',
                display_name: '政治面貌',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },

            // 联系信息
            {
                name: 'email',
                display_name: '邮箱',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                validation: [
                    {
                        type: 'email',
                        message: '邮箱格式不正确',
                    },
                ],
            },
            {
                name: 'work_email',
                display_name: '工作邮箱',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                validation: [
                    {
                        type: 'email',
                        message: '邮箱格式不正确',
                    },
                ],
            },
            {
                name: 'phone',
                display_name: '手机号',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                validation: [
                    {
                        type: 'pattern',
                        pattern: '^1[3-9]\\d{9}$',
                        message: '手机号格式不正确',
                    },
                ],
            },
            {
                name: 'work_phone',
                display_name: '工作电话',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'emergency_contact',
                display_name: '紧急联系人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'emergency_phone',
                display_name: '紧急联系电话',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'address',
                display_name: '居住地址',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },

            // 工作信息
            {
                name: 'employee_type',
                display_name: '员工类型',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '正式员工', value: 'REGULAR'},
                        {label: '试用期', value: 'PROBATION'},
                        {label: '实习生', value: 'INTERN'},
                        {label: '劳务派遣', value: 'DISPATCH'},
                        {label: '外包人员', value: 'OUTSOURCE'},
                        {label: '顾问', value: 'CONSULTANT'},
                        {label: '退休返聘', value: 'REHIRE'},
                    ],
                },
            },
            {
                name: 'employee_status',
                display_name: '员工状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '在职', value: 'ACTIVE'},
                        {label: '离职', value: 'RESIGNED'},
                        {label: '休假', value: 'LEAVE'},
                        {label: '停职', value: 'SUSPENDED'},
                        {label: '退休', value: 'RETIRED'},
                    ],
                },
            },
            {
                name: 'entry_date',
                display_name: '入职日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'probation_end_date',
                display_name: '试用期结束日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'regular_date',
                display_name: '转正日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'departure_date',
                display_name: '离职日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'departure_reason',
                display_name: '离职原因',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'work_years',
                display_name: '工作年限',
                type: 'DECIMAL',
                flags: ['NULLABLE'],
            },
            {
                name: 'job_title',
                display_name: '职位',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'job_level',
                display_name: '职级',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'job_rank',
                display_name: '职等',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'job_sequence',
                display_name: '职系',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'post_id',
                display_name: '岗位',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'dept_id',
                display_name: '所属部门',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'org_id',
                display_name: '所属机构',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'owner_id',
                display_name: '直属上级',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'work_location',
                display_name: '工作地点',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'office_location',
                display_name: '办公位置',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },

            // 教育背景
            {
                name: 'education',
                display_name: '最高学历',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '博士', value: 'PHD'},
                        {label: '硕士', value: 'MASTER'},
                        {label: '本科', value: 'BACHELOR'},
                        {label: '大专', value: 'COLLEGE'},
                        {label: '高中', value: 'HIGH_SCHOOL'},
                        {label: '初中', value: 'MIDDLE_SCHOOL'},
                        {label: '小学', value: 'PRIMARY_SCHOOL'},
                        {label: '其他', value: 'OTHER'},
                    ],
                },
            },
            {
                name: 'major',
                display_name: '专业',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'graduate_school',
                display_name: '毕业院校',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'graduate_date',
                display_name: '毕业日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },

            // 账号信息
            {
                name: 'username',
                display_name: '登录账号',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                validation: [
                    {
                        type: 'pattern',
                        pattern: '^[a-zA-Z0-9_]{4,20}$',
                        message: '登录账号必须为4-20位字母、数字或下划线',
                    },
                ],
            },
            {
                name: 'password',
                display_name: '密码',
                type: 'VARCHAR',
                flags: ['INTERNAL'],
            },
            {
                name: 'salt',
                display_name: '安全符',
                type: 'VARCHAR',
                flags: ['INTERNAL'],
            },
            {
                name: 'account_status',
                display_name: '账号状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '锁定', value: 'LOCKED'},
                        {label: '禁用', value: 'DISABLED'},
                        {label: '过期', value: 'EXPIRED'},
                    ],
                },
            },
            {
                name: 'last_login_time',
                display_name: '最后登录时间',
                type: 'DATE_TIME',
                flags: ['NULLABLE'],
            },
            {
                name: 'last_login_ip',
                display_name: '最后登录IP',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'login_count',
                display_name: '登录次数',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: 'remark',
                display_name: '备注',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },

            // 系统字段
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'owner',
                display_name: '直属上级',
                field: 'owner_id',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'subordinates',
                display_name: '下属',
                field: 'owner_id',
                entity: 'employee',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'dept',
                display_name: '所属部门',
                field: 'dept_id',
                entity: 'department',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'org',
                display_name: '所属机构',
                field: 'org_id',
                entity: 'organization',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
            {
                fields: ['username'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_user_code',
                fields: ['code'],
            },
            {
                name: 'idx_user_name',
                fields: ['name'],
            },
            {
                name: 'idx_user_username',
                fields: ['username'],
            },
            {
                name: 'idx_user_phone',
                fields: ['phone'],
            },
            {
                name: 'idx_user_email',
                fields: ['email'],
            },
            {
                name: 'idx_user_dept_id',
                fields: ['dept_id'],
            },
            {
                name: 'idx_user_org_id',
                fields: ['org_id'],
            },
            {
                name: 'idx_user_owner_id',
                fields: ['owner_id'],
            },
            {
                name: 'idx_user_employee_status',
                fields: ['employee_status'],
            },
            {
                name: 'idx_user_account_status',
                fields: ['account_status'],
            },
        ],
    },
}
