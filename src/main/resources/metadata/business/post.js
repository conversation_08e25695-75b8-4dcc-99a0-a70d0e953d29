export default {
    metadata: {
        name: 'post',
        display_name: '岗位',
        module: 'base',
        description: '企业组织架构中的岗位信息',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '岗位编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9]{2,20}$',
                        message: '岗位编号必须为2-20位大写字母和数字',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '岗位名称不能为空',
                    },
                ],
            },
            {
                name: 'post_type',
                display_name: '岗位类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '管理岗', value: 'MANAGEMENT'},
                        {label: '技术岗', value: 'TECHNICAL'},
                        {label: '业务岗', value: 'BUSINESS'},
                        {label: '行政岗', value: 'ADMINISTRATIVE'},
                        {label: '财务岗', value: 'FINANCE'},
                        {label: '人力资源岗', value: 'HR'},
                        {label: '其他', value: 'OTHER'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                    ],
                },
            },
            {
                name: 'dept_id',
                display_name: '所属部门',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'org_id',
                display_name: '所属机构',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'job_level_id',
                display_name: '职级',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'description',
                display_name: '岗位描述',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'responsibilities',
                display_name: '岗位职责',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'requirements',
                display_name: '任职要求',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'sort_order',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'dept',
                display_name: '所属部门',
                field: 'dept_id',
                entity: 'department',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'org',
                display_name: '所属机构',
                field: 'org_id',
                entity: 'organization',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'job_level',
                display_name: '职级',
                field: 'job_level_id',
                entity: 'job_level',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'users',
                display_name: '用户列表',
                field: 'post_id',
                entity: 'employee',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_post_code',
                fields: ['code'],
            },
            {
                name: 'idx_post_name',
                fields: ['name'],
            },
            {
                name: 'idx_post_dept_id',
                fields: ['dept_id'],
            },
            {
                name: 'idx_post_org_id',
                fields: ['org_id'],
            },
            {
                name: 'idx_post_job_level_id',
                fields: ['job_level_id'],
            },
            {
                name: 'idx_post_status',
                fields: ['status'],
            },
        ],
    },
}
