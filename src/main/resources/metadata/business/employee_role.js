export default {
    metadata: {
        name: 'employee_role',
        display_name: '雇员角色',
        module: 'base',
        description: '雇员与角色的多对多关联关系',
        fields: [
            {
                name: 'employee_id',
                display_name: '用户ID',
                type: 'VARCHAR',
                flags: [],
                validation: [
                    {
                        type: 'required',
                        message: '用户ID不能为空',
                    },
                ],
            },
            {
                name: 'role_id',
                display_name: '角色ID',
                type: 'VARCHAR',
                flags: [],
                validation: [
                    {
                        type: 'required',
                        message: '角色ID不能为空',
                    },
                ],
            },
            {
                name: 'grant_type',
                display_name: '授权类型',
                type: 'VARCHAR',
                flags: [],
                default_value: 'DIRECT',
                options: {
                    values: [
                        {label: '直接授权', value: 'DIRECT'},
                        {label: '继承授权', value: 'INHERITED'},
                        {label: '临时授权', value: 'TEMPORARY'},
                    ],
                },
            },
            {
                name: 'grant_by',
                display_name: '授权人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'grant_time',
                display_name: '授权时间',
                type: 'DATE_TIME',
                flags: ['NULLABLE'],
            },
            {
                name: 'expire_time',
                display_name: '过期时间',
                type: 'DATE_TIME',
                flags: ['NULLABLE'],
                description: '临时授权的过期时间，为空表示永久有效',
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                        {label: '过期', value: 'EXPIRED'},
                    ],
                },
            },
            {
                name: 'remark',
                display_name: '备注',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'employee',
                display_name: '雇员',
                field: 'employee_id',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'role',
                display_name: '角色',
                field: 'role_id',
                entity: 'role',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'granter',
                display_name: '授权人',
                field: 'grant_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['employee_id', 'role_id'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_user_role_user_id',
                fields: ['employee_id'],
            },
            {
                name: 'idx_user_role_role_id',
                fields: ['role_id'],
            },
            {
                name: 'idx_user_role_status',
                fields: ['status'],
            },
            {
                name: 'idx_user_role_grant_type',
                fields: ['grant_type'],
            },
            {
                name: 'idx_user_role_expire_time',
                fields: ['expire_time'],
            },
        ],
    },
}
