export default {
    metadata: {
        name: 'department',
        display_name: '部门',
        module: 'base',
        description: '企业组织架构中的部门信息',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '部门编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9]{2,20}$',
                        message: '部门编号必须为2-20位大写字母和数字',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '部门名称不能为空',
                    },
                ],
            },
            {
                name: 'short_name',
                display_name: '简称',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'parent_id',
                display_name: '上级部门',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'path',
                display_name: '部门路径',
                type: 'VARCHAR',
                flags: ['NULLABLE', 'INTERNAL'],
                description: '存储部门层级路径，格式为逗号分隔的ID列表',
            },
            {
                name: 'level',
                display_name: '部门层级',
                type: 'INT',
                flags: ['NULLABLE'],
                description: '部门层级深度，从1开始',
            },
            {
                name: 'dept_type',
                display_name: '部门类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '管理部门', value: 'MANAGEMENT'},
                        {label: '业务部门', value: 'BUSINESS'},
                        {label: '技术部门', value: 'TECHNICAL'},
                        {label: '财务部门', value: 'FINANCE'},
                        {label: '人力资源', value: 'HR'},
                        {label: '行政部门', value: 'ADMIN'},
                        {label: '市场部门', value: 'MARKETING'},
                        {label: '销售部门', value: 'SALES'},
                        {label: '研发部门', value: 'RD'},
                        {label: '其他', value: 'OTHER'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                        {label: '筹建', value: 'PREPARING'},
                        {label: '撤销', value: 'DISBANDED'},
                    ],
                },
            },
            {
                name: 'function',
                display_name: '部门职能',
                type: 'TEXT',
                flags: ['NULLABLE'],
                description: '部门主要职责和职能描述',
            },
            {
                name: 'order_no',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
                description: '用于同级部门排序',
            },
            {
                name: 'owner_id',
                display_name: '负责人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'deputy_owner_id',
                display_name: '副负责人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'org_id',
                display_name: '所属机构',
                type: 'VARCHAR',
                flags: [],
            },
            {
                name: 'cost_center',
                display_name: '成本中心',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'phone',
                display_name: '联系电话',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'email',
                display_name: '部门邮箱',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'address',
                display_name: '办公地点',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'remark',
                display_name: '备注',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'parent',
                display_name: '上级部门',
                field: 'parent_id',
                entity: 'department',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'children',
                display_name: '下级部门',
                field: 'parent_id',
                entity: 'department',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'owner',
                display_name: '负责人',
                field: 'owner_id',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'deputy_owner',
                display_name: '副负责人',
                field: 'deputy_owner_id',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'org',
                display_name: '所属机构',
                field: 'org_id',
                entity: 'organization',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'users',
                display_name: '部门成员',
                field: 'dept_id',
                entity: 'employee',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_dept_parent_id',
                fields: ['parent_id'],
            },
            {
                name: 'idx_dept_path',
                fields: ['path'],
            },
            {
                name: 'idx_dept_org_id',
                fields: ['org_id'],
            },
            {
                name: 'idx_dept_type',
                fields: ['dept_type'],
            },
            {
                name: 'idx_dept_status',
                fields: ['status'],
            },
            {
                name: 'idx_dept_owner_id',
                fields: ['owner_id'],
            },
        ],
    },
}
