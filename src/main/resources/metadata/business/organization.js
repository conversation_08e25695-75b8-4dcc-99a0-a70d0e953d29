export default {
    metadata: {
        name: 'organization',
        display_name: '机构',
        module: 'base',
        description: '企业组织机构信息',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '机构编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9]{3,20}$',
                        message: '机构编号必须为3-20位大写字母和数字',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '机构名称不能为空',
                    },
                ],
            },
            {
                name: 'short_name',
                display_name: '简称',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'parent_id',
                display_name: '上级机构',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'path',
                display_name: '组织路径',
                type: 'VARCHAR',
                flags: ['NULLABLE', 'INTERNAL'],
                description: '存储组织层级路径，格式为逗号分隔的ID列表',
            },
            {
                name: 'level',
                display_name: '组织层级',
                type: 'INT',
                flags: ['NULLABLE'],
                description: '组织层级深度，从1开始',
            },
            {
                name: 'org_type',
                display_name: '机构类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '集团', value: 'GROUP'},
                        {label: '公司', value: 'COMPANY'},
                        {label: '分公司', value: 'BRANCH'},
                        {label: '子公司', value: 'SUBSIDIARY'},
                        {label: '事业部', value: 'DIVISION'},
                        {label: '办事处', value: 'OFFICE'},
                        {label: '其他', value: 'OTHER'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                        {label: '筹建', value: 'PREPARING'},
                        {label: '关闭', value: 'CLOSED'},
                    ],
                },
            },
            {
                name: 'region_province',
                display_name: '所在省份',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'region_city',
                display_name: '所在城市',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'region_district',
                display_name: '所在区县',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'address',
                display_name: '详细地址',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'postal_code',
                display_name: '邮政编码',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'introduction',
                display_name: '简介',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'business_scope',
                display_name: '经营范围',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'legal_person',
                display_name: '法人代表',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'contact_person',
                display_name: '联系人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'contact_phone',
                display_name: '联系电话',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'contact_email',
                display_name: '联系邮箱',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'website',
                display_name: '官方网站',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'logo',
                display_name: '机构Logo',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'founded_date',
                display_name: '成立日期',
                type: 'DATE',
                flags: ['NULLABLE'],
            },
            {
                name: 'owner_id',
                display_name: '负责人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'sort_order',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'parent',
                display_name: '上级机构',
                field: 'parent_id',
                entity: 'organization',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'children',
                display_name: '下级机构',
                field: 'parent_id',
                entity: 'organization',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'departments',
                display_name: '部门列表',
                field: 'org_id',
                entity: 'department',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'users',
                display_name: '用户列表',
                field: 'org_id',
                entity: 'employee',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'owner',
                display_name: '负责人',
                field: 'owner_id',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_org_parent_id',
                fields: ['parent_id'],
            },
            {
                name: 'idx_org_path',
                fields: ['path'],
            },
            {
                name: 'idx_org_type',
                fields: ['org_type'],
            },
            {
                name: 'idx_org_status',
                fields: ['status'],
            },
        ],
    },
}
