export default {
    metadata: {
        name: 'permission',
        display_name: '权限',
        module: 'base',
        description: '系统权限定义',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '权限编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9_:]{2,50}$',
                        message: '权限编号必须为2-50位大写字母、数字、下划线或冒号',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '权限名称不能为空',
                    },
                ],
            },
            {
                name: 'permission_type',
                display_name: '权限类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '菜单权限', value: 'MENU'},
                        {label: '操作权限', value: 'OPERATION'},
                        {label: '数据权限', value: 'DATA'},
                        {label: 'API权限', value: 'API'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                    ],
                },
            },
            {
                name: 'description',
                display_name: '描述',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'parent_id',
                display_name: '父级权限',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'menu_id',
                display_name: '关联菜单',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '关联的菜单ID，仅当permission_type为MENU或OPERATION时有效',
            },
            {
                name: 'api_path',
                display_name: 'API路径',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '关联的API路径，仅当permission_type为API时有效',
            },
            {
                name: 'api_method',
                display_name: 'API方法',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: 'GET', value: 'GET'},
                        {label: 'POST', value: 'POST'},
                        {label: 'PUT', value: 'PUT'},
                        {label: 'DELETE', value: 'DELETE'},
                        {label: 'PATCH', value: 'PATCH'},
                        {label: 'ALL', value: 'ALL'},
                    ],
                },
                description: '关联的API方法，仅当permission_type为API时有效',
            },
            {
                name: 'entity_name',
                display_name: '实体名称',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '关联的实体名称，仅当permission_type为DATA时有效',
            },
            {
                name: 'operation_code',
                display_name: '操作代码',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                description: '操作代码，如add、edit、delete等，仅当permission_type为OPERATION时有效',
            },
            {
                name: 'sort_order',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'parent',
                display_name: '父级权限',
                field: 'parent_id',
                entity: 'permission',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'children',
                display_name: '子级权限',
                field: 'parent_id',
                entity: 'permission',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            // {
            //   name: 'menu',
            //   display_name: '关联菜单',
            //   field: 'menu_id',
            //   entity: 'menu',
            //   module: 'base',
            //   type: 'ONE',
            // },
            {
                name: 'roles',
                display_name: '角色列表',
                field: 'permission_id',
                entity: 'role_permission',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_permission_code',
                fields: ['code'],
            },
            {
                name: 'idx_permission_name',
                fields: ['name'],
            },
            {
                name: 'idx_permission_type',
                fields: ['permission_type'],
            },
            {
                name: 'idx_permission_status',
                fields: ['status'],
            },
            {
                name: 'idx_permission_parent_id',
                fields: ['parent_id'],
            },
            {
                name: 'idx_permission_menu_id',
                fields: ['menu_id'],
            },
            {
                name: 'idx_permission_entity_name',
                fields: ['entity_name'],
            },
        ],
    },
}
