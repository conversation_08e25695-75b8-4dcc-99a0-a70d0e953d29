export default {
    metadata: {
        name: 'role_permission',
        display_name: '角色权限',
        module: 'base',
        description: '角色与权限的多对多关联关系',
        fields: [
            {
                name: 'role_id',
                display_name: '角色ID',
                type: 'VARCHAR',
                flags: [],
                validation: [
                    {
                        type: 'required',
                        message: '角色ID不能为空',
                    },
                ],
            },
            {
                name: 'permission_id',
                display_name: '权限ID',
                type: 'VARCHAR',
                flags: [],
                validation: [
                    {
                        type: 'required',
                        message: '权限ID不能为空',
                    },
                ],
            },
            {
                name: 'permission_type',
                display_name: '权限类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '菜单权限', value: 'MENU'},
                        {label: '操作权限', value: 'OPERATION'},
                        {label: '数据权限', value: 'DATA'},
                        {label: 'API权限', value: 'API'},
                    ],
                },
            },
            {
                name: 'data_scope',
                display_name: '数据范围',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
                options: {
                    values: [
                        {label: '全部数据', value: 'ALL'},
                        {label: '本部门数据', value: 'DEPT'},
                        {label: '本部门及下级数据', value: 'DEPT_AND_CHILD'},
                        {label: '仅本人数据', value: 'SELF'},
                        {label: '自定义数据', value: 'CUSTOM'},
                    ],
                },
                description: '数据权限的范围，仅当permission_type为DATA时有效',
            },
            {
                name: 'data_scope_custom',
                display_name: '自定义数据范围',
                type: 'TEXT',
                flags: ['NULLABLE'],
                description: '自定义数据范围的JSON配置，仅当data_scope为CUSTOM时有效',
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                    ],
                },
            },
            {
                name: 'remark',
                display_name: '备注',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'role',
                display_name: '角色',
                field: 'role_id',
                entity: 'role',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'permission',
                display_name: '权限',
                field: 'permission_id',
                entity: 'permission',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['role_id', 'permission_id'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_role_permission_role_id',
                fields: ['role_id'],
            },
            {
                name: 'idx_role_permission_permission_id',
                fields: ['permission_id'],
            },
            {
                name: 'idx_role_permission_type',
                fields: ['permission_type'],
            },
            {
                name: 'idx_role_permission_status',
                fields: ['status'],
            },
        ],
    },
}
