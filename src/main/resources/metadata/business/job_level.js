export default {
    metadata: {
        name: 'job_level',
        display_name: '职级',
        module: 'base',
        description: '企业职级体系定义',
        fields: [
            {
                name: 'code',
                display_name: '编号',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '职级编号不能为空',
                    },
                    {
                        type: 'pattern',
                        pattern: '^[A-Z0-9]{1,10}$',
                        message: '职级编号必须为1-10位大写字母和数字',
                    },
                ],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                flags: ['KEY_INFO'],
                validation: [
                    {
                        type: 'required',
                        message: '职级名称不能为空',
                    },
                ],
            },
            {
                name: 'level_value',
                display_name: '职级值',
                type: 'INT',
                flags: [],
                description: '职级数值，用于排序和比较',
            },
            {
                name: 'level_type',
                display_name: '职级类型',
                type: 'VARCHAR',
                flags: [],
                options: {
                    values: [
                        {label: '管理序列', value: 'MANAGEMENT'},
                        {label: '专业序列', value: 'PROFESSIONAL'},
                        {label: '技术序列', value: 'TECHNICAL'},
                        {label: '营销序列', value: 'MARKETING'},
                        {label: '通用序列', value: 'GENERAL'},
                    ],
                },
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                flags: [],
                default_value: 'ACTIVE',
                options: {
                    values: [
                        {label: '正常', value: 'ACTIVE'},
                        {label: '停用', value: 'INACTIVE'},
                    ],
                },
            },
            {
                name: 'description',
                display_name: '描述',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'requirements',
                display_name: '任职要求',
                type: 'TEXT',
                flags: ['NULLABLE'],
            },
            {
                name: 'salary_min',
                display_name: '薪资下限',
                type: 'DECIMAL',
                flags: ['NULLABLE', 'INTERNAL'],
            },
            {
                name: 'salary_max',
                display_name: '薪资上限',
                type: 'DECIMAL',
                flags: ['NULLABLE', 'INTERNAL'],
            },
            {
                name: 'parent_id',
                display_name: '上级职级',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: 'sort_order',
                display_name: '排序号',
                type: 'INT',
                flags: ['NULLABLE'],
                default_value: 0,
            },
            {
                name: '_id',
                display_name: '主键',
                type: 'VARCHAR',
                flags: ['PRIMARY'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                flags: [],
            },
        ],
        relations: [
            {
                name: 'parent',
                display_name: '上级职级',
                field: 'parent_id',
                entity: 'job_level',
                module: 'base',
                type: 'ONE',
            },
            {
                name: 'children',
                display_name: '下级职级',
                field: 'parent_id',
                entity: 'job_level',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: 'posts',
                display_name: '岗位列表',
                field: 'job_level_id',
                entity: 'post',
                module: 'base',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'employee',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_job_level_code',
                fields: ['code'],
            },
            {
                name: 'idx_job_level_name',
                fields: ['name'],
            },
            {
                name: 'idx_job_level_value',
                fields: ['level_value'],
            },
            {
                name: 'idx_job_level_type',
                fields: ['level_type'],
            },
            {
                name: 'idx_job_level_parent_id',
                fields: ['parent_id'],
            },
            {
                name: 'idx_job_level_status',
                fields: ['status'],
            },
        ],
    },
}
