package com.edc.vibe_engine.engine.interfaces;

import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.interfaces.IRule;
import com.edc.vibe_engine.record.interfaces.IRecord;

public interface IRuleProcess<T extends IRule> {

    Class<T> support();

    default boolean support(Class<?> clazz) {
        return support().equals(clazz);
    }

    void process(DefEntity entity, IRecord data, T rule);
}
