package com.edc.vibe_engine.engine.interfaces;

import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.extension.ExtensionContext;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.interfaces.IRecord;

public interface IMutationExtension extends IExtension<IRecord> {


    @Override
    default Object execute(ExtensionContext<IRecord> context) throws Exception {
        final IRecord payload = context.payload();
        switch (context.point()) {
            case EngineConstants.PRE_INSERT -> {
                preInsert(context, context.entity(), payload);
                preSave(context, context.entity(), payload);
            }
            case EngineConstants.PRE_UPDATE -> {
                preUpdate(context, context.entity(), payload);
                preSave(context, context.entity(), payload);
            }
            case EngineConstants.POST_INSERT -> {
                postInsert(context, context.entity(), payload);
                postSave(context, context.entity(), payload);
            }
            case EngineConstants.POST_UPDATE -> {
                postUpdate(context, context.entity(), payload);
                postSave(context, context.entity(), payload);
            }
            case EngineConstants.READY_INSERT -> {
                readyInsert(context, context.entity(), payload);
                readySave(context, context.entity(), payload);
            }
            case EngineConstants.READY_UPDATE -> {
                readyUpdate(context, context.entity(), payload);
                readySave(context, context.entity(), payload);
            }
            case EngineConstants.COMMIT_INSERT -> {
                commitInsert(context, context.entity(), payload);
                commitSave(context, context.entity(), payload);
            }
            case EngineConstants.COMMIT_UPDATE -> {
                commitUpdate(context, context.entity(), payload);
                commitSave(context, context.entity(), payload);
            }
            case EngineConstants.PRE_DELETE -> preDelete(context, context.entity(), payload);
            case EngineConstants.READY_DELETE -> readyDelete(context, context.entity(), payload);
            case EngineConstants.POST_DELETE -> postDelete(context, context.entity(), payload);
            case EngineConstants.COMMIT_DELETE -> commitDelete(context, context.entity(), payload);
            default -> {
            }
        }
        return null;
    }


    default void preInsert(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void preUpdate(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void preSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void postInsert(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void readyInsert(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void readyUpdate(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void readySave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void postUpdate(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void postSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void commitInsert(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void commitUpdate(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void commitSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }


    default void preDelete(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void readyDelete(ExtensionContext<IRecord> context, DefEntity entity, IRecord payload) {

    }

    default void postDelete(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }

    default void commitDelete(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
    }
}
