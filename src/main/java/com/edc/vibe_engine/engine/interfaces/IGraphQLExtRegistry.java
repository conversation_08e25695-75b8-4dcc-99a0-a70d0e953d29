package com.edc.vibe_engine.engine.interfaces;

import com.edc.vibe_engine.meta.def.DefEntity;
import graphql.schema.GraphQLCodeRegistry;
import graphql.schema.GraphQLObjectType;

public interface IGraphQLExtRegistry {

    boolean support(DefEntity entity);

    default void registryQuery(DefEntity entity, GraphQLObjectType.Builder query, GraphQLCodeRegistry.Builder codeRegistry) {
    }

    default void registryMutation(DefEntity entity, GraphQLObjectType.Builder mutation, GraphQLCodeRegistry.Builder codeRegistry) {
    }
}
