package com.edc.vibe_engine.engine.constants;

public class EngineConstants {

    public static final String DEFAULT_EXTENSION_ENTITY = "__DEFAULT__";


    public static final String PRE_INSERT = "preInsert";
    public static final String READY_INSERT = "readyInsert";
    public static final String POST_INSERT = "postInsert";
    public static final String PRE_UPDATE = "preUpdate";
    public static final String READY_UPDATE = "readyUpdate";
    public static final String POST_UPDATE = "postUpdate";
    public static final String COMMIT_INSERT = "commitInsert";
    public static final String COMMIT_UPDATE = "commitUpdate";
    public static final String PRE_DELETE = "preDelete";
    public static final String READY_DELETE = "readyDelete";
    public static final String POST_DELETE = "postDelete";
    public static final String COMMIT_DELETE = "commitDelete";
    public static final String PRE_QUERY = "preQuery";
    public static final String POST_QUERY = "postQuery";
    public static final String PRE_QUERY_COUNT = "preQueryCount";
    public static final String POST_QUERY_COUNT = "postQueryCount";
    public static final String PRE_QUERY_PAGE = "preQueryPage";
    public static final String POST_QUERY_PAGE = "postQueryPage";
    public static final String PRE_QUERY_PAGE_COUNT = "preQueryPageCount";
    public static final String POST_QUERY_PAGE_COUNT = "postQueryPageCount";
    public static final String PRE_QUERY_PAGE_LIST = "preQueryPageList";
    public static final String POST_QUERY_PAGE_LIST = "postQueryPageList";

    public final static String MUTATION = "Mutation";
    public final static String QUERY = "Query";
}
