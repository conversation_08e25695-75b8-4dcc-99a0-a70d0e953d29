package com.edc.vibe_engine.engine.extension;

import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.interfaces.IExtension;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@SuppressWarnings({"rawtypes", "unchecked"})
public class ExtensionManager {

    private final Map<String, List<IExtension<?>>> extensionsMap = new HashMap<>();

    public ExtensionManager(List<IExtension<?>> extensions) {
        if (CollectionUtils.isEmpty(extensions)) {
            return;
        }
        for (IExtension extension : extensions) {
            String extensionEntity = extension.supportEntity();
            List<IExtension<?>> list = extensionsMap.computeIfAbsent(extensionEntity, k -> new ArrayList<>());
            list.add(extension);
        }
        extensionsMap.forEach((entityName, list) -> {
            list.sort(Comparator.comparingInt(IExtension::getOrder));
        });
    }


    public List<Object> executeExtensions(ExtensionContext context) {
        return executeExtensions(context, false);
    }


    public List<Object> executeExtensionsReverse(ExtensionContext context) {
        return executeExtensions(context, true);
    }

    public List<Object> executeExtensions(ExtensionContext context, boolean isReverse) {
        final String entityName = context.entity().getName();
        final List<IExtension<?>> extensions = new ArrayList<>(extensionsMap.getOrDefault(entityName, List.of()));
        extensions.addAll(extensionsMap.getOrDefault(EngineConstants.DEFAULT_EXTENSION_ENTITY, List.of()));
        if (isReverse) {
            return doExecuteExtensions(context, extensions.reversed());
        } else {
            return doExecuteExtensions(context, extensions);
        }
    }

    private static List<Object> doExecuteExtensions(ExtensionContext context, List<IExtension<?>> extensions) {
        List<Object> results = new ArrayList<>();
        for (IExtension extension : extensions) {
            try {
                results.add(extension.execute(context));
            } catch (Exception e) {
                log.error("execute extension error", e);
                throw new RuntimeException(e);
            }
        }
        return results;
    }
}
