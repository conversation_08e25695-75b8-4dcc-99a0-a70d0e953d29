package com.edc.vibe_engine.engine.extension.builtin;

import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.extension.ExtensionContext;
import com.edc.vibe_engine.engine.interfaces.IMutationExtension;
import com.edc.vibe_engine.engine.rule.RuleValidator;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.interfaces.IRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class BuiltInMutationExtension implements IMutationExtension {
    private final RuleValidator ruleValidator;


    @Override
    public String supportEntity() {
        return EngineConstants.DEFAULT_EXTENSION_ENTITY;
    }

    @Override
    public int getOrder() {
        return 9999;
    }

    @Override
    public void preSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
        ruleValidator.validate(entity, data);
    }
}
