package com.edc.vibe_engine.engine.router;

import com.edc.vibe_engine.engine.graphql.GraphQlFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.GraphQlSource;
import org.springframework.graphql.server.WebGraphQlHandler;
import org.springframework.graphql.server.webmvc.GraphQlHttpHandler;
import org.springframework.graphql.server.webmvc.GraphiQlHandler;
import org.springframework.graphql.server.webmvc.SchemaHandler;
import org.springframework.web.servlet.function.RequestPredicates;
import org.springframework.web.servlet.function.RouterFunction;
import org.springframework.web.servlet.function.RouterFunctions;
import org.springframework.web.servlet.function.ServerResponse;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class GraphqlRouting {

    private final GraphQlFactory graphQlFactory;

    @Bean
    public RouterFunction<ServerResponse> graphqlRouters() {
        RouterFunctions.Builder builder = RouterFunctions.route();
        builder.route(RequestPredicates.POST("/{module}/graphql"), request -> {
                            String module = request.pathVariable("module");

                            final WebGraphQlHandler webGraphQlHandler = this.graphQlFactory.getWebHandler(module);

                            final GraphQlHttpHandler graphQlHttpHandler = new GraphQlHttpHandler(webGraphQlHandler);
                            return graphQlHttpHandler.handleRequest(request);
                        }
                )
                .route(RequestPredicates.GET("/{module}/graphiql"), request -> {
                    String module = request.pathVariable("module");
                    GraphiQlHandler graphiQLHandler = new GraphiQlHandler("/%s/graphql".formatted(module), "");
                    return graphiQLHandler.handleRequest(request);
                })
                .route(RequestPredicates.GET("/{module}/schema"), request -> {
                    String module = request.pathVariable("module");
                    final GraphQlSource graphQlSource = this.graphQlFactory.getSource(module);
                    SchemaHandler schemaHandler = new SchemaHandler(graphQlSource);
                    return schemaHandler.handleRequest(request);
                });
        return builder.build();
    }


}
