package com.edc.vibe_engine.engine.rule;

import com.edc.vibe_engine.common.exception.ValidationException;
import com.edc.vibe_engine.engine.interfaces.IRuleProcess;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.validation.AsyncValidationRule;
import com.edc.vibe_engine.meta.validation.CompositeValidationRule;
import com.edc.vibe_engine.meta.validation.CustomValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRuleType;
import com.edc.vibe_engine.meta.validation.ValidationRuleWrapper;
import com.edc.vibe_engine.record.interfaces.IRecord;
import com.edc.vibe_engine.script.JsExpressionEvaluator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 验证规则处理器，用于在保存实体数据前执行验证规则
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ValidationRuleProcess implements IRuleProcess<ValidationRuleWrapper> {

    private final JsExpressionEvaluator expressionEvaluator;

    @Override
    public Class<ValidationRuleWrapper> support() {
        return ValidationRuleWrapper.class;
    }

    @Override
    public void process(DefEntity entity, IRecord data, ValidationRuleWrapper rule) {
        List<ValidationException.ValidationError> errors = new ArrayList<>();

        // 处理实体级别的验证规则
        if (rule.getRules() != null && !rule.getRules().isEmpty()) {
            for (Map.Entry<String, List<ValidationRule>> entry : rule.getRules().entrySet()) {
                for (ValidationRule validationRule : entry.getValue()) {
                    final String fieldName = entry.getKey();
                    Object value = data.get(fieldName);
                    validateRule(fieldName, value, data, validationRule, errors);
                }
            }
        }

        // 如果有错误，抛出验证异常
        if (!errors.isEmpty()) {
            ValidationException exception = new ValidationException("验证失败");
            exception.addErrors(errors);
            throw exception;
        }
    }

    /**
     * 验证单个规则
     *
     * @param fieldName      字段名
     * @param value          字段值
     * @param data           记录数据
     * @param validationRule 验证规则
     * @param errors         错误列表
     */
    private void validateRule(String fieldName, Object value, IRecord data, ValidationRule validationRule, List<ValidationException.ValidationError> errors) {
        // 检查条件
        if (validationRule.getCondition() != null && !validationRule.getCondition().isEmpty()) {
            boolean conditionMet = expressionEvaluator.evalExpression(validationRule.getCondition(), value, data);
            if (!conditionMet) {
                return; // 条件不满足，跳过此规则
            }
        }

        // 根据规则类型执行验证
        ValidationRuleType ruleType = validationRule.getType();
        if (ruleType == null) {
            return; // 无效的规则类型
        }

        String errorMessage = null;

        switch (ruleType) {
            case REQUIRED:
                errorMessage = validateRequired(value, validationRule);
                break;
            case MIN:
                errorMessage = validateMin(value, validationRule);
                break;
            case MAX:
                errorMessage = validateMax(value, validationRule);
                break;
            case PATTERN:
                errorMessage = validatePattern(value, validationRule);
                break;
            case EMAIL:
                errorMessage = validateEmail(value, validationRule);
                break;
            case URL:
                errorMessage = validateUrl(value, validationRule);
                break;
            case CUSTOM:
                errorMessage = validateCustom(value, data, validationRule);
                break;
            case ONE_OF:
            case ALL_OF:
                errorMessage = validateComposite(fieldName, value, data, validationRule, errors);
                break;
            case ASYNC:
                errorMessage = validateAsync(value, data, validationRule);
                break;
            default:
                log.warn("Unsupported validation rule type: {}", ruleType);
                break;
        }

        // 如果验证失败，添加错误
        if (errorMessage != null) {
            errors.add(new ValidationException.ValidationError(fieldName, errorMessage));
        }
    }

    /**
     * 验证必填规则
     */
    private String validateRequired(Object value, ValidationRule rule) {
        if (value == null || (value instanceof String && ((String) value).isEmpty())) {
            return rule.getMessage() != null ? rule.getMessage() : "此字段不能为空";
        }
        return null;
    }

    /**
     * 验证最小值/最小长度规则
     */
    private String validateMin(Object value, ValidationRule rule) {
        if (value == null) {
            return null; // 空值不验证
        }

        Map<String, Object> params = rule.getParamsMap();
        if (params == null || !params.containsKey("min")) {
            return null; // 无效的参数
        }

        Object minValue = params.get("min");
        if (minValue == null) {
            return null;
        }

        if (value instanceof Number && minValue instanceof Number) {
            double numValue = ((Number) value).doubleValue();
            double minNumValue = ((Number) minValue).doubleValue();
            if (numValue < minNumValue) {
                return rule.getMessage() != null ? rule.getMessage() : "值不能小于 " + minNumValue;
            }
        } else if (value instanceof String && minValue instanceof Number) {
            int length = ((String) value).length();
            int minLength = ((Number) minValue).intValue();
            if (length < minLength) {
                return rule.getMessage() != null ? rule.getMessage() : "长度不能小于 " + minLength;
            }
        }

        return null;
    }

    /**
     * 验证最大值/最大长度规则
     */
    private String validateMax(Object value, ValidationRule rule) {
        if (value == null) {
            return null; // 空值不验证
        }

        Map<String, Object> params = rule.getParamsMap();
        if (params == null || !params.containsKey("max")) {
            return null; // 无效的参数
        }

        Object maxValue = params.get("max");
        if (maxValue == null) {
            return null;
        }

        if (value instanceof Number && maxValue instanceof Number) {
            double numValue = ((Number) value).doubleValue();
            double maxNumValue = ((Number) maxValue).doubleValue();
            if (numValue > maxNumValue) {
                return rule.getMessage() != null ? rule.getMessage() : "值不能大于 " + maxNumValue;
            }
        } else if (value instanceof String && maxValue instanceof Number) {
            int length = ((String) value).length();
            int maxLength = ((Number) maxValue).intValue();
            if (length > maxLength) {
                return rule.getMessage() != null ? rule.getMessage() : "长度不能大于 " + maxLength;
            }
        }

        return null;
    }

    /**
     * 验证正则表达式规则
     */
    private String validatePattern(Object value, ValidationRule rule) {
        if (value == null || !(value instanceof String)) {
            return null; // 空值或非字符串不验证
        }

        Map<String, Object> params = rule.getParamsMap();
        if (params == null || !params.containsKey("pattern")) {
            return null; // 无效的参数
        }

        Object patternObj = params.get("pattern");
        if (patternObj == null || !(patternObj instanceof String)) {
            return null;
        }

        String patternStr = (String) patternObj;
        try {
            Pattern pattern = Pattern.compile(patternStr);
            if (!pattern.matcher((String) value).matches()) {
                return rule.getMessage() != null ? rule.getMessage() : "格式不正确";
            }
        } catch (Exception e) {
            log.error("Invalid pattern: {}", patternStr, e);
            return "正则表达式格式错误";
        }

        return null;
    }

    /**
     * 验证电子邮件规则
     */
    private String validateEmail(Object value, ValidationRule rule) {
        if (value == null || !(value instanceof String)) {
            return null; // 空值或非字符串不验证
        }

        String email = (String) value;
        if (email.isEmpty()) {
            return null; // 空字符串不验证
        }

        // 简单的电子邮件格式验证
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        if (!Pattern.matches(emailPattern, email)) {
            return rule.getMessage() != null ? rule.getMessage() : "电子邮件格式不正确";
        }

        return null;
    }

    /**
     * 验证URL规则
     */
    private String validateUrl(Object value, ValidationRule rule) {
        if (value == null || !(value instanceof String)) {
            return null; // 空值或非字符串不验证
        }

        String url = (String) value;
        if (url.isEmpty()) {
            return null; // 空字符串不验证
        }

        // 简单的URL格式验证
        String urlPattern = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
        if (!Pattern.matches(urlPattern, url)) {
            return rule.getMessage() != null ? rule.getMessage() : "URL格式不正确";
        }

        return null;
    }

    /**
     * 验证自定义规则
     */
    private String validateCustom(Object value, IRecord data, ValidationRule rule) {
        if (!(rule instanceof CustomValidationRule customRule)) {
            return null; // 不是自定义验证规则
        }

        String expression = customRule.getExpression();
        if (expression == null || expression.isEmpty()) {
            return null; // 无效的表达式
        }

        boolean isValid = expressionEvaluator.evalExpression(expression, value, data);
        if (!isValid) {
            return rule.getMessage() != null ? rule.getMessage() : "验证失败";
        }

        return null;
    }

    /**
     * 验证复合规则
     */
    private String validateComposite(String fieldName, Object value, IRecord data, ValidationRule rule, List<ValidationException.ValidationError> errors) {
        if (!(rule instanceof CompositeValidationRule)) {
            return null; // 不是复合验证规则
        }

        CompositeValidationRule compositeRule = (CompositeValidationRule) rule;
        List<ValidationRule> subRules = compositeRule.getRules();
        if (subRules == null || subRules.isEmpty()) {
            return null; // 无子规则
        }

        if (rule.getType() == ValidationRuleType.ALL_OF) {
            // 所有规则都必须满足
            List<ValidationException.ValidationError> subErrors = new ArrayList<>();
            for (ValidationRule subRule : subRules) {
                validateRule(fieldName, value, data, subRule, subErrors);
            }
            if (!subErrors.isEmpty()) {
                errors.addAll(subErrors);
                return rule.getMessage() != null ? rule.getMessage() : "验证失败";
            }
        } else if (rule.getType() == ValidationRuleType.ONE_OF) {
            // 至少一个规则满足
            List<ValidationException.ValidationError> subErrors = new ArrayList<>();
            for (ValidationRule subRule : subRules) {
                List<ValidationException.ValidationError> tempErrors = new ArrayList<>();
                validateRule(fieldName, value, data, subRule, tempErrors);
                if (tempErrors.isEmpty()) {
                    return null; // 有一个规则满足，验证通过
                }
                subErrors.addAll(tempErrors);
            }
            // 所有规则都不满足
            return rule.getMessage() != null ? rule.getMessage() : "验证失败";
        }

        return null;
    }

    /**
     * 验证异步规则（目前仅支持同步验证）
     */
    private String validateAsync(Object value, IRecord data, ValidationRule rule) {
        if (!(rule instanceof AsyncValidationRule)) {
            return null; // 不是异步验证规则
        }

        // 目前不支持异步验证，返回警告
        log.warn("Async validation is not supported yet");
        return null;
    }
}
