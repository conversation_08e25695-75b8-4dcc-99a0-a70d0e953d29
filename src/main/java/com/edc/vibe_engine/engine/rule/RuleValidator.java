package com.edc.vibe_engine.engine.rule;

import com.edc.vibe_engine.engine.interfaces.IRuleProcess;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.interfaces.IRule;
import com.edc.vibe_engine.record.interfaces.IRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@SuppressWarnings({"rawtypes", "unchecked"})
public class RuleValidator {

    private final List<IRuleProcess> ruleProcesses;


    public void validate(DefEntity entity, IRecord data) {
        ruleProcesses.forEach(process -> {
            final List<IRule> rules = entity.getRules();
            for (IRule rule : rules) {
                if (process.support(rule.getClass())) {
                    process.process(entity, data, rule);
                }
            }
        });
    }
}
