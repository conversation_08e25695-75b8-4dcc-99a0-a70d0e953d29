package com.edc.vibe_engine.engine.rule;

import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.engine.interfaces.IRuleProcess;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefUniqRule;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import com.edc.vibe_engine.record.interfaces.IRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class UniqRuleProcess implements IRuleProcess<DefUniqRule> {

    private final DataRecordAccess dataRecordAccess;

    @Override
    public Class<DefUniqRule> support() {
        return DefUniqRule.class;
    }

    @Override
    public void process(DefEntity entity, IRecord data, DefUniqRule rule) {
        final List<String> fields = rule.getFields();
        if (fields.isEmpty()) return;

        Condition condition = new Condition();
        boolean hasChanged = false;
        for (String field : fields) {
            if (data.hasChange(field)) {
                hasChanged = true;
            }

            final Object val = data.get(field);
            if (val == null) {
                condition.isNull(field);
            } else {
                condition.eq(field, val);
            }
        }
        if (!hasChanged) {
            return;
        }

        final DataRecord record = dataRecordAccess.queryOne(entity, condition);
        if (record == null)
            return;
        if (Objects.equals(record.get(MetaConstants.ID), data.get(MetaConstants.ID)))
            return;

        throw new RuntimeException("数据重复");
    }
}
