package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.common.support.AsyncUtils;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefRelation;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.script.JS;
import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;

public class AbsDataJoinFetcher {


    private final String entityName;
    private final String relationName;

    private final String targetEntityName;

    public AbsDataJoinFetcher(String entityName, String relationName, String targetEntityName) {
        this.entityName = entityName;
        this.relationName = relationName;
        this.targetEntityName = targetEntityName;
    }

    public <T> CompletableFuture<T> loadData(DataFetchingEnvironment environment, BiFunction<DefEntity, Condition, T> function) {
        // 获取源对象
        DataRecord source = environment.getSource();
        assert source != null;
        final DefEntity defEntity = MetaManager.get(entityName);
        final Optional<DefRelation> opt = defEntity.getRelations().stream()
                .filter(defRelation -> Objects.equals(defRelation.getName(), relationName)).findAny();
        if (opt.isEmpty())
            return CompletableFuture.completedFuture(null);
        final DefRelation relation = opt.get();
        final List<DefRelation.JoinMapping> joinMappings = relation.getJoinMappings();
        if (CollectionUtils.isEmpty(joinMappings))
            return CompletableFuture.completedFuture(null);

        final DefEntity targetEntity = MetaManager.get(targetEntityName);
        // 批量查询多个ID的数据
        Condition condition = new Condition();

        JS.withContext(Map.of("self", source, "condition", condition), ctx -> {
            for (DefRelation.JoinMapping mapping : joinMappings) {
                String mappingCondition = mapping.getConditionExp();
                if (StringUtils.isNotBlank(mappingCondition)) {
                    ctx.eval(JS.LANGUAGE_ID, mappingCondition);
                } else if (StringUtils.isNotBlank(mapping.getField()) && StringUtils.isNotBlank(mapping.getValExp())) {
                    final Object val = JS.toJava(ctx.eval(JS.LANGUAGE_ID, mapping.getValExp()));
                    condition.in(mapping.getField(), TypeCastUtils.castToList(val));
                }
            }
        });
        if (condition.getWhere().isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }

        return AsyncUtils.supplyAsync(() -> function.apply(targetEntity, condition));
    }
}
