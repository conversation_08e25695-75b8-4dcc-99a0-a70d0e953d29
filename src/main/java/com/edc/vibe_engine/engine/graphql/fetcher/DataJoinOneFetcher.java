package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;

import java.util.concurrent.CompletableFuture;

public class DataJoinOneFetcher extends AbsDataJoinFetcher implements DataFetcher<CompletableFuture<DataRecord>> {

    private final DataRecordAccess dataRecordAccess;

    public DataJoinOneFetcher(String entityName, String relationName, String targetEntityName, DataRecordAccess dataRecordAccess) {
        super(entityName, relationName, targetEntityName);
        this.dataRecordAccess = dataRecordAccess;
    }


    @Override
    public CompletableFuture<DataRecord> get(DataFetchingEnvironment environment) {
        return loadData(environment, dataRecordAccess::queryOne);
    }
}
