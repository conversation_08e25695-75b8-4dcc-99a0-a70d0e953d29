package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetchingEnvironment;
import org.dataloader.DataLoader;
import org.dataloader.DataLoaderFactory;
import org.dataloader.DataLoaderOptions;
import org.dataloader.DataLoaderRegistry;

public class AbsRelationDataFetcher {

    private final String targetEntityName;
    private final DataRecordAccess dataRecordAccess;

    public AbsRelationDataFetcher(String targetEntityName, DataRecordAccess dataRecordAccess) {
        this.targetEntityName = targetEntityName;
        this.dataRecordAccess = dataRecordAccess;
    }

    private static DataLoaderOptions initLoaderOptions() {
        return DataLoaderOptions.newOptions()
                //                .setBatchLoaderContextProvider(DataLoaderManager::collectContext)
                .setMaxBatchSize(1000);
    }


    protected DataLoader<String, DataRecord> getRelationDataLoader(DataFetchingEnvironment environment) {
        // 获取 DataLoader
        final String dataLoaderName = targetEntityName + "__REF_LOADER";
        final DataLoaderRegistry loaderRegistry = environment.getDataLoaderRegistry();
        return loaderRegistry.computeIfAbsent(dataLoaderName, name -> DataLoaderFactory.newMappedDataLoader(new DataMappedBatchLoader(targetEntityName, dataRecordAccess), initLoaderOptions()));
    }
}
