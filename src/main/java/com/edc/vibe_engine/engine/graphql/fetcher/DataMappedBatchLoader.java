package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.common.support.AsyncUtils;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import org.dataloader.BatchLoaderEnvironment;
import org.dataloader.MappedBatchLoaderWithContext;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletionStage;

public class DataMappedBatchLoader extends AbsDataBatchLoader implements MappedBatchLoaderWithContext<String, DataRecord> {

    public DataMappedBatchLoader(String entityName, DataRecordAccess dataRecordAccess) {
        super(entityName, dataRecordAccess);
    }

    @Override
    public CompletionStage<Map<String, DataRecord>> load(Set<String> set, BatchLoaderEnvironment environment) {
        return AsyncUtils.supplyAsync(() -> loadDataMap(set, environment));
    }


}
