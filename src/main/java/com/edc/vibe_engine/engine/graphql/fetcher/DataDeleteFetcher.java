package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.extension.ExtensionContext;
import com.edc.vibe_engine.engine.extension.ExtensionManager;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DataDeleteFetcher implements DataFetcher<Boolean> {

    private final DataRecordAccess dataRecordAccess;
    private final ExtensionManager extensionManager;
    private final String entityName;


    @Override
    public Boolean get(DataFetchingEnvironment environment) throws Exception {

        final DefEntity entity = MetaManager.get(entityName);
        final Object id = environment.getArgument(MetaConstants.ID);

        final DataRecord record = dataRecordAccess.queryOne(entity, new Condition().eq(MetaConstants.ID, id));

        extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.PRE_DELETE, record));

        final Integer deleted = dataRecordAccess.withTransaction(status -> {
            extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.READY_DELETE, record));
            final int delete = dataRecordAccess.delete(entity, record);
            extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.POST_DELETE, record));
            return delete;
        }, () -> extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.COMMIT_DELETE, record)));


        return deleted > 0;
    }
}
