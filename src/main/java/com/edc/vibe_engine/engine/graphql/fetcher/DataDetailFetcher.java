package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DataDetailFetcher implements DataFetcher<DataRecord> {

    private final DataRecordAccess dataRecordAccess;
    private final String entityName;



    @Override
    public DataRecord get(DataFetchingEnvironment environment) throws Exception {
        final DefEntity entity = MetaManager.get(entityName);
        final Object id = environment.getArgument(MetaConstants.ID);
        return dataRecordAccess.queryOne(entity, new Condition().eq(MetaConstants.ID, id));
    }
}
