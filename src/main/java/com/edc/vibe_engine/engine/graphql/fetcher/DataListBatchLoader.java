package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.common.support.AsyncUtils;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import org.dataloader.BatchLoaderEnvironment;
import org.dataloader.BatchLoaderWithContext;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;

public class DataListBatchLoader extends AbsDataBatchLoader implements BatchLoaderWithContext<String, DataRecord> {

    public DataListBatchLoader(String entityName, DataRecordAccess dataRecordAccess) {
        super(entityName, dataRecordAccess);
    }

    @Override
    public CompletionStage<List<DataRecord>> load(List<String> keys, BatchLoaderEnvironment environment) {
        return AsyncUtils.supplyAsync(() -> {
            final Map<String, DataRecord> map = loadDataMap(keys, environment);
            return keys.stream().map(map::get).toList();
        });
    }
}
