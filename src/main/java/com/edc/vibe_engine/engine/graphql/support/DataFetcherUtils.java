package com.edc.vibe_engine.engine.graphql.support;

import com.edc.vibe_engine.common.support.AsyncUtils;
import graphql.schema.DataFetcher;
import lombok.experimental.UtilityClass;

import java.util.concurrent.CompletableFuture;

@UtilityClass
public class DataFetcherUtils {
    public static <T> DataFetcher<CompletableFuture<T>> async(DataFetcher<T> fetcher) {
        return environment -> AsyncUtils.supplyAsync(() -> {
            try {
                return fetcher.get(environment);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }


}
