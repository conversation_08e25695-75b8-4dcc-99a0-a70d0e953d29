package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public class DataJoinManyFetcher extends AbsDataJoinFetcher implements DataFetcher<CompletableFuture<List<DataRecord>>> {

    private final DataRecordAccess dataRecordAccess;

    public DataJoinManyFetcher(String entityName, String relationName, String targetEntityName, DataRecordAccess dataRecordAccess) {
        super(entityName, relationName, targetEntityName);
        this.dataRecordAccess = dataRecordAccess;
    }


    @Override
    public CompletableFuture<List<DataRecord>> get(DataFetchingEnvironment environment) {
        return loadData(environment, dataRecordAccess::queryList);
    }
}
