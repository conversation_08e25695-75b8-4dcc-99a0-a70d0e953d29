package com.edc.vibe_engine.engine.graphql.ext;

import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.common.support.StringExUtils;
import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.interfaces.IGraphQLExtRegistry;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import graphql.Scalars;
import graphql.scalars.ExtendedScalars;
import graphql.schema.DataFetcher;
import graphql.schema.FieldCoordinates;
import graphql.schema.GraphQLArgument;
import graphql.schema.GraphQLCodeRegistry;
import graphql.schema.GraphQLFieldDefinition;
import graphql.schema.GraphQLNonNull;
import graphql.schema.GraphQLObjectType;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class MetaExtRegistry implements IGraphQLExtRegistry {

    public static final String METADATA = "_metadata";

    @Override
    public boolean support(DefEntity entity) {
        return Objects.equals(entity.getName(), MetaConstants.DEF_ENTITY);
    }

    @Override
    public void registryQuery(DefEntity entity, GraphQLObjectType.Builder query, GraphQLCodeRegistry.Builder codeRegistry) {
        codeRegistry.dataFetcher(FieldCoordinates.coordinates(EngineConstants.QUERY, METADATA), (DataFetcher<Object>) environment -> {
            final String key = environment.getArgument("key");
            final String[] keys = StringExUtils.splitColon(key);
            AssertUtils.assertTrue(keys != null && keys.length == 2, "key参数格式错误");
            final String metaType = keys[0]; // 元数据类型，目前只有 def_entity
            final String name = keys[1];
            return MetaManager.get(name);
        });


        query.field(GraphQLFieldDefinition.newFieldDefinition()
                .name(METADATA)
                .type(ExtendedScalars.Json)
                .argument(GraphQLArgument.newArgument()
                        .name("key").type(new GraphQLNonNull(Scalars.GraphQLID)))
        );
    }
}
