package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.common.interfaces.ILimited;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.common.types.Limited;
import com.edc.vibe_engine.data.query.QueryCondition;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.data.sql.support.ConditionHelper;
import com.edc.vibe_engine.json.JSON;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
public class DataListFetcher implements DataFetcher<List<DataRecord>> {

    private final DataRecordAccess dataRecordAccess;
    private final String entityName;



    @Override
    public List<DataRecord> get(DataFetchingEnvironment environment) throws Exception {
        final DefEntity entity = MetaManager.get(entityName);

        final Object conditionJson = environment.getArgument("condition");
        final ILimited limit = getLimit(environment);
        if (conditionJson != null) {
            final QueryCondition queryCondition = JSON.convertObject(conditionJson, QueryCondition.class);
            final Condition condition = ConditionHelper.convertToCondition(queryCondition);
            return dataRecordAccess.queryList(entity, condition, limit);
        }
        return dataRecordAccess.queryList(entity, new Condition(), limit);
    }

    private static Limited getLimit(DataFetchingEnvironment environment) {
        return new Limited(Optional.ofNullable(TypeCastUtils.castToInt(environment.getArgument("limit")))
                .orElse(500));
    }
}
