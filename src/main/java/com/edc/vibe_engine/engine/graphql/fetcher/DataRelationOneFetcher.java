package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import org.dataloader.DataLoader;

import java.util.concurrent.CompletableFuture;

public class DataRelationOneFetcher extends AbsRelationDataFetcher implements DataFetcher<CompletableFuture<DataRecord>> {

    private final String sourceFieldName;

    public DataRelationOneFetcher(String sourceFieldName, String targetEntityName, DataRecordAccess dataRecordAccess) {
        super(targetEntityName, dataRecordAccess);
        this.sourceFieldName = sourceFieldName;
    }


    @Override
    public CompletableFuture<DataRecord> get(DataFetchingEnvironment environment) {
        // 获取源对象
        DataRecord source = environment.getSource();
        assert source != null;
        // 获取关联 ID
        String relationId = source.getString(sourceFieldName);
        if (relationId == null) {
            return CompletableFuture.completedFuture(null);
        }

        final DataLoader<String, DataRecord> dataLoader = getRelationDataLoader(environment);
        // 使用 DataLoader 加载关联数据
        return dataLoader.load(relationId);
    }
}
