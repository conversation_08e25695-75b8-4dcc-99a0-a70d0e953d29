package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import org.apache.commons.collections4.CollectionUtils;
import org.dataloader.DataLoader;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

public class DataRelationManyFetcher extends AbsRelationDataFetcher implements DataFetcher<CompletableFuture<List<DataRecord>>> {

    private final String sourceFieldName;

    public DataRelationManyFetcher(String sourceFieldName, String targetEntityName, DataRecordAccess dataRecordAccess) {
        super(targetEntityName, dataRecordAccess);
        this.sourceFieldName = sourceFieldName;
    }


    @Override
    public CompletableFuture<List<DataRecord>> get(DataFetchingEnvironment environment) {
        // 获取源对象
        DataRecord source = environment.getSource();
        assert source != null;
        // 获取关联 ID
        List<Object> relationIds = source.getList(sourceFieldName);
        if (CollectionUtils.isEmpty(relationIds)) {
            return CompletableFuture.completedFuture(List.of());
        }

        final DataLoader<String, DataRecord> dataLoader = getRelationDataLoader(environment);
        // 使用 DataLoader 加载关联数据
        return dataLoader.loadMany(relationIds.stream().map(Object::toString).toList())
                .thenApply(list -> list.stream().filter(Objects::nonNull).toList());
    }
}
