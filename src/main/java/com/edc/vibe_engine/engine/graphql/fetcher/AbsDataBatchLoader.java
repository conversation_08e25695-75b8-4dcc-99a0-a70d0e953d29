package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import org.dataloader.BatchLoaderEnvironment;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbsDataBatchLoader {

    private final String entityName;

    private final DataRecordAccess dataRecordAccess;

    protected AbsDataBatchLoader(String entityName, DataRecordAccess dataRecordAccess) {
        this.entityName = entityName;
        this.dataRecordAccess = dataRecordAccess;
    }

    protected Map<String, DataRecord> loadDataMap(Collection<String> set, BatchLoaderEnvironment environment) {
        final DefEntity entity = MetaManager.get(entityName);
        // 批量查询多个ID的数据
        Condition condition = new Condition().in(MetaConstants.ID, set);
        List<DataRecord> records = dataRecordAccess.queryList(entity, condition, null);

        // 将结果映射为ID->记录的形式
        Map<String, DataRecord> result = new HashMap<>();
        for (DataRecord record : records) {
            result.put(record.getString(MetaConstants.ID), record);
        }
        return result;
    }
}
