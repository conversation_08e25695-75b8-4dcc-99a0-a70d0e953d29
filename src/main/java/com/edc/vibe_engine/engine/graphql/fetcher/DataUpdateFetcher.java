package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.engine.constants.EngineConstants;
import com.edc.vibe_engine.engine.extension.ExtensionContext;
import com.edc.vibe_engine.engine.extension.ExtensionManager;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@RequiredArgsConstructor
public class DataUpdateFetcher implements DataFetcher<DataRecord> {

    private final DataRecordAccess dataRecordAccess;
    private final ExtensionManager extensionManager;
    private final String entityName;


    @Override
    public DataRecord get(DataFetchingEnvironment environment) throws Exception {
        final DefEntity entity = MetaManager.get(entityName);
        final Object id = environment.getArgument(MetaConstants.ID);
        final Map<String, Object> input = environment.getArgument("input");

        final DataRecord record = dataRecordAccess.queryOne(entity, new Condition().eq(MetaConstants.ID, id));
        record.acceptInput(input);

        extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.PRE_UPDATE, record));

        dataRecordAccess.withTransaction(status -> {
            extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.READY_UPDATE, record));
            dataRecordAccess.update(entity, record);
            extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.POST_UPDATE, record));
            return null;
        }, () -> extensionManager.executeExtensions(new ExtensionContext<>(entity, EngineConstants.COMMIT_UPDATE, record)));

        return dataRecordAccess.queryOne(entity, new Condition().eq(MetaConstants.ID, id));
    }
}
