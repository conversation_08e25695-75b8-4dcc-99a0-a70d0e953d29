package com.edc.vibe_engine.engine.graphql.fetcher;

import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.common.types.ListPageResult;
import com.edc.vibe_engine.data.query.QueryCondition;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.data.sql.support.ConditionHelper;
import com.edc.vibe_engine.json.JSON;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.DataRecordAccess;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@RequiredArgsConstructor
public class DataPageFetcher implements DataFetcher<ListPageResult<DataRecord>> {

    private final DataRecordAccess dataRecordAccess;
    private final String entityName;


    @Override
    public ListPageResult<DataRecord> get(DataFetchingEnvironment environment) throws Exception {
        final DefEntity entity = MetaManager.get(entityName);
        final Object conditionJson = environment.getArgument("condition");
        final IPageable pageable = getPageable(environment);
        if (conditionJson != null) {
            final QueryCondition queryCondition = JSON.convertObject(conditionJson, QueryCondition.class);
            final Condition condition = ConditionHelper.convertToCondition(queryCondition);
            return dataRecordAccess.queryPage(entity, condition, pageable);
        }
        return dataRecordAccess.queryPage(entity, new Condition(), pageable);
    }

    private static IPageable getPageable(DataFetchingEnvironment environment) {
        final Integer page = Optional.ofNullable(TypeCastUtils.castToInt(environment.getArgument("page")))
                .orElse(1);
        final Integer pageSize = Optional.ofNullable(TypeCastUtils.castToInt(environment.getArgument("page_size")))
                .orElse(10);


        return IPageable.page(page, pageSize);
    }
}
