package com.edc.vibe_engine.record;

import com.edc.vibe_engine.common.interfaces.ILimited;
import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.common.support.PageableExUtils;
import com.edc.vibe_engine.common.types.ListPageResult;
import com.edc.vibe_engine.data.idgen.IdGenerator;
import com.edc.vibe_engine.data.sql.SqlExecutable;
import com.edc.vibe_engine.data.sql.block.AbsSqlBlock;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import com.edc.vibe_engine.data.sql.support.SqlParserUtils;
import com.edc.vibe_engine.data.transaction.TransactionExecutor;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.record.adapter.DataRecordExtractor;
import com.edc.vibe_engine.record.column.ColumnCollection;
import com.edc.vibe_engine.record.column.DataColumn;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

public class DataRecordAccess {

    private final JdbcTemplate jdbcTemplate;
    private final TransactionExecutor transactionExecutor;


    private static final Map<String, BiConsumer<DataRecord, AbsSqlBlock<?>>> presetFieldHandlers = new HashMap<>();

    static {
        presetFieldHandlers.put(MetaConstants.ID, (record, block) -> {
            if (ObjectUtils.isEmpty(record.get(MetaConstants.ID))) {
                record.set(MetaConstants.ID, IdGenerator.nextSnowId());
            }
            block.set(MetaConstants.ID, record.get(MetaConstants.ID));
        });
        presetFieldHandlers.put(MetaConstants.CREATE_AT, (record, block) -> {
            if (ObjectUtils.isEmpty(record.get(MetaConstants.CREATE_AT))) {
                record.set(MetaConstants.CREATE_AT, LocalDateTime.now());
            }
            block.setOnInsert(MetaConstants.CREATE_AT, record.get(MetaConstants.CREATE_AT));
        });
        presetFieldHandlers.put(MetaConstants.UPDATE_AT, (record, block) -> {
            record.set(MetaConstants.UPDATE_AT, LocalDateTime.now());
            block.setOnInsert(MetaConstants.UPDATE_AT, record.get(MetaConstants.UPDATE_AT));
        });
        presetFieldHandlers.put(MetaConstants.CREATE_BY, (record, block) -> {
            if (ObjectUtils.isEmpty(record.get(MetaConstants.CREATE_BY))) {
                record.set(MetaConstants.CREATE_BY, "test");
            }
            block.setOnInsert(MetaConstants.CREATE_BY, record.get(MetaConstants.CREATE_BY));
        });
        presetFieldHandlers.put(MetaConstants.UPDATE_BY, (record, block) -> {
            record.set(MetaConstants.UPDATE_BY, "test");
            block.setOnInsert(MetaConstants.UPDATE_BY, record.get(MetaConstants.UPDATE_BY));
        });
    }


    public DataRecordAccess(DataSource dataSource, PlatformTransactionManager transactionManager) {
        this(new JdbcTemplate(dataSource), transactionManager);
    }

    public DataRecordAccess(JdbcTemplate jdbcTemplate, PlatformTransactionManager transactionManager) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionExecutor = new TransactionExecutor(transactionManager);
    }

    public <T> T withTransaction(Function<TransactionStatus, T> function, Runnable afterCommit) {
        return this.transactionExecutor.runWithRequired(status -> {
            T result = function.apply(status);
            if (afterCommit != null) {
                afterCommit.run();
            }
            return result;
        });
    }

    public void registerAfterCommit(Runnable runnable) {
        this.transactionExecutor.registerAfterCommit(runnable);
    }


    private <T> T queryWithDefEntity(DefEntity entity, Condition condition, BiFunction<SqlExecutable, DataRecordExtractor, T> biConsumer) {
        final String tableName = entity.fullTableName();

        List<String> selects = new ArrayList<>();
        ColumnCollection columns = new ColumnCollection();
        for (DefField field : entity.getFields()) {
            selects.add(field.getName());
            columns.add(new DataColumn(field.getName(), field.getDataType().getJavaType(), field.getDefaultValue()));
        }

        final SqlExecutable select = SqlExecutable.createSelect(tableName, selects, condition);

        return biConsumer.apply(select, new DataRecordExtractor(columns));
    }

    public int inset(DefEntity entity, DataRecord record) {
        final String tableName = entity.fullTableName();
        final SqlExecutable insert = SqlExecutable.createInsert(tableName, mkBlockConsumer(entity, record));
        return jdbcTemplate.update(insert.getSql(), insert.getParamArray());
    }

    public int update(DefEntity entity, DataRecord record) {
        final String tableName = entity.fullTableName();
        final SqlExecutable insert = SqlExecutable.createUpdate(tableName, cond -> {
            cond.eq(MetaConstants.ID, record.get(MetaConstants.ID));
        }, mkBlockConsumer(entity, record));
        final int update = jdbcTemplate.update(insert.getSql(), insert.getParamArray());
        record.acceptChanges();
        return update;
    }

    public int delete(DefEntity entity, DataRecord record) {
        final String tableName = entity.fullTableName();
        final SqlExecutable delete = SqlExecutable.createDelete(tableName, cond -> {
            cond.eq(MetaConstants.ID, record.get(MetaConstants.ID));
        });
        final int update = jdbcTemplate.update(delete.getSql(), delete.getParamArray());
        record.acceptChanges();
        return update;
    }

    private static <T extends AbsSqlBlock<T>> Consumer<T> mkBlockConsumer(DefEntity entity, DataRecord record) {
        return block -> {
            for (DefField field : entity.getFields()) {
                final BiConsumer<DataRecord, AbsSqlBlock<?>> preset = presetFieldHandlers.get(field.getName());
                if (preset != null) {
                    preset.accept(record, block);
                } else {
                    block.set(field.getName(), record.get(field.getName()));
                }
            }
        };
    }

    public List<DataRecord> queryList(DefEntity entity, Condition condition) {
        return queryWithDefEntity(entity, condition, (select, extractor) -> {
            return jdbcTemplate.query(select.getSql(), extractor, select.getParamArray());
        });
    }

    public List<DataRecord> queryList(DefEntity entity, Condition condition, ILimited limited) {
        return queryWithDefEntity(entity, condition, (select, extractor) -> {
            String limitSql = SqlDialectHelper.appendLimitOffset(select.getSql(), limited);
            return jdbcTemplate.query(limitSql, extractor, select.getParamArray());
        });
    }

    public DataRecord queryOne(DefEntity entity, Condition condition) {
        final List<DataRecord> list = queryList(entity, condition);
        return AssertUtils.assertGetOne(list);
    }

    public ListPageResult<DataRecord> queryPage(DefEntity entity, Condition condition, IPageable pageable) {
        return queryWithDefEntity(entity, condition, (select, extractor) -> {
            final String selectSql = select.getSql();
            String pageSql = SqlDialectHelper.appendLimitOffset(selectSql, pageable);
            final Object[] paramArray = select.getParamArray();
            final List<DataRecord> records = jdbcTemplate.query(pageSql, extractor, paramArray);
            return PageableExUtils.getPage(records, pageable, () -> {
                String countSql = SqlParserUtils.autoCountSql(selectSql);
                return findLong(countSql, paramArray);
            });
        });
    }


    public List<DataRecord> queryList(SqlExecutable executable) {
        return queryList(executable.getSql(), executable.getParamArray());
    }

    public List<DataRecord> queryList(String sql, Object... params) {
        return jdbcTemplate.query(sql, new DataRecordExtractor(), params);
    }

    public DataRecord queryOne(SqlExecutable executable) {
        return queryOne(executable.getSql(), executable.getParamArray());
    }

    public DataRecord queryOne(String sql, Object... params) {
        final List<DataRecord> list = queryList(sql, params);
        return AssertUtils.assertGetOne(list);
    }

    public ListPageResult<DataRecord> queryPage(SqlExecutable executable, IPageable pageable) {
        return queryPage(executable.getSql(), pageable, executable.getParamArray());
    }

    public ListPageResult<DataRecord> queryPage(String sql, IPageable pageable, Object... params) {
        String pageSql = SqlDialectHelper.appendLimitOffset(sql, pageable);

        List<DataRecord> list = queryList(pageSql, params);
        return PageableExUtils.getPage(list, pageable, () -> {
            String countSql = SqlParserUtils.autoCountSql(sql);
            return findLong(countSql, params);
        });
    }

    public long findLong(String sql, Object... params) {
        Long number = jdbcTemplate.queryForObject(sql, Long.class, params);
        return Optional.ofNullable(number).orElse(0L);
    }
}
