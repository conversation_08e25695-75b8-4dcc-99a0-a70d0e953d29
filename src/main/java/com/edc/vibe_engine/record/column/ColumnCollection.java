package com.edc.vibe_engine.record.column;

import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.record.interfaces.IColumn;
import io.vavr.collection.Seq;
import io.vavr.collection.Vector;
import jakarta.annotation.Nonnull;

import java.util.Iterator;
import java.util.function.Function;
import java.util.function.ObjIntConsumer;

public class ColumnCollection implements Iterable<IColumn> {
    private Vector<IColumn> columns;

    public ColumnCollection() {
        this.columns = Vector.empty();
    }

    public ColumnCollection(Iterable<IColumn> columns) {
        this();
        columns.forEach(this::add);
    }

    /**
     * Adds a IDataColumn to the column collection.
     *
     * @param newColumn The new columns to add.
     * @return Returns a new DataTable with the item added.
     */
    public void add(IColumn newColumn) {
        AssertUtils.assertTrue(columnIdxByName(newColumn.getName()) == -1, "Duplicate column name: " + newColumn.getName());
        this.columns = columns.append(newColumn);
    }

    /**
     * Replaces the column at the specified index with the new column.
     *
     * @param index     The index of the item to be replaced.
     * @param newColumn The new column.
     * @return Returns a new collection with the column replaced.
     */
    public void replace(Integer index, IColumn newColumn) {
        final int existingIndex = columnIdxByName(newColumn.getName());
        AssertUtils.assertTrue(existingIndex == index || existingIndex == -1, "Duplicate column name: " + newColumn.getName());
        this.columns = columns.update(index, newColumn);
    }

    /**
     * Inserts a new column at the specified index.
     *
     * @param index     The column index to inserted the column at.
     * @param newColumn The new column.
     * @return Returns a new collection with the column inserted.
     */
    public void insert(Integer index, IColumn newColumn) {
        AssertUtils.assertTrue(columnIdxByName(newColumn.getName()) == -1, "Duplicate column name: " + newColumn.getName());
        this.columns = columns.insert(index, newColumn);
    }

    /**
     * Removes the specified column.
     *
     * @param index The index of the column to be removed.
     * @return Returns a new collection with the column removed.
     */
    public void remove(Integer index) {
        this.columns = columns.removeAt(index);
    }

    /**
     * Returns the IDataColumn by name.
     *
     * @param columnName The name of the IDataColumn.
     * @return Returns the IDataColumn.
     */
    public IColumn get(String columnName) {
        final int index = columnIdxByName(columnName);
        if (index == -1)
            return null;
        return this.columns.get(index);
    }

    public IColumn get(int index) {
        return columns.get(index);
    }

    public int size() {
        return this.columns.length();
    }

    public boolean isEmpty() {
        return this.columns.isEmpty();
    }

    /**
     * Map implementation for the DataColumnCollection class.
     *
     * @param <U>    Mapped return type.
     * @param mapper The map function.
     * @return Returns a sequence of the applied map.
     */
    public <U> Seq<U> map(Function<? super IColumn, ? extends U> mapper) {
        return this.columns.map(mapper);
    }

    public void forEachWithIndex(ObjIntConsumer<? super IColumn> consumer) {
        this.columns.forEachWithIndex(consumer);
    }


    @Override
    @Nonnull
    public Iterator<IColumn> iterator() {
        return columns.iterator();
    }


    public int columnIdxByName(String columnName) {
        return this.columns.indexWhere(col -> compare(col.getName(), columnName));
    }


    private static boolean compare(String str1, String str2) {
        return (str1 == null ? str2 == null : str1.equalsIgnoreCase(str2));
    }
}
