package com.edc.vibe_engine.record.column;

import com.edc.vibe_engine.record.interfaces.IColumn;

import java.util.HashMap;
import java.util.Map;

public class DataColumn implements IColumn {


    private static final Map<Class<?>, Object> SAFE_VALUE_MAP = new HashMap<>(8);

    static {
        SAFE_VALUE_MAP.put(String.class, "");
        SAFE_VALUE_MAP.put(Integer.class, 0);
        SAFE_VALUE_MAP.put(Long.class, 0L);
        SAFE_VALUE_MAP.put(Double.class, 0.0);
        SAFE_VALUE_MAP.put(Float.class, 0.0f);
        SAFE_VALUE_MAP.put(Boolean.class, false);
    }

    private final String name;
    private final Class<?> type;
    private final Object defaultValue;

    public DataColumn(String name, Class<?> type) {
        this(name, type, null);
    }

    public DataColumn(String name, Class<?> type, Object defaultValue) {
        this.name = name;
        this.type = type;
        this.defaultValue = defaultValue;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Class<?> getType() {
        return type;
    }

    @Override
    public Object defaultValue() {
        return defaultValue;
    }

    @Override
    public Object safeValue(Object val) {
        if (val != null) {
            return val;
        }
        return SAFE_VALUE_MAP.get(type);
    }
}
