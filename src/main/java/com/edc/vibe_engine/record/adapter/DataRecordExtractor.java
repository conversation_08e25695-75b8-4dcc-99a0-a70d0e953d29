package com.edc.vibe_engine.record.adapter;

import com.edc.vibe_engine.common.support.ClassExUtils;
import com.edc.vibe_engine.data.support.RowMapperUtils;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.column.ColumnCollection;
import com.edc.vibe_engine.record.column.DataColumn;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class DataRecordExtractor implements ResultSetExtractor<List<DataRecord>> {

    private final ColumnCollection columns;

    public DataRecordExtractor() {
        this.columns = new ColumnCollection();
    }


    public DataRecordExtractor(ColumnCollection columns) {
        this.columns = columns;
    }


    protected void initColumn(ResultSetMetaData metaData) throws SQLException {
        final int columnCount = metaData.getColumnCount();
        for (int i = 0; i < columnCount; i++) {
            // metaData 内部实现序号从 1 开始
            final String columnName = metaData.getColumnLabel(i + 1);
            final String columnClassName = metaData.getColumnClassName(i + 1);

            columns.add(new DataColumn(columnName, ClassExUtils.forName(columnClassName)));
        }
    }

    @Override
    public List<DataRecord> extractData(ResultSet rs) throws SQLException {
        if (columns.isEmpty()) {
            this.initColumn(rs.getMetaData());
        }
        List<DataRecord> records = new ArrayList<>();
        while (rs.next()) {
            DataRecord row = new DataRecord(columns);
            RowMapperUtils.forEachColumn(rs, row::set);
            row.acceptChanges();
            records.add(row);
        }
        return records;
    }
}
