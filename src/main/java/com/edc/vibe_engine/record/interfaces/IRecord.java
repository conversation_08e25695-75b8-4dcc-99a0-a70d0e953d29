package com.edc.vibe_engine.record.interfaces;

import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.json.JSON;
import com.fasterxml.jackson.core.type.TypeReference;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IRecord {

    /**
     * @return 返回原始值 k -> v
     */
    Map<String, Object> toMap();

    Set<String> keySet();

    boolean containsKey(String column);

    /**
     * 字段是否有变更
     *
     * @param column
     * @return
     */
    boolean hasChange(String column);


    /**
     * @param column 字段名
     * @return 取数
     */
    Object get(String column);

    /**
     * @param column 字段名
     * @param tClass
     * @param <T>
     * @return 取数，并转换类型
     */
    default <T> T get(String column, Class<T> tClass) {
        return TypeCastUtils.cast(get(column), tClass);
    }


    @SuppressWarnings("unchecked")
    default Map<String, Object> getMap(String column) {
        Object value = get(column);

        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }

        if (value instanceof String) {
            return JSON.toMap((String) value);
        }

        return JSON.convertToMap(value);
    }

    @SuppressWarnings("unchecked")
    default List<Object> getList(String column) {
        Object value = get(column);
        if (value == null) {
            return null;
        }

        if (value instanceof List<?>) {
            return ((List<Object>) value);
        }

        if (JSON.mayBeJsonArray(value)) {
            return JSON.toList((String) value);
        }
        return JSON.convertObjectList(value, Object.class);
    }

    default <T> T getObject(String column, Class<T> clazz) {
        Object value = get(column);
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return JSON.toJavaObject(((String) value), clazz);
        }
        return JSON.convertObject(value, clazz);
    }

    default <T> T getObject(String column, TypeReference<T> genericType) {
        Object value = get(column);
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return JSON.toJavaObject(((String) value), genericType);
        }
        return JSON.convertObject(value, genericType);
    }

    default <T> List<T> getObjectList(String column, Class<T> clazz) {
        Object value = get(column);
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return JSON.toJavaList(((String) value), clazz);
        }
        return JSON.convertObjectList(value, clazz);
    }

    default Boolean getBoolean(String column) {
        return get(column, Boolean.class);
    }

    default Short getShort(String column) {
        return get(column, Short.class);
    }

    default Integer getInteger(String column) {
        return get(column, Integer.class);
    }

    default Long getLong(String column) {
        return get(column, Long.class);
    }


    default Float getFloat(String column) {
        return get(column, Float.class);
    }

    default Double getDouble(String column) {
        return get(column, Double.class);
    }

    default BigDecimal getBigDecimal(String column) {
        return get(column, BigDecimal.class);
    }


    default BigInteger getBigInteger(String column) {
        return get(column, BigInteger.class);
    }

    default String getString(String column) {
        return get(column, String.class);
    }

    default LocalDate getDate(String column) {
        return get(column, LocalDate.class);
    }

    default LocalDateTime getDateTime(String column) {
        return get(column, LocalDateTime.class);
    }

    default LocalTime getTime(String column) {
        return get(column, LocalTime.class);
    }
}
