package com.edc.vibe_engine.record.support;

import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.record.column.ColumnCollection;
import com.edc.vibe_engine.record.column.DataColumn;

public class EntityRecordUtils {
    public static ColumnCollection mkColumnCollection(DefEntity entity) {
        ColumnCollection columns = new ColumnCollection();
        for (DefField field : entity.getFields()) {
            columns.add(new DataColumn(field.getName(), field.getDataType().getJavaType(), field.getDefaultValue()));
        }
        return columns;
    }
}
