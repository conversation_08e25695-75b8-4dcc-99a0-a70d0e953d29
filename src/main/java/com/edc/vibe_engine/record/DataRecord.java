package com.edc.vibe_engine.record;

import com.edc.vibe_engine.common.support.ClassExUtils;
import com.edc.vibe_engine.common.support.ObjectExUtils;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.json.JSON;
import com.edc.vibe_engine.record.column.ColumnCollection;
import com.edc.vibe_engine.record.column.DataColumn;
import com.edc.vibe_engine.record.interfaces.IColumn;
import com.edc.vibe_engine.record.interfaces.IModifyRecord;
import org.apache.commons.collections4.MapUtils;
import org.eclipse.collections.api.map.primitive.MutableIntObjectMap;
import org.eclipse.collections.impl.map.mutable.primitive.IntObjectHashMap;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

public class DataRecord implements IModifyRecord {

    private final ColumnCollection columns;
    private MutableIntObjectMap<Object> prev;
    private MutableIntObjectMap<Object> curr;


    public DataRecord() {
        this(new ColumnCollection());
    }

    public DataRecord(ColumnCollection columns) {
        this.columns = columns;
        this.prev = null;
        this.curr = mkIntObjectMap(columns, IColumn::defaultValue);
    }

    public DataRecord(Map<String, Object> raw) {
        this();
        this.acceptInput(raw);
    }

    public void acceptInput(Map<String, Object> input) {
        if (MapUtils.isEmpty(input)) {
            return;
        }

        for (Map.Entry<String, Object> entry : input.entrySet()) {
            set(entry.getKey(), entry.getValue());
        }
    }

    private static MutableIntObjectMap<Object> mkIntObjectMap(ColumnCollection columns, Function<IColumn, Object> consumer) {
        final IntObjectHashMap<Object> result = IntObjectHashMap.newMap();
        columns.forEachWithIndex((column, index) -> result.put(index, consumer.apply(column)));
        return result;
    }

    private void beginEdit() {
        if (this.curr == null) {
            this.curr = IntObjectHashMap.newMap();
            this.prev.forEachKeyValue((index, obj) -> this.curr.put(index, clone(obj)));
        }
    }

    @Override
    public void set(String column, Object value) {
        final int index = this.columns.columnIdxByName(column);
        beginEdit();
        if (index >= 0) {
            final IColumn iColumn = columns.get(index);
            this.curr.put(index, TypeCastUtils.cast(value, iColumn.getType()));
        } else {
            this.columns.add(new DataColumn(column, Object.class, null));
            this.curr.put(columns.size() - 1, value);
        }
    }

    @Override
    public void acceptChanges() {
        if (this.curr != null) {
            this.prev = IntObjectHashMap.newMap();
            this.curr.forEachKeyValue((index, obj) -> this.prev.put(index, clone(obj)));
        }
        this.curr = null;
    }

    private Object clone(Object obj) {
        if (obj == null) {
            return null;
        }

        if (ClassExUtils.isBasicJavaType(obj.getClass())) {
            return obj;
        } else {
            return JSON.clone(obj);
        }
    }

    @Override
    public Map<String, Object> toMap() {
        final int size = columns.size();
        Map<String, Object> map = new LinkedHashMap<>(size);
        for (int i = 0; i < size; i++) {
            map.put(columns.get(i).getName(), doGetVal(i));
        }
        return map;
    }

    @Override
    public Set<String> keySet() {
        return columns.map(IColumn::getName).toJavaSet();
    }

    @Override
    public boolean containsKey(String column) {
        return columns.get(column) != null;
    }

    @Override
    public boolean hasChange(String column) {
        final int index = columns.columnIdxByName(column);
        if (index < 0)
            return false;
        if (this.prev == null)
            return true;
        if (this.curr == null)
            return false;

        final Object prevVal = Optional.of(this.prev).map(map -> map.get(index)).orElse(null);
        final Object currVal = Optional.ofNullable(this.curr).map(map -> map.get(index)).orElse(null);
        return !ObjectExUtils.isEqual(prevVal, currVal);
    }

    @Override
    public Object get(String column) {
        final int index = columns.columnIdxByName(column);
        if (index < 0)
            return null;
        return doGetVal(index);
    }

    private Object doGetVal(int index) {
        MutableIntObjectMap<Object> used = this.curr == null ? this.prev : this.curr;
        final Object store = used.get(index);
        final IColumn column = columns.get(index);
        return column.safeValue(store);
    }
}
