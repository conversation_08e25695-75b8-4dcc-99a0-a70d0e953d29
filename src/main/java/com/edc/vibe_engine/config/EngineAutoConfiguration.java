package com.edc.vibe_engine.config;

import com.edc.vibe_engine.config.listener.DBInitListener;
import com.edc.vibe_engine.data.JdbcAccess;
import com.edc.vibe_engine.data.connection.ConnectionSupplier;
import com.edc.vibe_engine.data.schema.AbsJdbcSchemaManager;
import com.edc.vibe_engine.data.schema.JdbcSchemaManager;
import com.edc.vibe_engine.data.schema.MySqlSchemaManager;
import com.edc.vibe_engine.data.schema.PgSqlSchemaManager;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import com.edc.vibe_engine.meta.interfaces.IMetaRegister;
import com.edc.vibe_engine.record.DataRecordAccess;
import org.springframework.boot.autoconfigure.jdbc.JdbcConnectionDetails;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.List;

@Configuration
public class EngineAutoConfiguration {

    @Bean
    public JdbcSchemaManager jdbcSchemaManager(JdbcConnectionDetails details) {
        final ConnectionSupplier connectionSupplier = new ConnectionSupplier(details.getJdbcUrl(), details.getUsername(), details.getPassword());
        final SqlDialect sqlDialect = SqlDialect.fromJdbc(details.getJdbcUrl());
        final AbsJdbcSchemaManager schemaManager;
        if (sqlDialect == SqlDialect.PGSQL) {
            schemaManager = new PgSqlSchemaManager(connectionSupplier);
        } else {
            schemaManager = new MySqlSchemaManager(connectionSupplier);
        }
        return schemaManager;
    }


    //    @Bean
    //    public DataSource dataSource(JdbcConnectionDetails details) {
    //        //        final JdbcConfig jdbc = props.getJdbc();
    //        return DataSourceBuilder.create(details.getClass().getClassLoader())
    //                .type(HikariDataSource.class)
    //                .driverClassName(details.getDriverClassName())
    //                .url(details.getJdbcUrl())
    //                .username(details.getUsername())
    //                .password(details.getPassword())
    //                .build();
    //    }

    @Bean
    public JdbcAccess jdbcAccess(DataSource dataSource) {
        return new JdbcAccess(dataSource);
    }


    @Bean
    public DataRecordAccess dataRecordAccess(DataSource dataSource, PlatformTransactionManager transactionManager) {
        return new DataRecordAccess(dataSource, transactionManager);
    }


    @Bean
    public DBInitListener dbInitListener(JdbcSchemaManager jdbcSchemaManager, List<IMetaRegister> metaRegisters) {
        return new DBInitListener(jdbcSchemaManager, metaRegisters);
    }
}
