package com.edc.vibe_engine.config.listener;

import com.edc.vibe_engine.data.schema.JdbcSchemaManager;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.interfaces.IMetaRegister;
import com.edc.vibe_engine.meta.support.MetaDBUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class DBInitListener implements ApplicationListener<ContextRefreshedEvent> {

    private final AtomicBoolean done = new AtomicBoolean(false);

    private final JdbcSchemaManager jdbcSchemaManager;
    private final List<IMetaRegister> metaRegisters;

    public DBInitListener(JdbcSchemaManager jdbcSchemaManager, List<IMetaRegister> metaRegisters) {
        this.jdbcSchemaManager = jdbcSchemaManager;
        this.metaRegisters = metaRegisters;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (!done.compareAndSet(false, true)) {
            return;
        }

        log.info("Initializing database and registering metadata...");

        // 注册所有元数据
        for (IMetaRegister metaRegister : metaRegisters) {
            try {
                log.info("Registering metadata with {}", metaRegister.getClass().getSimpleName());
                metaRegister.register();
            } catch (Exception e) {
                log.error("Error registering metadata with {}", metaRegister.getClass().getSimpleName(), e);
            }
        }

        // 获取所有实体并更新数据库结构
        final List<DefEntity> entities = MetaManager.getAll();
        log.info("Found {} entities to upgrade in database", entities.size());

        for (DefEntity meta : entities) {
            try {
                jdbcSchemaManager.upgrade(MetaDBUtils.convertDBTable(meta), null, log::info);
            } catch (Exception e) {
                log.error("Error upgrading database table for entity: {}", meta.getName(), e);
            }
        }

        log.info("Database initialization completed");
    }
}
