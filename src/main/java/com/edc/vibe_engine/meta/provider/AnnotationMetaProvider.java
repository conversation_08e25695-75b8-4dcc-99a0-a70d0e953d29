package com.edc.vibe_engine.meta.provider;

import com.edc.vibe_engine.meta.annotation.Entity;
import com.edc.vibe_engine.meta.annotation.MetaSource;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.interfaces.IMetaProvider;
import com.edc.vibe_engine.meta.model.MetaDefinition;
import com.edc.vibe_engine.meta.register.EntityAnnotationRegister;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 注解驱动的元数据提供者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AnnotationMetaProvider implements IMetaProvider {

    private final EntityAnnotationRegister annotationRegister;

    /**
     * 注解元数据缓存
     */
    private final Map<String, MetaDefinition> annotationMetaCache = new ConcurrentHashMap<>();

    @Override
    public MetaSource.SourceType getSourceType() {
        return MetaSource.SourceType.ANNOTATION;
    }

    @Override
    public MetaDefinition getMetaDefinition(String entityName) {
        // 检查缓存
        if (annotationMetaCache.containsKey(entityName)) {
            return annotationMetaCache.get(entityName);
        }

        // 扫描注解并解析
        try {
            Class<?> entityClass = findEntityClass(entityName);
            if (entityClass == null) {
                return null;
            }

            MetaDefinition definition = parseAnnotationToMetaDefinition(entityClass);
            if (definition != null) {
                annotationMetaCache.put(entityName, definition);
            }

            return definition;
        } catch (Exception e) {
            log.error("Failed to get meta definition for entity: {}", entityName, e);
            return null;
        }
    }

    @Override
    public List<MetaDefinition> getAllMetaDefinitions() {
        // 扫描所有带有@Entity注解的类
        try {
            var entityClasses = annotationRegister.scan("com.edc.vibe_engine");
            return entityClasses.stream()
                    .map(this::parseAnnotationToMetaDefinition)
                    .filter(def -> def != null)
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get all meta definitions", e);
            return List.of();
        }
    }

    @Override
    public List<MetaDefinition> getMetaDefinitionsByModule(String module) {
        return getAllMetaDefinitions().stream()
                .filter(def -> module.equals(def.getModule()))
                .toList();
    }

    @Override
    public void registerMetaDefinition(MetaDefinition definition) {
        // 注解驱动的元数据通常不支持动态注册
        log.warn("Annotation meta provider does not support dynamic registration");
    }

    @Override
    public boolean exists(String entityName) {
        return getMetaDefinition(entityName) != null;
    }

    @Override
    public void refresh() {
        annotationMetaCache.clear();
        log.info("Annotation meta cache refreshed");
    }

    @Override
    public int getPriority() {
        return 100; // 注解驱动的优先级较高
    }

    /**
     * 查找实体类
     */
    private Class<?> findEntityClass(String entityName) {
        try {
            var entityClasses = annotationRegister.scan("com.edc.vibe_engine");
            return entityClasses.stream()
                    .filter(clazz -> {
                        Entity entity = clazz.getAnnotation(Entity.class);
                        return entity != null && entityName.equals(entity.name());
                    })
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            log.error("Failed to find entity class for: {}", entityName, e);
            return null;
        }
    }

    /**
     * 解析注解为元数据定义
     */
    private MetaDefinition parseAnnotationToMetaDefinition(Class<?> entityClass) {
        try {
            Entity entityAnnotation = entityClass.getAnnotation(Entity.class);
            if (entityAnnotation == null) {
                return null;
            }

            // 解析实体注解
            DefEntity defEntity = annotationRegister.parse(entityClass);

            // 转换为MetaDefinition
            MetaDefinition definition = MetaDefinition.fromDefEntity(defEntity);
            definition.setSource(MetaSource.SourceType.ANNOTATION);

            // 设置优先级
            MetaSource metaSource = entityClass.getAnnotation(MetaSource.class);
            if (metaSource != null) {
                definition.setPriority(metaSource.priority());
            }

            return definition;
        } catch (Exception e) {
            log.error("Failed to parse annotation for class: {}", entityClass.getName(), e);
            return null;
        }
    }
}
