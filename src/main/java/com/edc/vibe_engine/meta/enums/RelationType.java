package com.edc.vibe_engine.meta.enums;

/**
 * 关联类型
 */
public enum RelationType {
    /**
     * 通过当前实体的某个字段，以主键形式关联到另一个实体
     */
    REF_ONE("ref_one"),
    /**
     * 通过当前实体的某个 数组/json 字段，以主键形式关联到多个实体
     */
    REF_MANY("ref_many"),

    /**
     * 通过当前实体，按规则关联到另一个实体，实体本身不记录关联主键
     */
    JOIN_ONE("join_one"),
    /**
     * 通过当前实体，按规则关联到多个实体，实体本身不记录关联主键
     */
    JOIN_MANY("join_many"),
    ;

    private final String value;

    RelationType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public boolean isRef() {
        return this == REF_ONE || this == REF_MANY;
    }

    public boolean isJoin() {
        return this == JOIN_ONE || this == JOIN_MANY;
    }

    public static RelationType fromValue(String value) {
        for (RelationType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown RelationType: " + value);
    }
}
