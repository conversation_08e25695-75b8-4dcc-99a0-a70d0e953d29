package com.edc.vibe_engine.meta.enums;

/**
 * 模式类型
 */
public enum FormModeType {
    CREATE("create"),
    EDIT("edit"),
    VIEW("view");

    private final String value;

    FormModeType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static FormModeType fromValue(String value) {
        for (FormModeType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown ModeType: " + value);
    }
}
