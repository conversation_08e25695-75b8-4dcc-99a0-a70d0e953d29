package com.edc.vibe_engine.meta.enums;

/**
 * 关联条件类型
 */
public enum RelationConditionType {
    ALWAYS("always"),
    EXPRESSION("expression"),
    ROLE("role"),
    MODE("mode");

    private final String value;

    RelationConditionType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static RelationConditionType fromValue(String value) {
        for (RelationConditionType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown RelationConditionType: " + value);
    }
}
