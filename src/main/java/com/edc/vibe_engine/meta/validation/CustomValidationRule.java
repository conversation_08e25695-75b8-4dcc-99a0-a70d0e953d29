package com.edc.vibe_engine.meta.validation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 自定义验证规则
 */
@Getter
@Setter
@SuperBuilder(toBuilder = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomValidationRule extends ValidationRule {

    /**
     * 创建一个新的自定义验证规则实例
     *
     * @param expression 验证表达式
     */
    public CustomValidationRule(String expression) {
        super();
        setType(ValidationRuleType.CUSTOM);
        CustomValidationParams params = new CustomValidationParams();
        params.setExpression(expression);
        setParams(params);
    }

    /**
     * 获取验证表达式
     *
     * @return 验证表达式
     */
    public String getExpression() {
        if (getParams() instanceof CustomValidationParams) {
            return ((CustomValidationParams) getParams()).getExpression();
        }
        return null;
    }

    /**
     * 设置验证表达式
     *
     * @param expression 验证表达式
     */
    public void setExpression(String expression) {
        CustomValidationParams params = new CustomValidationParams();
        params.setExpression(expression);
        setParams(params);
    }

    /**
     * 自定义验证规则参数
     */
    @Getter
    @Setter
    public static class CustomValidationParams {
        private String expression;
    }
}
