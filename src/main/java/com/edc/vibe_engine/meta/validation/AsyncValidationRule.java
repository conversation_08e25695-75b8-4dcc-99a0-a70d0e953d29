package com.edc.vibe_engine.meta.validation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 异步验证规则
 */
@Getter
@Setter
@SuperBuilder(toBuilder = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AsyncValidationRule extends ValidationRule {

    /**
     * 创建一个新的异步验证规则实例
     *
     * @param url     验证API地址
     * @param method  HTTP方法
     * @param headers 请求头
     */
    public AsyncValidationRule(String url, String method, Map<String, String> headers) {
        super();
        setType(ValidationRuleType.ASYNC);
        setAsync(true);
        AsyncValidationParams params = new AsyncValidationParams();
        params.setUrl(url);
        params.setMethod(method);
        params.setHeaders(headers);
        setParams(params);
    }

    /**
     * 获取验证API地址
     *
     * @return 验证API地址
     */
    public String getUrl() {
        if (getParams() instanceof AsyncValidationParams) {
            return ((AsyncValidationParams) getParams()).getUrl();
        }
        return null;
    }

    /**
     * 设置验证API地址
     *
     * @param url 验证API地址
     */
    public void setUrl(String url) {
        AsyncValidationParams params = getAsyncParams();
        params.setUrl(url);
        setParams(params);
    }

    /**
     * 获取HTTP方法
     *
     * @return HTTP方法
     */
    public String getMethod() {
        if (getParams() instanceof AsyncValidationParams) {
            return ((AsyncValidationParams) getParams()).getMethod();
        }
        return null;
    }

    /**
     * 设置HTTP方法
     *
     * @param method HTTP方法
     */
    public void setMethod(String method) {
        AsyncValidationParams params = getAsyncParams();
        params.setMethod(method);
        setParams(params);
    }

    /**
     * 获取请求头
     *
     * @return 请求头
     */
    public Map<String, String> getHeaders() {
        if (getParams() instanceof AsyncValidationParams) {
            return ((AsyncValidationParams) getParams()).getHeaders();
        }
        return null;
    }

    /**
     * 设置请求头
     *
     * @param headers 请求头
     */
    public void setHeaders(Map<String, String> headers) {
        AsyncValidationParams params = getAsyncParams();
        params.setHeaders(headers);
        setParams(params);
    }

    private AsyncValidationParams getAsyncParams() {
        if (getParams() instanceof AsyncValidationParams) {
            return (AsyncValidationParams) getParams();
        }
        return new AsyncValidationParams();
    }

    /**
     * 异步验证规则参数
     */
    @Getter
    @Setter
    public static class AsyncValidationParams {
        private String url;
        private String method;
        private Map<String, String> headers;
    }
}
