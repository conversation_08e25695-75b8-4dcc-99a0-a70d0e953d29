package com.edc.vibe_engine.meta.validation;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 复合验证规则
 */
@Getter
@Setter
public class CompositeValidationRule extends ValidationRule {

    /**
     * 创建一个新的复合验证规则实例
     *
     * @param type  验证类型，必须是 ONE_OF 或 ALL_OF
     * @param rules 子规则列表
     */
    public CompositeValidationRule(ValidationRuleType type, List<ValidationRule> rules) {
        super();
        if (type != ValidationRuleType.ONE_OF && type != ValidationRuleType.ALL_OF) {
            throw new IllegalArgumentException("复合验证规则类型必须是 ONE_OF 或 ALL_OF");
        }
        setType(type);
        CompositeValidationParams params = new CompositeValidationParams();
        params.setRules(rules);
        setParams(params);
    }

    /**
     * 创建一个新的复合验证规则实例（使用字符串类型）
     *
     * @param typeValue 验证类型的字符串表示，必须是 "oneOf" 或 "allOf"
     * @param rules     子规则列表
     */
    public CompositeValidationRule(String typeValue, List<ValidationRule> rules) {
        this(ValidationRuleType.fromValue(typeValue), rules);
        if (getType() != ValidationRuleType.ONE_OF && getType() != ValidationRuleType.ALL_OF) {
            throw new IllegalArgumentException("复合验证规则类型必须是 'oneOf' 或 'allOf'");
        }
    }

    /**
     * 获取子规则列表
     *
     * @return 子规则列表
     */
    public List<ValidationRule> getRules() {
        if (getParams() instanceof CompositeValidationParams) {
            return ((CompositeValidationParams) getParams()).getRules();
        }
        return null;
    }

    /**
     * 设置子规则列表
     *
     * @param rules 子规则列表
     */
    public void setRules(List<ValidationRule> rules) {
        CompositeValidationParams params = new CompositeValidationParams();
        params.setRules(rules);
        setParams(params);
    }

    /**
     * 复合验证规则参数
     */
    @Getter
    @Setter
    public static class CompositeValidationParams {
        private List<ValidationRule> rules;
    }
}
