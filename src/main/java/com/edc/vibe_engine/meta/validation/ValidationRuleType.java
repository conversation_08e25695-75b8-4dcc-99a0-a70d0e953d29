package com.edc.vibe_engine.meta.validation;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 验证规则类型枚举
 */
public enum ValidationRuleType {
    /**
     * 必填验证
     */
    REQUIRED("required"),
    
    /**
     * 最小值/最小长度验证
     */
    MIN("min"),
    
    /**
     * 最大值/最大长度验证
     */
    MAX("max"),
    
    /**
     * 正则表达式验证
     */
    PATTERN("pattern"),
    
    /**
     * 电子邮件格式验证
     */
    EMAIL("email"),
    
    /**
     * URL格式验证
     */
    URL("url"),
    
    /**
     * 依赖字段验证
     */
    DEPENDENCY("dependency"),
    
    /**
     * 自定义验证
     */
    CUSTOM("custom"),
    
    /**
     * 异步验证
     */
    ASYNC("async"),
    
    /**
     * 满足所有子规则
     */
    ALL_OF("allOf"),
    
    /**
     * 满足任一子规则
     */
    ONE_OF("oneOf");
    
    private final String value;
    
    ValidationRuleType(String value) {
        this.value = value;
    }
    
    /**
     * 获取验证规则类型的字符串值
     * 
     * @return 验证规则类型的字符串值
     */
    @JsonValue
    public String getValue() {
        return value;
    }
    
    /**
     * 根据字符串值获取验证规则类型枚举
     * 
     * @param value 验证规则类型的字符串值
     * @return 验证规则类型枚举，如果找不到匹配的类型则返回null
     */
    public static ValidationRuleType fromValue(String value) {
        for (ValidationRuleType type : ValidationRuleType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 检查给定的字符串值是否是有效的验证规则类型
     * 
     * @param value 验证规则类型的字符串值
     * @return 如果是有效的验证规则类型则返回true，否则返回false
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
