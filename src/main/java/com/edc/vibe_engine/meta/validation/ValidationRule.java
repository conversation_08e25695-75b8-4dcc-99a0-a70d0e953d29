package com.edc.vibe_engine.meta.validation;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 验证规则
 */
@Getter
@Setter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidationRule {

    /**
     * 验证类型
     */
    private ValidationRuleType type;

    /**
     * 验证参数
     */
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "@class")
    private Object params;

    /**
     * 错误消息（支持模板语法）
     */
    private String message;

    /**
     * 启用条件表达式
     */
    private String condition;

    /**
     * 条件依赖的字段
     */
    private List<String> dependsOn;

    /**
     * 是否为异步验证
     */
    private Boolean async;

    /**
     * 获取验证类型的字符串表示
     *
     * @return 验证类型的字符串表示
     */
    @JsonIgnore
    public String getTypeValue() {
        return type != null ? type.getValue() : null;
    }

    /**
     * 根据字符串设置验证类型
     *
     * @param typeValue 验证类型的字符串表示
     */
    public void setTypeValue(String typeValue) {
        this.type = ValidationRuleType.fromValue(typeValue);
    }

    /**
     * 获取参数的Map表示
     *
     * @return 参数的Map表示
     */
    @JsonIgnore
    @SuppressWarnings("unchecked")
    public Map<String, Object> getParamsMap() {
        if (params instanceof Map) {
            return (Map<String, Object>) params;
        }
        return null;
    }

    /**
     * 设置参数的Map表示
     *
     * @param paramsMap 参数的Map表示
     */
    public void setParamsMap(Map<String, Object> paramsMap) {
        this.params = paramsMap;
    }
}
