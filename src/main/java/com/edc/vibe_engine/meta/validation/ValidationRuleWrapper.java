package com.edc.vibe_engine.meta.validation;

import com.edc.vibe_engine.meta.interfaces.IRule;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 验证规则包装类，实现IRule接口，使ValidationRule能够被RuleValidator处理
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidationRuleWrapper implements IRule {

    /**
     * 验证规则列表
     */
    private Map<String, List<ValidationRule>> rules;

    /**
     * 规则执行时机，固定为"before_save"
     */
    @Override
    public String getPoint() {
        return "before_save";
    }

    /**
     * 获取验证规则列表
     *
     * @return 验证规则列表
     */
    @JsonIgnore
    public Map<String, List<ValidationRule>> getRules() {
        return rules;
    }
}
