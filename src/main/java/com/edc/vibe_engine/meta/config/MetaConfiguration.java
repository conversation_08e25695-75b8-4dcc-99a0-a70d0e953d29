package com.edc.vibe_engine.meta.config;

import com.edc.vibe_engine.meta.annotation.MetaSource;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 元数据配置类，用于配置双引擎驱动模式
 */
@Data
@Component
@ConfigurationProperties(prefix = "vibe.meta")
public class MetaConfiguration {

    /**
     * 默认元数据来源
     */
    private MetaSource.SourceType defaultSource = MetaSource.SourceType.ANNOTATION;

    /**
     * 是否启用双引擎模式
     */
    private boolean dualEngineEnabled = true;

    /**
     * 元数据合并策略
     */
    private MergeStrategy mergeStrategy = MergeStrategy.PRIORITY_BASED;

    /**
     * 是否启用元数据缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（秒）
     */
    private long cacheExpireSeconds = 3600;

    /**
     * 是否启用元数据版本控制
     */
    private boolean versionControlEnabled = false;

    /**
     * 配置文件路径映射
     */
    private Map<String, String> configPaths = new HashMap<>();

    /**
     * 实体特定配置
     */
    private Map<String, EntityConfig> entities = new HashMap<>();

    /**
     * 元数据合并策略
     */
    public enum MergeStrategy {
        /**
         * 基于优先级合并
         */
        PRIORITY_BASED,

        /**
         * 注解优先
         */
        ANNOTATION_FIRST,

        /**
         * 配置优先
         */
        CONFIG_FIRST,

        /**
         * 深度合并
         */
        DEEP_MERGE
    }

    /**
     * 实体配置
     */
    @Data
    public static class EntityConfig {
        /**
         * 元数据来源
         */
        private MetaSource.SourceType source;

        /**
         * 配置文件路径
         */
        private String configPath;

        /**
         * 是否启用缓存
         */
        private Boolean cacheEnabled;

        /**
         * 自定义属性
         */
        private Map<String, Object> properties = new HashMap<>();
    }
}
