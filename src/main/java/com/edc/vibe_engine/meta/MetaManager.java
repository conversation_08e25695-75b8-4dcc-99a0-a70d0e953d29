package com.edc.vibe_engine.meta;

import com.edc.vibe_engine.data.idgen.IdGenerator;
import com.edc.vibe_engine.meta.constants.MetaConstants;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.def.DefRelation;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.enums.FieldFlag;
import com.edc.vibe_engine.meta.enums.RelationType;
import com.edc.vibe_engine.meta.interfaces.IRule;
import com.edc.vibe_engine.meta.manager.DualEngineMetaManager;
import com.edc.vibe_engine.meta.model.MetaDefinition;
import com.edc.vibe_engine.meta.validation.ValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRuleWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MetaManager {

    /**
     * 元数据的定义集合（保持向后兼容）
     */
    public static final Map<String, DefEntity> METADATA_MAP = new HashMap<>();

    /**
     * 双引擎元数据管理器（延迟注入避免循环依赖）
     */
    private static DualEngineMetaManager dualEngineMetaManager;

    @Autowired
    @Lazy
    public void setDualEngineMetaManager(DualEngineMetaManager dualEngineMetaManager) {
        MetaManager.dualEngineMetaManager = dualEngineMetaManager;
    }

    public static List<DefEntity> getByModule(String module) {
        // 优先使用双引擎管理器
        if (dualEngineMetaManager != null) {
            return dualEngineMetaManager.getMetaDefinitionsByModule(module).stream()
                    .map(MetaDefinition::toDefEntity)
                    .toList();
        }

        // 回退到传统方式
        return METADATA_MAP.values().stream()
                .filter(e -> StringUtils.equals(e.getModule(), module))
                .toList();
    }

    public static List<DefEntity> getAll() {
        // 优先使用双引擎管理器
        if (dualEngineMetaManager != null) {
            return dualEngineMetaManager.getAllMetaDefinitions().stream()
                    .map(MetaDefinition::toDefEntity)
                    .toList();
        }

        // 回退到传统方式
        return List.copyOf(METADATA_MAP.values());
    }

    public static DefEntity get(String entityName) {
        // 优先使用双引擎管理器
        if (dualEngineMetaManager != null) {
            return dualEngineMetaManager.getDefEntity(entityName);
        }

        // 回退到传统方式
        return METADATA_MAP.get(entityName);
    }

    public static DefEntity registerEntity(DefEntity.DefEntityBuilder builder) {
        basisFields().forEach(builder::field);
        basisRelations().forEach(builder::relation);
        final DefEntity defEntity = builder.build();
        // log.info("registerEntity:" + JSON.toJSON(defEntity));
        registerEntity(defEntity);

        return defEntity;
    }

    public static void registerEntity(DefEntity defEntity) {
        if (StringUtils.isEmpty(defEntity.getId())) {
            defEntity.setId(IdGenerator.nextSnowId());
        }
        final List<DefField> fields = new ArrayList<>(defEntity.getFields());
        fields.sort(Comparator.comparingInt(f -> {
            if (f.getName().equals(MetaConstants.ID)) {
                return -1;
            } else if (f.getName().startsWith("_")) {
                return 1;
            } else {
                return 0;
            }
        }));
        for (DefField field : fields) {
            if (StringUtils.isEmpty(field.getId())) {
                field.setId(IdGenerator.nextSnowId());
            }
        }
        defEntity.setFields(fields);
        for (DefRelation relation : defEntity.getRelations()) {
            if (StringUtils.isEmpty(relation.getId())) {
                relation.setId(IdGenerator.nextSnowId());
            }
        }

        // 合并表单验证规则
        List<IRule> rules = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(defEntity.getRules())) {
            rules.addAll(defEntity.getRules());
        }
        final ValidationRuleWrapper ruleWrapper = buildValidationRule(defEntity);
        rules.add(ruleWrapper);
        defEntity.setRules(rules);

        String key = defEntity.getName();
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("实体定义的模块和名称不能为空");
        }
        METADATA_MAP.put(key, defEntity);
    }

    /**
     * 从实体定义创建验证规则包装器
     *
     * @param entity 实体定义
     * @return 验证规则包装器
     */
    private static ValidationRuleWrapper buildValidationRule(DefEntity entity) {
        Map<String, List<ValidationRule>> rules = new HashMap<>();

        // 收集所有字段的验证规则
        if (entity.getFields() != null) {
            for (DefField field : entity.getFields()) {
                if (field.getValidationRules() != null && !field.getValidationRules().isEmpty()) {
                    rules.put(field.getName(), field.getValidationRules());
                }
            }
        }

        // 创建验证规则包装器
        return ValidationRuleWrapper.builder()
                .rules(rules)
                .build();
    }

    public static List<DefField> basisFields() {
        return List.of(
                DefField.builder().name(MetaConstants.ID).displayName("主键").dataType(DataType.VARCHAR)
                        .flag(FieldFlag.PRIMARY).build(),
                DefField.builder().name(MetaConstants.CREATE_BY).displayName("创建人").dataType(DataType.VARCHAR).build(),
                DefField.builder().name(MetaConstants.CREATE_AT).displayName("创建时间").dataType(DataType.DATE_TIME)
                        .build(),
                DefField.builder().name(MetaConstants.UPDATE_BY).displayName("修改人").dataType(DataType.VARCHAR).build(),
                DefField.builder().name(MetaConstants.UPDATE_AT).displayName("修改时间").dataType(DataType.DATE_TIME)
                        .build());
    }

    public static List<DefRelation> basisRelations() {
        return List.of(
                DefRelation.builder().name("_creator").displayName("创建人").type(RelationType.REF_ONE)
                        .refField(MetaConstants.CREATE_BY).module("base").entity("employee").build(),
                DefRelation.builder().name("_updater").displayName("修改人").type(RelationType.REF_ONE)
                        .refField(MetaConstants.UPDATE_BY).module("base").entity("employee").build());
    }

}
