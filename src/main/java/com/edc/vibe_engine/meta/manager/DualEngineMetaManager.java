package com.edc.vibe_engine.meta.manager;

import com.edc.vibe_engine.meta.annotation.MetaSource;
import com.edc.vibe_engine.meta.config.MetaConfiguration;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.interfaces.IMetaProvider;
import com.edc.vibe_engine.meta.model.MetaDefinition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 双引擎元数据管理器
 * 统一管理注解驱动和配置驱动的元数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DualEngineMetaManager {

    private final MetaConfiguration metaConfiguration;
    private final List<IMetaProvider> metaProviders;

    /**
     * 元数据缓存
     */
    private final Map<String, MetaDefinition> metaCache = new ConcurrentHashMap<>();

    /**
     * 元数据版本缓存
     */
    private final Map<String, String> versionCache = new ConcurrentHashMap<>();

    /**
     * 获取元数据定义
     *
     * @param entityName 实体名称
     * @return 元数据定义
     */
    public MetaDefinition getMetaDefinition(String entityName) {
        // 检查缓存
        if (metaConfiguration.isCacheEnabled() && metaCache.containsKey(entityName)) {
            return metaCache.get(entityName);
        }

        // 从各个提供者获取元数据
        List<MetaDefinition> definitions = new ArrayList<>();
        for (IMetaProvider provider : metaProviders) {
            MetaDefinition definition = provider.getMetaDefinition(entityName);
            if (definition != null) {
                definitions.add(definition);
            }
        }

        if (definitions.isEmpty()) {
            return null;
        }

        // 合并元数据
        MetaDefinition mergedDefinition = mergeMetaDefinitions(definitions);

        // 缓存结果
        if (metaConfiguration.isCacheEnabled()) {
            metaCache.put(entityName, mergedDefinition);
        }

        return mergedDefinition;
    }

    /**
     * 获取DefEntity（兼容现有API）
     *
     * @param entityName 实体名称
     * @return DefEntity
     */
    public DefEntity getDefEntity(String entityName) {
        MetaDefinition definition = getMetaDefinition(entityName);
        return definition != null ? definition.toDefEntity() : null;
    }

    /**
     * 获取所有元数据定义
     *
     * @return 所有元数据定义
     */
    public List<MetaDefinition> getAllMetaDefinitions() {
        Map<String, List<MetaDefinition>> allDefinitions = new HashMap<>();

        // 从所有提供者收集元数据
        for (IMetaProvider provider : metaProviders) {
            List<MetaDefinition> definitions = provider.getAllMetaDefinitions();
            for (MetaDefinition definition : definitions) {
                allDefinitions.computeIfAbsent(definition.getName(), k -> new ArrayList<>())
                        .add(definition);
            }
        }

        // 合并同名实体的元数据
        List<MetaDefinition> result = new ArrayList<>();
        for (Map.Entry<String, List<MetaDefinition>> entry : allDefinitions.entrySet()) {
            MetaDefinition merged = mergeMetaDefinitions(entry.getValue());
            result.add(merged);
        }

        return result;
    }

    /**
     * 按模块获取元数据定义
     *
     * @param module 模块名称
     * @return 模块下的元数据定义列表
     */
    public List<MetaDefinition> getMetaDefinitionsByModule(String module) {
        return getAllMetaDefinitions().stream()
                .filter(def -> module.equals(def.getModule()))
                .toList();
    }

    /**
     * 注册元数据定义
     *
     * @param definition 元数据定义
     */
    public void registerMetaDefinition(MetaDefinition definition) {
        // 清除缓存
        if (metaConfiguration.isCacheEnabled()) {
            metaCache.remove(definition.getName());
        }

        // 通知所有提供者
        for (IMetaProvider provider : metaProviders) {
            if (provider.supports(definition.getSource())) {
                provider.registerMetaDefinition(definition);
            }
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        metaCache.clear();
        versionCache.clear();
        log.info("Meta cache cleared");
    }

    /**
     * 清除指定实体的缓存
     *
     * @param entityName 实体名称
     */
    public void clearCache(String entityName) {
        metaCache.remove(entityName);
        versionCache.remove(entityName);
        log.info("Meta cache cleared for entity: {}", entityName);
    }

    /**
     * 合并多个元数据定义
     *
     * @param definitions 元数据定义列表
     * @return 合并后的元数据定义
     */
    private MetaDefinition mergeMetaDefinitions(List<MetaDefinition> definitions) {
        if (definitions.isEmpty()) {
            return null;
        }

        if (definitions.size() == 1) {
            return definitions.get(0);
        }

        // 根据配置的合并策略进行合并
        return switch (metaConfiguration.getMergeStrategy()) {
            case PRIORITY_BASED -> mergePriorityBased(definitions);
            case ANNOTATION_FIRST -> mergeAnnotationFirst(definitions);
            case CONFIG_FIRST -> mergeConfigFirst(definitions);
            case DEEP_MERGE -> mergeDeep(definitions);
        };
    }

    /**
     * 基于优先级的合并策略
     */
    private MetaDefinition mergePriorityBased(List<MetaDefinition> definitions) {
        // 按优先级排序，优先级高的在前
        definitions.sort(Comparator.comparingInt(MetaDefinition::getPriority).reversed());

        MetaDefinition base = definitions.get(0);
        MetaDefinition.MetaDefinitionBuilder builder = base.toBuilder();

        // 合并其他定义
        for (int i = 1; i < definitions.size(); i++) {
            MetaDefinition other = definitions.get(i);
            mergeInto(builder, other);
        }

        return builder.build();
    }

    /**
     * 注解优先的合并策略
     */
    private MetaDefinition mergeAnnotationFirst(List<MetaDefinition> definitions) {
        MetaDefinition annotationDef = definitions.stream()
                .filter(def -> def.getSource() == MetaSource.SourceType.ANNOTATION)
                .findFirst()
                .orElse(definitions.get(0));

        MetaDefinition.MetaDefinitionBuilder builder = annotationDef.toBuilder();

        // 用其他定义补充
        for (MetaDefinition other : definitions) {
            if (other != annotationDef) {
                mergeInto(builder, other);
            }
        }

        return builder.build();
    }

    /**
     * 配置优先的合并策略
     */
    private MetaDefinition mergeConfigFirst(List<MetaDefinition> definitions) {
        MetaDefinition configDef = definitions.stream()
                .filter(def -> def.getSource() == MetaSource.SourceType.CONFIG)
                .findFirst()
                .orElse(definitions.get(0));

        MetaDefinition.MetaDefinitionBuilder builder = configDef.toBuilder();

        // 用其他定义补充
        for (MetaDefinition other : definitions) {
            if (other != configDef) {
                mergeInto(builder, other);
            }
        }

        return builder.build();
    }

    /**
     * 深度合并策略
     */
    private MetaDefinition mergeDeep(List<MetaDefinition> definitions) {
        // 实现深度合并逻辑
        // 这里可以实现更复杂的合并策略，比如字段级别的合并
        return mergePriorityBased(definitions);
    }

    /**
     * 将other的属性合并到builder中
     */
    private void mergeInto(MetaDefinition.MetaDefinitionBuilder builder, MetaDefinition other) {
        // 这里实现具体的合并逻辑
        // 可以根据需要决定哪些属性需要合并，哪些需要覆盖
        if (other.getDisplayName() != null) {
            builder.displayName(other.getDisplayName());
        }
        if (other.getDescription() != null) {
            builder.description(other.getDescription());
        }
        // 可以继续添加其他属性的合并逻辑
    }
}
