package com.edc.vibe_engine.meta.model;

import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.enums.FieldFlag;
import com.edc.vibe_engine.meta.validation.ValidationRule;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 字段定义模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FieldDefinition {

    /**
     * 字段ID
     */
    private String id;

    /**
     * 字段名称
     */
    private String name;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 数据类型
     */
    private DataType dataType;

    /**
     * 字段长度
     */
    private Integer size;

    /**
     * 小数位数
     */
    private Integer digits;

    /**
     * 字段标志
     */
    private List<FieldFlag> flags;

    /**
     * 包装字段
     */
    private String wrap;

    /**
     * 默认值
     */
    private Object defaultValue;

    /**
     * 选项列表
     */
    private List<OptionDefinition> options;

    /**
     * 验证规则
     */
    private List<ValidationRule> validationRules;

    /**
     * UI控件配置
     */
    private UIControlDefinition uiControl;

    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;

    /**
     * 转换为DefField
     */
    public DefField toDefField() {
        DefField.DefFieldBuilder builder = DefField.builder()
                .id(this.id)
                .name(this.name)
                .displayName(this.displayName)
                .dataType(this.dataType)
                .wrap(this.wrap);

        if (this.size != null) {
            builder.size(this.size);
        }

        if (this.digits != null) {
            builder.digits(this.digits);
        }

        if (this.flags != null) {
            builder.flags(this.flags);
        }

        if (this.defaultValue != null) {
            builder.defaultValue(this.defaultValue);
        }

        if (this.options != null) {
            builder.options(this.options.stream()
                    .map(OptionDefinition::toDefOption)
                    .toList());
        }

        if (this.validationRules != null) {
            builder.validationRules(this.validationRules);
        }

        return builder.build();
    }

    /**
     * 从DefField创建FieldDefinition
     */
    public static FieldDefinition fromDefField(DefField defField) {
        FieldDefinitionBuilder builder = FieldDefinition.builder()
                .id(defField.getId())
                .name(defField.getName())
                .displayName(defField.getDisplayName())
                .dataType(defField.getDataType())
                .size(defField.getSize())
                .digits(defField.getDigits())
                .wrap(defField.getWrap())
                .defaultValue(defField.getDefaultValue())
                .flags(defField.getFlags());

        if (defField.getOptions() != null) {
            builder.options(defField.getOptions().stream()
                    .map(OptionDefinition::fromDefOption)
                    .toList());
        }

        if (defField.getValidationRules() != null) {
            builder.validationRules(defField.getValidationRules());
        }

        return builder.build();
    }
}
