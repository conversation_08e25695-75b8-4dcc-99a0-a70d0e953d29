package com.edc.vibe_engine.meta.model;

import com.edc.vibe_engine.meta.annotation.MetaSource;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统一的元数据定义模型，兼容注解和配置两种驱动方式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MetaDefinition {

    /**
     * 元数据ID
     */
    private String id;

    /**
     * 元数据名称
     */
    private String name;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 模块名称
     */
    private String module;

    /**
     * 描述
     */
    private String description;

    /**
     * 元数据来源
     */
    private MetaSource.SourceType source;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 版本号
     */
    private String version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 实体定义
     */
    private EntityDefinition entity;

    /**
     * 字段定义列表
     */
    private List<FieldDefinition> fields;

    /**
     * 关联定义列表
     */
    private List<RelationDefinition> relations;

    /**
     * 规则定义列表
     */
    private List<RuleDefinition> rules;

    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;

    /**
     * 转换为DefEntity
     */
    public DefEntity toDefEntity() {
        DefEntity.DefEntityBuilder builder = DefEntity.builder()
                .id(this.id)
                .name(this.name)
                .displayName(this.displayName)
                .module(this.module)
                .description(this.description);

        if (this.entity != null) {
            builder.type(this.entity.getType());
        }

        if (this.fields != null) {
            this.fields.forEach(field -> builder.field(field.toDefField()));
        }

        if (this.relations != null) {
            this.relations.forEach(relation -> builder.relation(relation.toDefRelation()));
        }

        if (this.rules != null) {
            this.rules.forEach(rule -> builder.rule(rule.toIRule()));
        }

        return builder.build();
    }

    /**
     * 从DefEntity创建MetaDefinition
     */
    public static MetaDefinition fromDefEntity(DefEntity defEntity) {
        MetaDefinitionBuilder builder = MetaDefinition.builder()
                .id(defEntity.getId())
                .name(defEntity.getName())
                .displayName(defEntity.getDisplayName())
                .module(defEntity.getModule())
                .description(defEntity.getDescription())
                .source(MetaSource.SourceType.ANNOTATION);

        if (defEntity.getType() != null) {
            builder.entity(EntityDefinition.builder()
                    .type(defEntity.getType())
                    .build());
        }

        if (defEntity.getFields() != null) {
            builder.fields(defEntity.getFields().stream()
                    .map(FieldDefinition::fromDefField)
                    .toList());
        }

        if (defEntity.getRelations() != null) {
            builder.relations(defEntity.getRelations().stream()
                    .map(RelationDefinition::fromDefRelation)
                    .toList());
        }

        if (defEntity.getRules() != null) {
            builder.rules(defEntity.getRules().stream()
                    .map(RuleDefinition::fromIRule)
                    .toList());
        }

        return builder.build();
    }
}
