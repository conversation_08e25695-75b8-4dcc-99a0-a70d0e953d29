package com.edc.vibe_engine.meta.model;

import com.edc.vibe_engine.meta.interfaces.IRule;
import com.edc.vibe_engine.meta.validation.ValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRuleWrapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 规则定义模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleDefinition {

    /**
     * 规则ID
     */
    private String id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则类型
     */
    private String type;

    /**
     * 执行时机
     */
    private String point;

    /**
     * 规则参数
     */
    private Map<String, Object> params;

    /**
     * 验证规则（当type为validation时使用）
     */
    private Map<String, List<ValidationRule>> validationRules;

    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;

    /**
     * 转换为IRule
     */
    public IRule toIRule() {
        if ("validation".equals(this.type)) {
            return ValidationRuleWrapper.builder()
                    .rules(this.validationRules)
                    .build();
        }

        // 其他类型的规则可以在这里扩展
        throw new UnsupportedOperationException("Unsupported rule type: " + this.type);
    }

    /**
     * 从IRule创建RuleDefinition
     */
    public static RuleDefinition fromIRule(IRule rule) {
        RuleDefinitionBuilder builder = RuleDefinition.builder()
                .point(rule.getPoint());

        if (rule instanceof ValidationRuleWrapper) {
            ValidationRuleWrapper wrapper = (ValidationRuleWrapper) rule;
            builder.type("validation")
                    .validationRules(wrapper.getRules());
        }

        return builder.build();
    }
}
