package com.edc.vibe_engine.meta.support;

import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import com.edc.vibe_engine.data.schema.types.DBField;
import com.edc.vibe_engine.data.schema.types.DBTable;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.enums.FieldFlag;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

public class MetaDBUtils {

    public static DBTable convertDBTable(DefEntity entity) {
        final DBTable.DBTableBuilder tableBuilder = DBTable.builder()
                .code(entity.getId())
                .name(entity.fullTableName())
                //                .primaryKey(entity.primaryField().getName())
                .comment(entity.getDescription());

        Set<String> fieldNames = new HashSet<>();
        Set<String> primaryKeys = new LinkedHashSet<>();
        for (DefField field : entity.getFields()) {
            final DBField dbField = convertDBField(field);
            AssertUtils.assertFalse(fieldNames.contains(dbField.getName()), "实体:{}[{}], 字段:{}[{}] 重复！",
                    entity.getDisplayName(), entity.getName(),
                    field.getDisplayName(), field.getName()
            );
            fieldNames.add(dbField.getName());
            tableBuilder.field(dbField);
            if (field.getFlags().contains(FieldFlag.PRIMARY)) {
                primaryKeys.add(dbField.getName());
            }
        }
        tableBuilder.primaryKey(String.join(StringConstants.COMMA, primaryKeys));
        return tableBuilder.build();
    }

    public static DBField convertDBField(DefField bizField) {
        final DataType defFieldType = bizField.getDataType();
        final DBFieldType sqlType = defFieldType.getSqlType();
        final String defaultValue = bizField.getDefaultValue();

        final DBField.DBFieldBuilder fieldBuilder = DBField.builder()
                .name(bizField.getName())
                .comment(bizField.getDisplayName())
                .type(sqlType)
                .allowNull(defFieldType.getSafeValue() == null)
                .defaultValue(defaultValue);

        //        final SizeParam sizeParam = bizField.getBizControlType().getSizeParam();
        //        if (sizeParam != null) {
        //            if (!sizeParam.hasSize()) {
        //                fieldBuilder.size(bizField.getDataType().hasSize() ? bizField.getSize() : 0);
        //            } else {
        //                final Size size = sizeParam.getSize();
        //                fieldBuilder.size(bizField.getSize() != 0 ? Math.min(Math.max(size.getMin(), bizField.getSize()), size.getMax()) : size.getDefaultSize());
        //            }
        //            if (!sizeParam.hasDigit()) {
        //                fieldBuilder.numDigits(0);
        //            } else {
        //                final Digit digit = sizeParam.getDigit();
        //                // 小数的0可能是业务传入，故不能以此判空取默认
        //                fieldBuilder.numDigits(Math.min(Math.max(digit.getMin(), bizField.getNumDigits()), digit.getMax()));
        //            }
        //            if (sizeParam.hasSize() && sizeParam.hasDigit()) {
        //                fieldBuilder.size(dbField.getSize() + dbField.getNumDigits());
        //            }
        //        } else {
        //            fieldBuilder.size(bizField.getDataType().hasSize() ? bizField.getSize() : 0);
        //            fieldBuilder.numDigits(bizField.getDataType().hasDigit() ? bizField.getNumDigits() : 0);
        //        }
        if (sqlType.isText()) {
            fieldBuilder.defaultValue(defaultValue);
        } else if (sqlType.isNumeric()) {
            if (StringUtils.isNotEmpty(defaultValue)) {
                try {
                    new BigDecimal(defaultValue);
                    fieldBuilder.defaultValue(defaultValue);
                } catch (Exception ignored) {

                }
            }
        } else if (sqlType.isNumber()) {
            if (StringUtils.isEmpty(defaultValue) || !NumberUtils.isDigits(defaultValue)) {
                fieldBuilder.defaultValue("0");
            } else {
                fieldBuilder.defaultValue(defaultValue);
            }
        } else if (sqlType == DBFieldType.BOOLEAN) {
            fieldBuilder.defaultValue(StringUtils.defaultIfEmpty(defaultValue, "false"));
        }

        //        dbField.setAutoIncrement(bizField.getDataType() == LibDataTypeEnum.AUTO_INCREMENT || bizField.getDataType() == LibDataTypeEnum.AUTO_INCREMENT_64);
        // path 字段特殊处理字符集，解决索引长度不足
        //        if (Objects.equals(dbField.getName(), FieldNameConstants.PATH)) {
        //            dbField.setCollate("latin1_general_ci");
        //        }

        return fieldBuilder.build();
    }

}
