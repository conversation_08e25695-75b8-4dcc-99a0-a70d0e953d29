package com.edc.vibe_engine.meta.control;

import java.util.List;

/**
 * 过滤条件
 */
public class FilterConfig {
    private String condition;         // 条件表达式
    private List<String> dependsOn;   // 依赖字段

    public FilterConfig() {
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public List<String> getDependsOn() {
        return dependsOn;
    }

    public void setDependsOn(List<String> dependsOn) {
        this.dependsOn = dependsOn;
    }
}
