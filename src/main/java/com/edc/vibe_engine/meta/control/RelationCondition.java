package com.edc.vibe_engine.meta.control;

import com.edc.vibe_engine.meta.enums.FormModeType;
import com.edc.vibe_engine.meta.enums.RelationConditionType;

import java.util.List;

/**
 * 关联条件
 */
public class RelationCondition {
    private RelationConditionType type;      // 条件类型
    private String expression;               // 条件表达式
    private List<String> roles;              // 角色列表
    private List<FormModeType> modes;            // 模式列表

    public RelationCondition() {
    }

    public RelationCondition(RelationConditionType type) {
        this.type = type;
    }

    public RelationConditionType getType() {
        return type;
    }

    public void setType(RelationConditionType type) {
        this.type = type;
    }

    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<FormModeType> getModes() {
        return modes;
    }

    public void setModes(List<FormModeType> modes) {
        this.modes = modes;
    }
}
