package com.edc.vibe_engine.meta.control;

import lombok.Data;

import java.util.List;

/**
 * 关联控制配置
 */
@Data
public class RelationControl {

    private RelationCondition condition;      // 关联条件

    private String valueField;                // 值字段，默认为_id
    private String labelField;                // 标签字段，默认为name
    private List<String> displayFields;       // 显示字段列表
    private Boolean multiple;                 // 是否多选
    //    private CascadeConfig cascade;            // 级联配置
    //    private FilterConfig filter;              // 过滤条件
    private Boolean searchable;               // 是否可搜索
    private Boolean createable;               // 是否可创建
    private Boolean previewable;              // 是否可预览
}
