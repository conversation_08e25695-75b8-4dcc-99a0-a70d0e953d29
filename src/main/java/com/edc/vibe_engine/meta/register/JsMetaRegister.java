package com.edc.vibe_engine.meta.register;

import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.def.DefRelation;
import com.edc.vibe_engine.meta.def.DefUniqRule;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.enums.RelationType;
import com.edc.vibe_engine.meta.interfaces.IMetaRegister;
import com.edc.vibe_engine.script.JS;
import lombok.extern.slf4j.Slf4j;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Source;
import org.graalvm.polyglot.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * JS元数据注册器
 * 从classpath下的JS文件中读取元数据定义并注册到MetaManager
 */
@Slf4j
//@Component
public class JsMetaRegister implements IMetaRegister {

    private static final String JS_METADATA_PATH = "classpath*:metadata/**/*.js";

    @Override
    public void register() {
        try {
            // 获取所有JS文件
            Resource[] resources = new PathMatchingResourcePatternResolver().getResources(JS_METADATA_PATH);
            log.info("Found {} JS metadata files", resources.length);

            // 打印所有找到的资源
            for (Resource resource : resources) {
                log.info("Found JS file: {}", resource.getFilename());
            }

            if (resources.length == 0) {
                log.warn("No JS metadata files found with pattern: {}", JS_METADATA_PATH);
                // 尝试列出classpath根目录下的所有资源
                Resource[] rootResources = new PathMatchingResourcePatternResolver().getResources("classpath:*");
                log.info("Found {} resources in classpath root", rootResources.length);
                for (Resource resource : rootResources) {
                    log.info("Root resource: {}", resource.getFilename());
                }
                return;
            }

            JS.withContext(context -> {
                // 处理每个JS文件
                for (Resource resource : resources) {
                    try {
                        log.info("Processing JS file: {}", resource.getFilename());
                        registerJsMetadata(context, resource);
                    } catch (Exception e) {
                        log.error("Failed to register JS metadata from {}", resource.getFilename(), e);
                    }
                }
            });

            //            // 创建JS执行上下文
            //            try (Context context = Context.newBuilder("js")
            //                    .allowAllAccess(true)
            //                    .option("js.esm-eval-returns-exports", "true")
            //                    .build()) {
            //
            //
            //            }
        } catch (IOException e) {
            log.error("Failed to load JS metadata files", e);
        }
    }

    /**
     * 注册单个JS文件中的元数据
     */
    private void registerJsMetadata(Context context, Resource resource) throws IOException {
        log.info("Processing JS metadata file: {}", resource.getFilename());

        // 读取JS文件内容
        String filename = resource.getFilename();
        try (InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            // 读取文件内容
            StringBuilder contentBuilder = new StringBuilder();
            char[] buffer = new char[1024];
            int bytesRead;
            while ((bytesRead = reader.read(buffer)) != -1) {
                contentBuilder.append(buffer, 0, bytesRead);
            }
            String content = contentBuilder.toString();

            log.debug("JS file content: {}", content);

            try {
                // 尝试不同的方式解析JS文件
                // 尝试作为ES模块解析
                Source source = JS.source(content, filename);
                Value result = context.eval(source);
                log.info("Successfully parsed {} as ES module", filename);

                if (result != null && result.hasMembers()) {
                    // ES模块格式: export default { ... }
                    Value defaultExport = result.getMember("default");
                    log.info("Found default export in {}", filename);

                    // 处理元数据
                    if (defaultExport.hasMembers() && defaultExport.hasMember("metadata")) {
                        Value metadata = defaultExport.getMember("metadata");
                        log.info("Found metadata in default export of {}", filename);
                        processMetadata(metadata);
                    } else {
                        log.warn("No metadata found in default export of {}", filename);
                    }
                } else {
                    log.warn("No valid export found in JS file: {}", filename);
                }
            } catch (Exception e) {
                log.error("Error evaluating JS file: {}", filename, e);
            }
        }
    }

    /**
     * 处理元数据对象
     */
    private void processMetadata(Value metadata) {
        if (!metadata.hasMembers()) {
            log.warn("Invalid metadata format: not an object");
            return;
        }

        try {
            // 直接从Value对象获取属性
            String name = getStringMember(metadata, "name");
            String displayName = getStringMember(metadata, "display_name");
            String module = getStringMember(metadata, "module");

            if (name == null || module == null) {
                log.warn("Invalid metadata: missing required fields (name, module)");
                return;
            }

            log.info("Processing metadata for entity: {}.{}", module, name);

            // 构建DefEntity
            DefEntity.DefEntityBuilder builder = DefEntity.builder()
                    .name(name)
                    .displayName(displayName)
                    .module(module);

            // 处理字段
            if (metadata.hasMember("fields") && metadata.getMember("fields").hasArrayElements()) {
                Value fields = metadata.getMember("fields");
                long fieldsCount = fields.getArraySize();
                log.info("Found {} fields", fieldsCount);

                for (int i = 0; i < fieldsCount; i++) {
                    Value field = fields.getArrayElement(i);
                    DefField defField = createField(field);
                    if (defField != null) {
                        builder.field(defField);
                    }
                }
            }

            // 处理关系
            if (metadata.hasMember("relations") && metadata.getMember("relations").hasArrayElements()) {
                Value relations = metadata.getMember("relations");
                long relationsCount = relations.getArraySize();
                log.info("Found {} relations", relationsCount);

                for (int i = 0; i < relationsCount; i++) {
                    Value relation = relations.getArrayElement(i);
                    DefRelation defRelation = createRelation(relation);
                    if (defRelation != null) {
                        builder.relation(defRelation);
                    }
                }
            }

            // 处理唯一性规则
            if (metadata.hasMember("rules") && metadata.getMember("rules").hasArrayElements()) {
                Value rules = metadata.getMember("rules");
                long rulesCount = rules.getArraySize();
                log.info("Found {} rules", rulesCount);

                for (int i = 0; i < rulesCount; i++) {
                    Value rule = rules.getArrayElement(i);
                    if (rule.hasMember("fields") && rule.getMember("fields").hasArrayElements()) {
                        Value fieldsValue = rule.getMember("fields");
                        List<String> fields = new ArrayList<>();

                        for (int j = 0; j < fieldsValue.getArraySize(); j++) {
                            Value fieldValue = fieldsValue.getArrayElement(j);
                            if (fieldValue.isString()) {
                                fields.add(fieldValue.asString());
                            }
                        }

                        if (!fields.isEmpty()) {
                            builder.rule(new DefUniqRule(fields));
                        }
                    }
                }
            }

            // 注册到MetaManager
            MetaManager.registerEntity(builder);
            log.info("Registered entity: {}.{}", module, name);
        } catch (Exception e) {
            log.error("Failed to process metadata", e);
        }
    }

    /**
     * 获取Value对象的字符串属性
     */
    private String getStringMember(Value value, String key) {
        if (value.hasMember(key)) {
            Value member = value.getMember(key);
            if (member.isString()) {
                return member.asString();
            }
        }
        return null;
    }

    /**
     * 创建DefField
     */
    private DefField createField(Value fieldValue) {
        String name = getStringMember(fieldValue, "name");
        String displayName = getStringMember(fieldValue, "display_name");
        String type = getStringMember(fieldValue, "type");

        if (name == null || type == null) {
            log.warn("Invalid field: missing required fields (name, type)");
            return null;
        }

        try {
            DataType dataType = DataType.valueOf(type);
            return DefField.builder()
                    .name(name)
                    .displayName(displayName)
                    .dataType(dataType)
                    .build();
        } catch (IllegalArgumentException e) {
            log.warn("Invalid data type: {}", type);
            return DefField.builder()
                    .name(name)
                    .displayName(displayName)
                    .dataType(DataType.VARCHAR)
                    .build();
        }
    }

    /**
     * 创建DefRelation
     */
    private DefRelation createRelation(Value relationValue) {
        String name = getStringMember(relationValue, "name");
        String displayName = getStringMember(relationValue, "display_name");
        String entity = getStringMember(relationValue, "entity");
        String module = getStringMember(relationValue, "module");
        String type = getStringMember(relationValue, "type");

        if (name == null || entity == null || module == null || type == null) {
            log.warn("Invalid relation: missing required fields");
            return null;
        }

        try {
            return DefRelation.builder()
                    .name(name)
                    .displayName(displayName)
                    //                    .field(field)
                    .entity(entity)
                    .module(module)
                    .type(RelationType.valueOf(type))
                    .build();
        } catch (IllegalArgumentException e) {
            log.warn("Invalid relation type: {}", type);
            return null;
        }
    }
}
