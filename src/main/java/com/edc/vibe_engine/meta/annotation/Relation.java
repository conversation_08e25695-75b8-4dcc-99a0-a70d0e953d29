package com.edc.vibe_engine.meta.annotation;

import com.edc.vibe_engine.meta.enums.RelationType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Relation {

    String name() default "";

    String displayName() default "";

    /**
     * @return 关联目标模块，默认与当前对象同一模块
     */
    String module() default "";

    /**
     * @return 关联目标实体
     */
    String entity();

    RelationType type();

    String refField() default "";

    /**
     * @return 启用关联的条件， js表达式
     */
    String usingIf() default "";

    /**
     * @return 字段映射
     */
    JoinMapping[] joinMapping() default {};


    @Documented
    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface JoinMapping {

        /**
         * @return 关联目标字段
         */
        String field();

        /**
         * @return 字段值表达式
         */
        String valExp() default "";


        /**
         * @return 关联条件表达式
         */
        String conditionExp() default "";

        /**
         * @return 可更新
         */
        boolean updatable() default true;
    }

}
