package com.edc.vibe_engine.meta.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 元数据来源注解，用于标识元数据的来源和优先级
 */
@Documented
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface MetaSource {

    /**
     * @return 元数据来源类型
     */
    SourceType source() default SourceType.ANNOTATION;

    /**
     * @return 优先级，数值越大优先级越高
     */
    int priority() default 0;

    /**
     * @return 是否允许被配置覆盖
     */
    boolean overridable() default true;

    /**
     * @return 配置文件路径（当source为CONFIG时使用）
     */
    String configPath() default "";

    /**
     * 元数据来源类型
     */
    enum SourceType {
        /**
         * 注解驱动
         */
        ANNOTATION,

        /**
         * 配置文件驱动
         */
        CONFIG,

        /**
         * 数据库驱动
         */
        DATABASE,

        /**
         * API驱动
         */
        API,

        /**
         * 混合模式
         */
        HYBRID
    }
}
