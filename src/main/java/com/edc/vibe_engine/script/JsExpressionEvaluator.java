package com.edc.vibe_engine.script;

import com.edc.vibe_engine.record.interfaces.IRecord;
import lombok.extern.slf4j.Slf4j;
import org.graalvm.polyglot.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * JavaScript表达式求值器，用于执行条件表达式和自定义验证表达式
 */
@Slf4j
@Component
public class JsExpressionEvaluator {

    /**
     * 执行自定义验证表达式
     *
     * @param expression 验证表达式
     * @param value      字段值
     * @param record     记录对象
     * @return 验证是否通过
     */
    public boolean evalExpression(String expression, Object value, IRecord record) {
        if (expression == null || expression.trim().isEmpty()) {
            return true; // 空表达式默认为true
        }

        try {
            Map<String, Object> context = createContext(value, record);
            return JS.withContext(ctx -> {
                // 将上下文变量绑定到JavaScript环境
                Value bindings = ctx.getBindings(JS.LANGUAGE_ID);
                for (Map.Entry<String, Object> entry : context.entrySet()) {
                    bindings.putMember(entry.getKey(), entry.getValue());
                }

                // 构建JavaScript函数
                String functionBody = "return " + expression + ";";
                String functionScript = "(() => { " + functionBody + " })";

                // 执行函数
                Value function = ctx.eval(JS.LANGUAGE_ID, functionScript);
                Value result = function.execute();

                // 转换结果为布尔值
                return result.asBoolean();
            });
        } catch (Exception e) {
            log.error("Error evaluating validation expression: {}", expression, e);
            return false; // 出错时默认为false
        }
    }

    /**
     * 创建JavaScript执行上下文
     *
     * @param value  字段值
     * @param record 记录对象
     * @return 上下文Map
     */
    private Map<String, Object> createContext(Object value, IRecord record) {
        Map<String, Object> context = new HashMap<>();
        context.put("value", value);
        context.put("record", record != null ? record.toMap() : new HashMap<>());
        return context;
    }
}
