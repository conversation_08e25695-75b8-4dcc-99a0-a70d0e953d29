//package com.edc.vibe_engine.script;
//
//import org.graalvm.polyglot.Context;
//import org.graalvm.polyglot.Engine;
//import org.graalvm.polyglot.HostAccess;
//import org.graalvm.polyglot.PolyglotAccess;
//import org.graalvm.polyglot.PolyglotException;
//import org.graalvm.polyglot.Value;
//
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.CompletionStage;
//import java.util.concurrent.locks.Lock;
//import java.util.concurrent.locks.ReentrantLock;
//import java.util.function.Consumer;
//import java.util.function.Function;
//
//class ConcurrentJsExecutor {
//
//    private static final String JS = "js";
//    private static final String THEN = "then";
//    private static final String CATCH = "catch";
//
//    private final String jsCode;
//
//    /**
//     * Utility class to associate locks with polyglot contexts.
//     */
//    private static class ContextProvider {
//
//        private final Context context;
//        private final ReentrantLock lock;
//
//        ContextProvider(Context cx) {
//            this.context = cx;
//            this.lock = new ReentrantLock();
//        }
//
//        Context getContext() {
//            return context;
//        }
//
//        Lock getLock() {
//            return lock;
//        }
//    }
//
//    /**
//     * A GraalVM engine shared between multiple JavaScript contexts.
//     */
//    private final Engine sharedEngine = Engine.newBuilder().build();
//
//    /**
//     * A Thread-local used to ensure that we have one JavaScript context per
//     * Helidon thread.
//     */
//    private final ThreadLocal<ContextProvider> jsContext = ThreadLocal.withInitial(() -> {
//        /*
//         * For simplicity, allow ALL accesses. In a real application, access to resources should be restricted.
//         */
//        Context cx = Context.newBuilder(JS).allowHostAccess(HostAccess.ALL).allowPolyglotAccess(PolyglotAccess.ALL)
//                .engine(sharedEngine).build();
//        /*
//         * Register a Java method in the Context global scope as a JavaScript function.
//         */
//        ContextProvider provider = new ContextProvider(cx);
//        cx.getBindings(JS).putMember("computeFromJava", createJavaInteropComputeFunction(provider));
//        System.out.println("Created new JS context for thread " + Thread.currentThread());
//        return provider;
//    });
//
//    ConcurrentJsExecutor(String jsCode) {
//        this.jsCode = jsCode;
//    }
//
//    /**
//     * Returns the Java implementation of <code>computeFromJava</code> exposed
//     * to JavaScript.
//     */
//    private Function<?, ?> createJavaInteropComputeFunction(ContextProvider cx) {
//        /*
//         * The Java implementation of the `computeFromJava` function takes one argument
//         * as input and returns a JavaScript Promise.
//         *
//         * In Java terms, this is equivalent to returning an instance of
//         * `ComputeFromJavaFunction`.
//         */
//        return (requestId) -> (ComputeFromJavaFunction) (onResolve, onReject) -> {
//            /*
//             * Simulate async execution by submitting the actual task to a thread pool.
//             */
//            CompletableFuture.supplyAsync(() -> {
//                /*
//                 * This code will resolve or reject a JavaScript Promise that was created in another thread.
//                 * Synchronization can be used to ensure that no concurrent access can happen on JavaScript objects
//                 * that belong to the same context.
//                 */
//                cx.getLock().lock();
//                try {
//                    /*
//                     * Reject the Promise if requestId is smaller than 42.
//                     */
//                    if ((int) requestId < 42) {
//                        return onReject.execute(requestId + " is not a valid request id!");
//                    }
//                    /*
//                     * Do some random calculation using `requestId`.
//                     */
//                    double v = (int) requestId + Math.random();
//                    /*
//                     * Resolve the JavaScript Promise with the computed value. This will resume the
//                     * JavaScript `async` function execution.
//                     */
//                    return onResolve.execute(v);
//                } catch (PolyglotException e) {
//                    /*
//                     * Something went wrong. Reject the JavaScript Promise.
//                     */
//                    return onReject.execute(e.getGuestObject() == null ? e.getGuestObject() : e.getMessage());
//                } finally {
//                    cx.getLock().unlock();
//                }
//            });
//        };
//    }
//
//    /**
//     * Submit a new request to the JavaScript engine. Returns a
//     * `CompletionStage` instance that will complete when GraalVM JavaScript
//     * produces a value.
//     */
//    public CompletionStage<Object> callJavaScriptAsyncFunction(int requestId) {
//        /*
//         * Create a new future. It will be completed by the JavaScript engine as a
//         * consequence of a JavaScript Promise resolution.
//         */
//        CompletableFuture<Object> jsExecution = new CompletableFuture<>();
//        /*
//         * Helidon might use multiple threads to handle concurrent requests. To minimize the number of contexts
//         * in the application, we can use one polyglot context per thread. To this end, we use thread-locals.
//         */
//        ContextProvider cx = jsContext.get();
//        /*
//         * A concurrent thread might resolve or reject a JavaScript Promise that was created in this context.
//         * Synchronization can be used to ensure that no concurrent access can happen on objects that belong
//         * to the same polyglot context.
//         */
//        cx.getLock().lock();
//        try {
//            /*
//             * Execute the JavaScript async function. Will return a JavaScript Promise.
//             */
//            Value jsAsyncFunctionPromise = cx.getContext().eval(JS, jsCode).execute(requestId);
//            /*
//             * Register event reactions for the given Promise. The corresponding Java
//             * methods will be executed when the Promise completes.
//             */
//            jsAsyncFunctionPromise.invokeMember(THEN, (Consumer<?>) jsExecution::complete)
//                    .invokeMember(CATCH, (Consumer<Throwable>) jsExecution::completeExceptionally);
//        } catch (Throwable t) {
//            /*
//             * Something went wrong while running JavaScript.
//             */
//            jsExecution.completeExceptionally(t);
//        } finally {
//            cx.getLock().unlock();
//        }
//        return jsExecution;
//    }
//
//    /**
//     * An arbitrary "thenable" interface. Used to expose Java methods to
//     * JavaScript Promise objects.
//     */
//    @FunctionalInterface
//    public interface ComputeFromJavaFunction {
//
//        void then(Value onResolve, Value onReject);
//    }
//
//}
