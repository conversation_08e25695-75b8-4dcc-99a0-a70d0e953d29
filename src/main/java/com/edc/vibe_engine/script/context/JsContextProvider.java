//package com.edc.vibe_engine.script.context;
//
//import org.graalvm.polyglot.Context;
//import org.graalvm.polyglot.Engine;
//import org.graalvm.polyglot.HostAccess;
//import org.graalvm.polyglot.PolyglotAccess;
//
//import java.util.concurrent.locks.ReentrantLock;
//import java.util.function.Supplier;
//
//public class JsContextProvider {
//
//    private static final String JS = "js";
//    private static final String THEN = "then";
//    private static final String CATCH = "catch";
//
//    private static final Engine sharedEngine = Engine.newBuilder().build();
//
//    private final static ScopedValue<JsContextProvider> JS_CONTEXT = ScopedValue.newInstance();
//
//
//    public static <T> T with(Supplier<T> supplier) throws Throwable {
//        try (Context cx = Context.newBuilder(JS)
//                .allowAllAccess(true)
//                .allowHostAccess(HostAccess.ALL)
//                .allowPolyglotAccess(PolyglotAccess.ALL)
//                .option("js.esm-eval-returns-exports", "true")
//                .engine(sharedEngine).build()) {
//            /*
//             * Register a Java method in the Context global scope as a JavaScript function.
//             */
//            JsContextProvider provider = new JsContextProvider(cx);
//
//            return ScopedValue.where(JS_CONTEXT, provider).call(() -> supplier.get());
//        }
//    }
//
//    public static Context getContext() {
//        return JS_CONTEXT.get().context;
//    }
//
//
//    public static ReentrantLock getLock() {
//        return JS_CONTEXT.get().lock;
//    }
//
//
//    private final Context context;
//    private final ReentrantLock lock;
//
//    JsContextProvider(Context cx) {
//        this.context = cx;
//        this.lock = new ReentrantLock();
//    }
//}
