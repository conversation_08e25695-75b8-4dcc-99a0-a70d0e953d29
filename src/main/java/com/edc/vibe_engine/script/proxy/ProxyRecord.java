package com.edc.vibe_engine.script.proxy;

import com.edc.vibe_engine.record.interfaces.IModifyRecord;
import com.edc.vibe_engine.record.interfaces.IRecord;
import org.graalvm.polyglot.Value;
import org.graalvm.polyglot.proxy.ProxyObject;

public record ProxyRecord(IRecord record) implements ProxyObject {

    @Override
    public Object getMember(String key) {
        return record.get(key);
    }

    @Override
    public Object getMemberKeys() {
        return record.keySet();
    }

    @Override
    public boolean hasMember(String key) {
        return record.containsKey(key);
    }

    @Override
    public void putMember(String key, Value value) {
        ((IModifyRecord) record).set(key, value.as(Object.class));
    }
}
