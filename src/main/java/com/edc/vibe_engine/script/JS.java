package com.edc.vibe_engine.script;

import com.edc.vibe_engine.script.proxy.ProxyFactory;
import org.apache.commons.collections4.MapUtils;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Engine;
import org.graalvm.polyglot.HostAccess;
import org.graalvm.polyglot.PolyglotAccess;
import org.graalvm.polyglot.Source;
import org.graalvm.polyglot.SourceSection;
import org.graalvm.polyglot.Value;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;

public class JS {

    public static final String LANGUAGE_ID = "js";

    private static final Engine sharedEngine = Engine.newBuilder()
            .allowExperimentalOptions(true)
            .build();

    /**
     * 表示 JavaScript 函数的包装类
     */
    public record JsFunction(String source) {

        /**
         * 执行函数
         *
         * @param args 函数参数
         * @return 函数执行结果
         */
        public Object execute(Context context, Object... args) {
            Value functionValue = context.eval(LANGUAGE_ID, source);
            if (functionValue != null && functionValue.canExecute()) {
                Value result = functionValue.execute(args);
                return toJava(result);
            }
            throw new IllegalStateException("Function cannot be executed (may be deserialized)");
        }

    }

    public static void withContext(Consumer<Context> consumer) {
        withContext(Map.of(), consumer);
    }

    public static void withContext(Map<String, Object> bindObject, Consumer<Context> consumer) {
        withContext(bindObject, ctx -> {
            consumer.accept(ctx);
            return null;
        });
    }

    public static <T> T withContext(Function<Context, T> function) {
        return withContext(Map.of(), function);
    }

    public static <T> T withContext(Map<String, Object> bindObject, Function<Context, T> function) {
        try (Context ctx = Context.newBuilder(LANGUAGE_ID)
                .allowAllAccess(true)
                .allowHostAccess(HostAccess.ALL)
                .allowPolyglotAccess(PolyglotAccess.ALL)
                .option("js.esm-eval-returns-exports", "true")
                .engine(sharedEngine).build()) {
            if (MapUtils.isNotEmpty(bindObject)) {
                // 将上下文变量绑定到JavaScript环境
                Value bindings = ctx.getBindings(JS.LANGUAGE_ID);
                for (Map.Entry<String, Object> entry : bindObject.entrySet()) {
                    bindings.putMember(entry.getKey(), ProxyFactory.of(entry.getValue()));
                }
            }
            return function.apply(ctx);
        }
    }

    public static Source source(String content, String filename) throws IOException {
        return Source.newBuilder("js", content, filename)
                .mimeType("application/javascript+module")
                .build();
    }

    /**
     * 创建函数包装对象，包含函数源码和执行能力
     *
     * @param functionValue GraalVM 函数值对象
     * @return 包装后的 JsFunction 对象
     */
    public static JsFunction createFunctionWrapper(Value functionValue) {
        if (functionValue == null || !functionValue.canExecute()) {
            throw new IllegalArgumentException("Value is not executable");
        }

        // 尝试获取函数源码
        String source = extractFunctionSource(functionValue);

        // 创建函数包装对象
        return new JsFunction("(%s)".formatted(source));
    }

    /**
     * 提取函数源码
     *
     * @param functionValue GraalVM 函数值对象
     * @return 函数源码字符串
     */
    private static String extractFunctionSource(Value functionValue) {
        // 首先尝试获取源码位置
        SourceSection sourceSection = functionValue.getSourceLocation();
        if (sourceSection != null) {
            return sourceSection.getCharacters().toString();
        }

        // 如果无法获取源码位置，尝试使用 Function.prototype.toString 方法
        try {
            return withContext(ctx -> {
                // 获取 Function.prototype.toString 方法
                Value toStringFunc = ctx.eval("js", "Function.prototype.toString");

                // 调用 toString 方法获取函数源码
                if (toStringFunc.canExecute()) {
                    Value result = toStringFunc.execute(functionValue);
                    return result.asString();
                }

                // 如果无法执行 toString，尝试直接转换为字符串
                return functionValue.toString();
            });
        } catch (Exception e) {
            // 如果出现异常，返回默认字符串
            return "function() { /* Source not available */ }";
        }
    }

    /**
     * 将 GraalVM 的 Value 对象转换为 Java 对象
     * 支持基本类型、数组、Map、List 以及嵌套对象的转换
     *
     * @param value GraalVM Value 对象
     * @return 转换后的 Java 对象
     */
    public static Object toJava(Value value) {
        // 处理 null 值
        if (value == null || value.isNull()) {
            return null;
        }

        // 处理基本类型
        if (value.isBoolean()) {
            return value.asBoolean();
        } else if (value.isString()) {
            return value.asString();
        } else if (value.isNumber()) {
            return value.as(Number.class);
        } else if (value.isHostObject()) {
            return value.asHostObject();
        } else if (value.isProxyObject()) {
            return value.asProxyObject();
        }

        // 处理函数类型
        //        if (value.canExecute() && !value.hasMembers() && !value.hasArrayElements()) {
        if (value.canExecute()) {
            return createFunctionWrapper(value);
        }

        // 处理日期时间类型
        if (value.isDate() || value.isTime() || value.isInstant() ||
            value.isDuration() || value.isTimeZone()) {
            return value.as(Object.class); // 转换为适当的 Java 日期时间对象
        }

        // 处理数组类型
        if (value.hasArrayElements()) {
            long size = value.getArraySize();
            // 判断是否应该转换为 List 而不是数组
            boolean convertToList = false;

            // 检查是否有 push 方法，如果有可能是 JavaScript 数组，应转为 List
            if (value.hasMember("push") && value.getMember("push").canExecute()) {
                convertToList = true;
            }

            if (convertToList) {
                List<Object> list = new ArrayList<>((int) size);
                for (int i = 0; i < size; i++) {
                    list.add(toJava(value.getArrayElement(i)));
                }
                return list;
            } else {
                Object[] array = new Object[(int) size];
                for (int i = 0; i < array.length; i++) {
                    array[i] = toJava(value.getArrayElement(i));
                }
                return array;
            }
        }

        // 处理 Map 类型（JavaScript 对象）
        if (value.hasMembers()) {
            Map<String, Object> map = new LinkedHashMap<>();
            Set<String> keys = value.getMemberKeys();
            for (String key : keys) {
                Value member = value.getMember(key);
                // 处理函数类型的成员
                if (member.canExecute()) {
                    // 创建函数对象，包含函数源码和执行能力
                    map.put(key, createFunctionWrapper(member));
                } else {
                    map.put(key, toJava(member));
                }
            }
            return map;
        }

        // 处理 Hash 类型（如 JavaScript 的 Map 对象）
        if (value.hasHashEntries()) {
            Map<Object, Object> map = new HashMap<>();
            Value entriesIterator = value.getHashEntriesIterator();

            while (entriesIterator.hasIteratorNextElement()) {
                Value entry = entriesIterator.getIteratorNextElement();
                if (entry.hasArrayElements() && entry.getArraySize() == 2) {
                    Object key = toJava(entry.getArrayElement(0));
                    Object val = toJava(entry.getArrayElement(1));
                    map.put(key, val);
                }
            }
            return map;
        }

        // 处理迭代器类型
        if (value.hasIterator()) {
            List<Object> list = new ArrayList<>();
            Value iterator = value.getIterator();

            while (iterator.hasIteratorNextElement()) {
                list.add(toJava(iterator.getIteratorNextElement()));
            }
            return list;
        }

        // 处理其他类型，尝试使用 as(Object.class) 进行转换
        try {
            return value.as(Object.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("Cannot convert value: " + value + ". Error: " + e.getMessage(), e);
        }
    }
}
