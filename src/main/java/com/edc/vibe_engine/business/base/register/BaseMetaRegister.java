package com.edc.vibe_engine.business.base.register;

import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.def.DefRelation;
import com.edc.vibe_engine.meta.def.DefUniqRule;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.enums.FieldFlag;
import com.edc.vibe_engine.meta.enums.RelationType;
import com.edc.vibe_engine.meta.interfaces.IMetaRegister;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BaseMetaRegister implements IMetaRegister {


    @Override
    public void register() {
        final DefEntity.DefEntityBuilder orgBuilder = DefEntity.builder()
                .module("base")
                .name("organization").displayName("机构")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("short_name").displayName("简称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("address").displayName("地址").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("org_type").displayName("机构类型").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("introduction").displayName("简介").dataType(DataType.TEXT).build())
                .field(DefField.builder().name("contact_person").displayName("联系人").dataType(DataType.TEXT).build())
                .field(DefField.builder().name("contact_phone").displayName("联系电话").dataType(DataType.TEXT).build());

        MetaManager.registerEntity(orgBuilder);

        // 部门
        final DefEntity.DefEntityBuilder deptBuilder = DefEntity.builder()
                .module("base")
                .name("department").displayName("部门")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("parent_id").displayName("直属上级 id").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("parent").displayName("直属上级").type(RelationType.REF_ONE)
                        .refField("parent_id").module("base").entity("department")
                        .build())
                .field(DefField.builder().name("path").displayName("所属路径").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("owner_id").displayName("负责人 id").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("owner").displayName("负责人").type(RelationType.REF_ONE)
                        .refField("owner_id").module("base").entity("employee")
                        .build())
                .field(DefField.builder().name("org_id").displayName("所属机构 id").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("org").displayName("所属机构").type(RelationType.REF_ONE)
                        .refField("org_id").module("base").entity("organization")
                        .build())
                .relation(DefRelation.builder().name("employees").displayName("部门人员").type(RelationType.JOIN_MANY)
                        .joinMapping(DefRelation.JoinMapping.builder().field("dept_id").valExp("self._id").build())
                        .module("base").entity("employee")
                        .build());

        MetaManager.registerEntity(deptBuilder);

        // 用户
        final DefEntity.DefEntityBuilder userBuilder = DefEntity.builder()
                .module("base")
                .name("employee").displayName("用户")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("nick_name").displayName("昵称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("sex").displayName("性别").dataType(DataType.VARCHAR).build())
                // 联系信息
                .field(DefField.builder().name("email").displayName("邮箱").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("phone").displayName("手机号").dataType(DataType.VARCHAR).build())

                // 安全信息
                .field(DefField.builder().name("password").displayName("密码").dataType(DataType.VARCHAR).flag(FieldFlag.INTERNAL).build())
                .field(DefField.builder().name("salt").displayName("安全符").dataType(DataType.VARCHAR).flag(FieldFlag.INTERNAL).build())

                // 归属信息
                .field(DefField.builder().name("owner_id").displayName("负责人 id").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("owner").displayName("负责人").type(RelationType.REF_ONE)
                        .refField("owner_id").module("base").entity("employee")
                        .build())
                .field(DefField.builder().name("dept_id").displayName("所属部门 id").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("dept").displayName("所属部门").type(RelationType.REF_ONE)
                        .refField("dept_id").module("base").entity("department")
                        .build())
                .field(DefField.builder().name("post_id").displayName("岗位").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("org_id").displayName("所属机构").dataType(DataType.VARCHAR).build())
                .relation(DefRelation.builder().name("org").displayName("所属机构").type(RelationType.REF_ONE)
                        .refField("org_id").module("base").entity("organization")
                        .build());
        MetaManager.registerEntity(userBuilder);

        // 角色
        final DefEntity.DefEntityBuilder roleBuilder = DefEntity.builder()
                .module("base")
                .name("role").displayName("角色")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("description").displayName("描述").dataType(DataType.TEXT).build());
        MetaManager.registerEntity(roleBuilder);

        // 菜单
        final DefEntity.DefEntityBuilder menuBuilder = DefEntity.builder()
                .module("base")
                .name("menu").displayName("菜单")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("url").displayName("URL").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("parent_id").displayName("父级ID").dataType(DataType.VARCHAR).build());
        MetaManager.registerEntity(menuBuilder);

        // 权限
        final DefEntity.DefEntityBuilder permissionBuilder = DefEntity.builder()
                .module("base")
                .name("permission").displayName("权限")
                .field(DefField.builder().name("code").displayName("编号").dataType(DataType.VARCHAR).build())
                .rule(new DefUniqRule(List.of("code")))
                .field(DefField.builder().name("name").displayName("名称").dataType(DataType.VARCHAR).build())
                .field(DefField.builder().name("description").displayName("描述").dataType(DataType.TEXT).build());
        MetaManager.registerEntity(permissionBuilder);

    }
}
