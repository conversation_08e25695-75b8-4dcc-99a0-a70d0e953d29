package com.edc.vibe_engine.common.slot;

import com.edc.vibe_engine.common.support.LockUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 参考 liteflow 的上下文容器实现
 *
 * @param <T>
 */
@Slf4j
public class SlotPool<T> {


    public final AtomicInteger occupyCount = new AtomicInteger(0);

    /**
     * 这里为什么采用ConcurrentHashMap作为slot存放的容器？
     * 因为ConcurrentHashMap的随机取值复杂度也和数组一样为O(1)，并且没有并发问题，还有自动扩容的功能
     * 用数组的话，扩容涉及copy，线程安全问题还要自己处理
     */
    private final ConcurrentHashMap<Integer, T> contexts;

    private final ConcurrentLinkedQueue<Integer> queue;

    /**
     * 扩容锁
     */
    private final Lock scalingLock = new ReentrantLock();

    private final String group;
    /**
     * 当前slot的下标index的最大值
     */
    private Integer currentIndexMaxValue = 1024;

    public SlotPool(String group) {
        this.group = group;
        contexts = new ConcurrentHashMap<>();
        queue = IntStream.range(0, currentIndexMaxValue)
                .boxed()
                .collect(Collectors.toCollection(ConcurrentLinkedQueue::new));
    }


    public int offerIndex(T ctx) {
        try {
            // 这里有没有并发问题？
            // 没有，因为QUEUE的类型为ConcurrentLinkedQueue，并发情况下，每次取到的index不会相同
            // 当然前提是QUEUE里面的值不会重复，但是这个是由其他机制来保证的
            Integer slotIndex = queue.poll();

            if (slotIndex == null) {
                // 只有在扩容的时候需要用到synchronized重量级锁
                // 扩一次容，增强原来size的0.75，因为初始slot容量为1024，从某种层面来说，即便并发很大。但是扩容的次数不会很多。
                // 因为单个机器的tps上限总归是有一个极限的，不可能无限制的增长。
                slotIndex = LockUtils.execute(scalingLock, () -> {
                    // 在扩容的一刹那，去竞争这个锁的线程还是有一些，所以获得这个锁的线程这里要再次取一次。如果为null，再真正扩容
                    Integer innerSlotIndex = queue.poll();
                    if (innerSlotIndex == null) {
                        int nextMaxIndex = (int) Math.round(currentIndexMaxValue * 1.75);
                        queue.addAll(IntStream.range(currentIndexMaxValue, nextMaxIndex)
                                .boxed()
                                .collect(Collectors.toCollection(ConcurrentLinkedQueue::new)));
                        currentIndexMaxValue = nextMaxIndex;
                        // 扩容好，从队列里再取出扩容好的index
                        innerSlotIndex = queue.poll();
                    }
                    return innerSlotIndex;
                });
            }

            if (slotIndex != null) {
                contexts.put(slotIndex, ctx);
                occupyCount.incrementAndGet();
                return slotIndex;
            }
        } catch (Exception e) {
            log.error("{} offer ctx error", group, e);
            return -1;
        }
        return -1;
    }

    public void release(int slotIndex) {
        final T context = contexts.remove(slotIndex);
        if (context != null) {
            log.debug("{} slot[{}] released", group, slotIndex);
            queue.add(slotIndex);
            occupyCount.decrementAndGet();
        } else {
            log.warn("{} slot[{}] already has been released", group, slotIndex);
        }
    }

    public T get(Integer slotIndex) {
        return contexts.get(slotIndex);
    }
}
