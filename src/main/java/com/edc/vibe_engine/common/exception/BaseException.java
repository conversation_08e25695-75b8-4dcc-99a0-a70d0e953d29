package com.edc.vibe_engine.common.exception;


/**
 * 异常处理基础类
 */

public class BaseException extends RuntimeException implements IKnownException {
    private static final long serialVersionUID = -7743825163305086064L;

    private int errorCode;

    public BaseException() {

    }

    public BaseException(String message) {
        this(500, message);
    }

    public BaseException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(Throwable cause) {
        super(cause);
    }
}
