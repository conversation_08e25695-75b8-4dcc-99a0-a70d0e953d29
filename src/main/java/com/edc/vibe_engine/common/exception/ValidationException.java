package com.edc.vibe_engine.common.exception;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 验证异常，用于在验证失败时抛出
 */
@Getter
public class ValidationException extends BaseException {
    private static final long serialVersionUID = 1L;

    /**
     * 验证错误列表，每个错误包含字段名和错误消息
     */
    private final List<ValidationError> errors;

    /**
     * 创建一个验证异常
     *
     * @param message 异常消息
     */
    public ValidationException(String message) {
        super(400, message);
        this.errors = new ArrayList<>();
    }

    /**
     * 创建一个验证异常
     *
     * @param message 异常消息
     * @param errors  验证错误列表
     */
    public ValidationException(String message, List<ValidationError> errors) {
        super(400, message);
        this.errors = errors;
    }

    /**
     * 创建一个验证异常
     *
     * @param fieldName 字段名
     * @param message   错误消息
     */
    public ValidationException(String fieldName, String message) {
        super(400, "验证失败: " + fieldName + " - " + message);
        this.errors = new ArrayList<>();
        this.errors.add(new ValidationError(fieldName, message));
    }

    /**
     * 添加一个验证错误
     *
     * @param fieldName 字段名
     * @param message   错误消息
     */
    public void addError(String fieldName, String message) {
        this.errors.add(new ValidationError(fieldName, message));
    }

    /**
     * 添加一个验证错误
     *
     * @param error 验证错误
     */
    public void addError(ValidationError error) {
        this.errors.add(error);
    }

    /**
     * 添加多个验证错误
     *
     * @param errors 验证错误列表
     */
    public void addErrors(List<ValidationError> errors) {
        this.errors.addAll(errors);
    }

    /**
     * 从Map列表创建验证异常
     *
     * @param errorMaps 错误Map列表，每个Map包含field和message字段
     * @return 验证异常
     */
    public static ValidationException fromErrorMaps(List<Map<String, Object>> errorMaps) {
        List<ValidationError> errors = new ArrayList<>();
        for (Map<String, Object> errorMap : errorMaps) {
            String field = (String) errorMap.get("field");
            String message = (String) errorMap.get("message");
            errors.add(new ValidationError(field, message));
        }
        return new ValidationException("验证失败", errors);
    }

    /**
     * 验证错误类，包含字段名和错误消息
     */
    @Getter
    public static class ValidationError {
        private final String field;
        private final String message;

        public ValidationError(String field, String message) {
            this.field = field;
            this.message = message;
        }

        @Override
        public String toString() {
            return field + ": " + message;
        }
    }
}
