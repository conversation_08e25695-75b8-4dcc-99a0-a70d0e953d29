package com.edc.vibe_engine.common.interfaces;

import com.edc.vibe_engine.common.types.Pageable;

public interface IPageable extends ILimited {

    static IPageable page(int page, int pageSize) {
        Pageable request = new Pageable();
        request.setPage(page);
        request.setPageSize(pageSize);
        return request;
    }

    /**
     * @return 页码
     */
    int getPage();

    /**
     * @return 每页数量
     */
    int getPageSize();

    default int getOffset() {
        return (Math.max(getPage() - 1, 0)) * getPageSize();
    }

    @Override
    default int getLimit() {
        return getPageSize();
    }
}
