package com.edc.vibe_engine.common.types;

import com.edc.vibe_engine.common.interfaces.IPageResult;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class AbsPageData<T> implements IPageResult<T> {

    @Serial
    private static final long serialVersionUID = -733459364781807653L;

    private int page;
    private int pageSize;
    private long totalSize;
    private long totalPage;

    private T items;

    private boolean hasMore = Boolean.TRUE;


    public void calcTotalPage() {
        if (totalSize == 0) {
            this.totalPage = 1;
        } else if (totalSize % this.pageSize == 0) {
            this.totalPage = totalSize / this.pageSize;
        } else {
            this.totalPage = totalSize / this.pageSize + 1;
        }
        this.hasMore = this.page < this.totalPage;
    }
}
