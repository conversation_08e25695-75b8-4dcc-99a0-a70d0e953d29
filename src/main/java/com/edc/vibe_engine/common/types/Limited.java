package com.edc.vibe_engine.common.types;

import com.edc.vibe_engine.common.interfaces.ILimited;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Limited implements ILimited {

    private int limit;

    @Override
    public void fixLimit(int maxLimit) {
        this.limit = ILimited.calcLimit(this.limit, maxLimit);
    }
}
