package com.edc.vibe_engine.common.types;


import com.edc.vibe_engine.common.interfaces.IPageable;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ListPageResult<T> extends AbsPageData<List<T>> {
    @Serial
    private static final long serialVersionUID = -7930851589190647812L;

    public static <T> ListPageResult<T> empty() {
        ListPageResult<T> result = new ListPageResult<>();
        result.setHasMore(false);
        result.setItems(Collections.emptyList());
        return result;
    }

    public static <T> ListPageResult<T> byQuery(long total, IPageable query, List<? extends T> list) {
        ListPageResult<T> result = new ListPageResult<>();
        result.setItems(new ArrayList<>(list));
        result.setPageSize(query.getPageSize());
        result.setPage(query.getPage());
        result.setTotalSize(total);
        result.calcTotalPage();
        return result;
    }


    public <R> ListPageResult<R> transform(Function<? super T, ? extends R> mapper) {
        return map(list -> CollectionUtils.isEmpty(list) ? Collections.emptyList() : list.stream().map(mapper).collect(Collectors.toList()));
    }

    public <R> ListPageResult<R> map(Function<List<? extends T>, List<R>> mapper) {
        ListPageResult<R> result = new ListPageResult<>();
        final List<T> list = this.getItems();
        result.setItems(mapper.apply(list));
        result.setPageSize(this.getPageSize());
        result.setPage(this.getPage());
        result.setTotalSize(this.getTotalSize());
        result.calcTotalPage();
        return result;
    }

    public <R> ListPageResult<R> transform(List<R> result) {
        return map(list -> result);
    }

}
