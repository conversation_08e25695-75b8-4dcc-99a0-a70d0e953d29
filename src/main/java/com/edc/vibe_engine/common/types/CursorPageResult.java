package com.edc.vibe_engine.common.types;

import com.edc.vibe_engine.common.interfaces.ICursor;
import com.edc.vibe_engine.common.interfaces.ICursorResult;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class CursorPageResult<T> implements ICursorResult<List<T>> {

    @Serial
    private static final long serialVersionUID = -8929616969745006227L;

    private String nextCursor;

    private Boolean hasMore = Boolean.TRUE;

    private List<T> list;


    public static <T> CursorPageResult<T> empty() {
        CursorPageResult<T> result = new CursorPageResult<>();
        result.setHasMore(false);
        result.setList(Collections.emptyList());
        return result;
    }

    public static <T> CursorPageResult<T> byQuery(ICursor query, List<? extends T> list, String nextCursor) {
        CursorPageResult<T> result = new CursorPageResult<>();
        result.setList(new ArrayList<>(list));
        result.setNextCursor(nextCursor);
        result.setHasMore(list.size() >= query.getPageSize());
        return result;
    }


    public <R> CursorPageResult<R> transform(Function<? super T, ? extends R> mapper) {
        return map(list -> CollectionUtils.isEmpty(list) ? Collections.emptyList() : list.stream().map(mapper).collect(Collectors.toList()));
    }

    public <R> CursorPageResult<R> map(Function<List<? extends T>, List<R>> mapper) {
        CursorPageResult<R> result = new CursorPageResult<>();
        final List<T> list = this.getList();
        result.setList(mapper.apply(list));
        result.setHasMore(this.getHasMore());
        result.setNextCursor(this.getNextCursor());
        return result;
    }

    public <R> CursorPageResult<R> transform(List<R> result) {
        return map(list -> result);
    }
}
