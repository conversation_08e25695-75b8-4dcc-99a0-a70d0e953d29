package com.edc.vibe_engine.common.types;

import com.edc.vibe_engine.common.interfaces.ICursor;
import com.edc.vibe_engine.common.interfaces.ILimited;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Cursor implements ICursor {


    /**
     * 游标
     */
    private String nextCursor;
    /**
     * 分页数量
     */
    private int pageSize = 10;


    @Override
    public void fixLimit(int maxLimit) {
        this.pageSize = ILimited.calcLimit(this.pageSize, maxLimit);
    }
}
