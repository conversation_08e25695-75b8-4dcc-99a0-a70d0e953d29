package com.edc.vibe_engine.common.types;

import com.edc.vibe_engine.common.interfaces.ILimited;
import com.edc.vibe_engine.common.interfaces.IPageable;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Setter
public class Pageable implements Serializable, IPageable {

    @Serial
    private static final long serialVersionUID = 7029390064176027575L;

    /**
     * 页码
     */
    private int page = 1;
    /**
     * 分页数量
     */
    private int pageSize = 10;


    @Override
    public int getPage() {
        return page;
    }

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public void fixLimit(int maxLimit) {
        this.pageSize = ILimited.calcLimit(this.pageSize, maxLimit);
    }

    public IPageable asPageable() {
        return IPageable.page(page, pageSize);
    }
}
