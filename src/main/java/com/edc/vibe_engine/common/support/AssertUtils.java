package com.edc.vibe_engine.common.support;

import com.edc.vibe_engine.common.exception.AssertionException;
import com.edc.vibe_engine.common.exception.IllegalDataException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;

import java.util.Collection;
import java.util.function.Supplier;
import java.util.regex.Pattern;

public final class AssertUtils {
    public static void assertNull(Object target, String message, Object... params) {
        if (target != null) {
            throw buildAssertionException(message, params);
        }
    }

    public static void assertNotNull(Object target, String message, Object... params) {
        if (target == null) {
            throw buildAssertionException(message, params);
        }
    }

    public static void assertTrue(boolean target, String message, Object... params) {
        if (!target) {
            throw buildAssertionException(message, params);
        }
    }

    public static void assertFalse(boolean target, String message, Object... params) {
        assertTrue(!target, message, params);
    }

    public static void assertHasText(String target, String message, Object... params) {
        if (!StringUtils.isNotEmpty(target)) {
            throw buildAssertionException(message, params);
        }
    }

    public static void assertMatches(String target, Pattern pattern, String message, Object... params) {
        if (!pattern.matcher(target).matches()) {
            throw buildAssertionException(message, params);
        }
    }

    public static void throwException(String message, Object... params) {
        throw buildAssertionException(message, params);
    }

    public static AssertionException buildAssertionException(String message, Object[] params) {
        String errorMessage = params == null ? message : MessageFormatter.arrayFormat(message, params).getMessage();
        return new AssertionException(errorMessage);
    }


    public static void assertNull(Object target, Supplier<? extends RuntimeException> supplier) {
        if (target != null) {
            throw supplier.get();
        }
    }

    public static void assertNotNull(Object target, Supplier<? extends RuntimeException> supplier) {
        if (target == null) {
            throw supplier.get();
        }
    }

    public static void assertTrue(boolean target, Supplier<? extends RuntimeException> supplier) {
        if (!target) {
            throw supplier.get();
        }
    }

    public static void assertFalse(boolean target, Supplier<? extends RuntimeException> supplier) {
        assertTrue(!target, supplier);
    }

    public static void assertHasText(String target, Supplier<? extends RuntimeException> supplier) {
        if (!StringUtils.isNotEmpty(target)) {
            throw supplier.get();
        }
    }

    public static void assertMatches(String target, Pattern pattern, Supplier<? extends RuntimeException> supplier) {
        if (!pattern.matcher(target).matches()) {
            throw supplier.get();
        }
    }

    public static <T> T assertGetOne(Collection<? extends T> list) {
        return assertGetOne(list, null);
    }

    public static <T> T assertGetOne(Collection<? extends T> list, Supplier<String> messageSupplier) {
        assertIsOne(list, messageSupplier);
        return CollectionUtils.isNotEmpty(list) ? IterableUtils.get(list, 0) : null;
    }

    public static void assertIsOne(Collection<?> list) {
        assertIsOne(list, null);
    }

    public static void assertIsOne(Collection<?> list, Supplier<String> messageSupplier) {
        if (CollectionUtils.isNotEmpty(list)) {
            if (list.size() != 1) {
                final StringBuilder message = new StringBuilder("One entity is expected, but the query result is multiple entitys.");
                if (messageSupplier != null) {
                    final String s = messageSupplier.get();
                    if (StringUtils.isNoneEmpty(s)) {
                        message.append(s);
                    }
                }
                throw new IllegalDataException(message.toString());
            }
        }
    }
}
