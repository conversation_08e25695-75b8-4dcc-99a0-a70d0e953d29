package com.edc.vibe_engine.common.support;

import io.vavr.CheckedFunction0;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

@Slf4j
public class AsyncUtils {

    public static final ExecutorService VIRTUAL_THREAD_EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();


    //    public static int getBaseThreadNum(final int baseNum, final int multiply) {
    //        final int availabledProcessors = Runtime.getRuntime().availableProcessors();
    //        final int coreSize = availabledProcessors * multiply;
    //        log.info("availabledProcessors={},coreSize={},baseNum={}", availabledProcessors, coreSize, baseNum);
    //        return Math.max(coreSize, baseNum);
    //    }

    public static <U> CompletableFuture<U> supplyAsync(CheckedFunction0<U> supplier) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return supplier.apply();
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        }, VIRTUAL_THREAD_EXECUTOR);
    }

    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier, Executor executor) {
        return CompletableFuture.supplyAsync(supplier, executor);
    }

    public static CompletableFuture<Void> supplyAsync(Runnable runnable, Executor executor) {
        return supplyAsync(() -> {
            runnable.run();
            return null;
        }, executor);
    }

    public static <V> CompletableFuture<V> failedFuture(Exception e) {
        CompletableFuture<V> future = new CompletableFuture<>();
        future.completeExceptionally(e);
        return future;
    }

    public static <V> Throwable cause(CompletableFuture<V> completableFuture) {
        if (!completableFuture.isCompletedExceptionally()) {
            return null;
        }
        try {
            completableFuture.get();
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause != null) {
                return cause;
            }
            return e;
        }
    }

    public static <V> boolean succeeded(CompletableFuture<V> future) {
        return future.isDone() && !future.isCompletedExceptionally();
    }

    public static <V> boolean failed(CompletableFuture<V> future) {
        return future.isDone() && future.isCompletedExceptionally();
    }

    public static <T> List<T> await(Collection<? extends CompletionStage<T>> cfs) {
        return allOf(cfs).join();
    }

    public static <T> T await(CompletionStage<T> completionStage) {
        return completionStage.toCompletableFuture().join();
    }


    @SuppressWarnings("unchecked")
    public static <T> T tryAwait(Object obj) {
        if (obj instanceof CompletionStage) {
            return await(((CompletionStage<T>) obj));
        } else if (obj instanceof Collection<?>) {
            final CompletableFuture<List<Object>> zipped = toListFuture(((Collection<?>) obj));
            return (T) await(zipped);
        } else {
            return (T) obj;
        }
    }

    public static <T> CompletableFuture<List<T>> allOf(Collection<? extends CompletionStage<T>> cfs) {
        final Stream<? extends CompletionStage<T>> stream = cfs.stream();
        return allOf(stream);
    }

    @SuppressWarnings("unchecked")
    private static <T> CompletableFuture<List<T>> allOf(Stream<? extends CompletionStage<T>> stream) {
        final CompletableFuture<T>[] futures = stream.map(CompletionStage::toCompletableFuture)
                .toArray(CompletableFuture[]::new);
        return CompletableFuture.allOf(futures)
                .thenApply(v -> Arrays.stream(futures)
                        .map(CompletableFuture::join)
                        .collect(toList())
                );
    }

    @SuppressWarnings("unchecked")
    public static <T> CompletableFuture<T> toFuture(Object t) {
        if (t instanceof CompletionStage) {
            return ((CompletionStage<T>) t).toCompletableFuture();
        } else {
            return CompletableFuture.completedFuture((T) t);
        }
    }

    public static <T> CompletableFuture<List<T>> toListFuture(Collection<?> list) {
        return allOf(list.stream()
                .map(AsyncUtils::toFuture));
    }

}
