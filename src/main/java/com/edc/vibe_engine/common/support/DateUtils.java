package com.edc.vibe_engine.common.support;


import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.SignStyle;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.time.temporal.TemporalUnit;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;

import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoField.NANO_OF_SECOND;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;

/**
 * 时间工具类，统一使用 LocalDateTime
 */
public final class DateUtils {

    public static final Pattern REGEX_DATE_TIME = Pattern.compile("[1-3]\\d{3}-\\d{1,2}-\\d{1,2}( \\d{1,2}:\\d{1,2}(:\\d{1,2})?)?");

    public static final Pattern REGEX_DATE_TIME_SLASH = Pattern.compile("[1-3]\\d{3}/\\d{1,2}/\\d{1,2}( \\d{1,2}:\\d{1,2}(:\\d{1,2})?)?");


    public static final int LIB_MAX_DATE = 29991231;
    public static final long LIB_MAX_DATE_TIME = 29991231235959L;

    public static final long L10 = 10000000000L;
    public static final int L8 = 100000000;
    public static final int L6 = 1000000;
    public static final int L4 = 10000;
    public static final int L2 = 100;

    public static final String DATE = "yyyy-MM-dd";

    public static final String TIME = "HH:mm:ss";

    public static final String TIME_WITHOUT_SEC = "HH:mm";

    public static final String DATE_TIME = DATE + " " + TIME;

    public static final String DATE_TIME_WITHOUT_SEC = DATE + " " + TIME_WITHOUT_SEC;

    /**
     * 默认为 +08:00 时区
     */
    public static final String OFFSET_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSS'+08:00'";
    /**
     * UTC 0 时区
     */
    public static final String UTC_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final long MILLS_THRESHOLD = 99999999999L;


    public static final DateTimeFormatter UTC_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern(UTC_DATE_TIME);
    public static final DateTimeFormatter OFFSET_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern(OFFSET_DATE_TIME);

    public static final DateTimeFormatter DATE_FORMAT = new DateTimeFormatterBuilder()
            .appendValue(ChronoField.YEAR, 4, 4, SignStyle.EXCEEDS_PAD)
            .appendLiteral('-')
            .appendValue(ChronoField.MONTH_OF_YEAR, 1, 2, SignStyle.NOT_NEGATIVE)
            .appendLiteral('-')
            .appendValue(ChronoField.DAY_OF_MONTH, 1, 2, SignStyle.NOT_NEGATIVE)
            .toFormatter();

    public static final DateTimeFormatter TIME_FORMAT = new DateTimeFormatterBuilder()
            .appendValue(HOUR_OF_DAY, 1, 2, SignStyle.NOT_NEGATIVE)
            .appendLiteral(':')
            .appendValue(MINUTE_OF_HOUR, 1, 2, SignStyle.NOT_NEGATIVE)
            .optionalStart()
            .appendLiteral(':')
            .appendValue(SECOND_OF_MINUTE, 1, 2, SignStyle.NOT_NEGATIVE)
            .optionalStart()
            .appendFraction(NANO_OF_SECOND, 0, 9, true)
            .toFormatter();

    public static final DateTimeFormatter DATE_TIME_FORMAT = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .append(DATE_FORMAT)
            .optionalStart()
            .appendLiteral(' ')
            .append(TIME_FORMAT).toFormatter();

    public static final DateTimeFormatter DATE_FORMAT_SLASH = new DateTimeFormatterBuilder()
            .appendValue(ChronoField.YEAR, 4, 4, SignStyle.EXCEEDS_PAD)
            .appendLiteral('/')
            .appendValue(ChronoField.MONTH_OF_YEAR, 1, 2, SignStyle.NOT_NEGATIVE)
            .appendLiteral('/')
            .appendValue(ChronoField.DAY_OF_MONTH, 1, 2, SignStyle.NOT_NEGATIVE)
            .toFormatter();

    public static final DateTimeFormatter DATE_TIME_FORMAT_SLASH = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .append(DATE_FORMAT_SLASH)
            .optionalStart()
            .appendLiteral(' ')
            .append(TIME_FORMAT).toFormatter();

    public static final ZoneOffset DEFAULT_OFFSET = ZoneOffset.ofHours(8);

    public static final LocalDate MIN_DATE = LocalDate.of(1970, 1, 1);
    public static final LocalDate MAX_DATE = LocalDate.of(2999, 12, 31);
    public static final LocalTime MIN_TIME = LocalTime.of(0, 0, 0, 0);
    public static final LocalTime MAX_TIME = LocalTime.of(23, 59, 59, 999000000);

    public static final LocalDateTime MIN_DATE_TIME = MIN_DATE.atTime(MIN_TIME);
    public static final LocalDateTime MAX_DATE_TIME = MAX_DATE.atTime(MAX_TIME);


    public enum Unit {
        SECONDS("Seconds"),
        MINUTES("Minutes"),
        DAYS("Days"),
        MONTHS("Months"),
        YEARS("Years"),
        ;

        private final String name;

        Unit(String name) {
            this.name = name;
        }

        public static Unit from(String name) {
            for (Unit value : Unit.values()) {
                if (value.name.equalsIgnoreCase(name)) {
                    return value;
                }
            }
            return null;
        }

        public TemporalUnit getUnit() {
            return ChronoUnit.valueOf(this.name());
        }
    }


    /**
     * 转换时间格式
     */
    public static String formatDateTime(LocalDateTime localDateTime, String format) {
        return format(localDateTime, format);
    }

    public static String format(TemporalAccessor temporal, String format) {
        if (temporal == null) {
            return "";
        }
        DateTimeFormatter formatter = StringUtils.isNotEmpty(format) ? DateTimeFormatter.ofPattern(format) : OFFSET_DATE_TIME_FORMAT;
        return formatter.format(temporal);
    }


    /**
     * @param temporal
     * @return {@link String}
     */
    public static String formatTemporal(TemporalAccessor temporal) {
        switch (temporal) {
            case null -> {
                return "";
            }
            case LocalDateTime localDateTime -> {
                return formatDateTime(localDateTime);
            }
            case LocalDate localDate -> {
                return formatDate(localDate);
            }
            case LocalTime localTime -> {
                return formatTime(localTime);
            }
            default -> {
            }
        }
        final LocalDateTime localDateTime = temporalToDateTime(temporal);
        return formatDateTime(localDateTime);
    }

    public static String formatDateTime(LocalDateTime localDateTime) {
        return formatDateTime((TemporalAccessor) localDateTime);
    }

    public static String formatDateTime(TemporalAccessor temporal) {
        if (temporal == null) {
            return StringUtils.EMPTY;
        }
        return OFFSET_DATE_TIME_FORMAT.format(temporal);
    }

    public static String formatDate(LocalDate localDate) {
        return formatDate((TemporalAccessor) localDate);
    }

    public static String formatDate(TemporalAccessor temporal) {
        if (temporal == null) {
            return StringUtils.EMPTY;
        }
        return DateTimeFormatter.ISO_LOCAL_DATE.format(temporal);
    }


    public static String formatTime(LocalTime localTime) {
        return formatTime((TemporalAccessor) localTime);
    }

    public static String formatTime(TemporalAccessor temporal) {
        if (temporal == null) {
            return StringUtils.EMPTY;
        }
        return DateTimeFormatter.ISO_LOCAL_TIME.format(temporal);
    }

    public static Long temporalToNumber(TemporalAccessor temporal) {
        if (temporal == null) {
            return 0L;
        }
        if (temporal instanceof LocalDateTime) {
            return dateTimeToNumber(((LocalDateTime) temporal));
        }
        if (temporal instanceof LocalDate) {
            return dateToNumber(((LocalDate) temporal));
        }
        if (temporal instanceof LocalTime) {
            return timeToNumber(((LocalTime) temporal));
        }
        final LocalDateTime localDateTime = temporalToDateTime(temporal);
        return dateTimeToNumber(localDateTime);
    }

    public static Long dateToNumber(LocalDate value) {
        return dateTimeToNumber(value.atTime(MIN_TIME));
    }

    public static Long timeToNumber(LocalTime value) {
        return dateTimeToNumber(value.atDate(MIN_DATE));
    }

    public static Long dateTimeToNumber(LocalDateTime value) {
        return value.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 现在标准时间
     *
     * @return {@link LocalDateTime}
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }


    public static TemporalAccessor parseTemporal(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        final String upperCase = date.toUpperCase().trim();
        if (upperCase.contains("T")) {
            // 带时间
            return DateTimeFormatter.ISO_DATE_TIME.parse(upperCase);
        }
        if (REGEX_DATE_TIME.matcher(upperCase).matches()) {
            return DATE_TIME_FORMAT.parse(upperCase);
        }
        if (REGEX_DATE_TIME_SLASH.matcher(upperCase).matches()) {
            return DATE_TIME_FORMAT_SLASH.parse(upperCase);
        }
        if (upperCase.indexOf("-") == 4) {
            // 符合 yyyy-
            if (upperCase.length() > 10) {
                // 带时区
                return DateTimeFormatter.ISO_OFFSET_DATE.parse(upperCase);
            }
            return DateTimeFormatter.ISO_LOCAL_DATE.parse(upperCase);
        }
        if (upperCase.contains(":")) {
            // 带毫秒的、且带时区
            if (upperCase.contains(".") && (upperCase.endsWith("Z") || upperCase.contains("+") || upperCase.contains("-"))) {
                return DateTimeFormatter.ISO_OFFSET_TIME.parse(upperCase);
            }
            return DateTimeFormatter.ISO_TIME.parse(upperCase);
        }
        if (StringUtils.isNumeric(upperCase)) {
            // 时间戳
            final long epochMilli = Long.parseLong(upperCase);
            return parseDateTime(epochMilli);
        }
        return null;
    }

    public static <T> T parseDateTime(Number number, Function<LocalDateTime, T> function) {
        final LocalDateTime localDateTime = parseDateTime(number);
        if (localDateTime == null) {
            return null;
        }
        return function.apply(localDateTime);
    }

    public static LocalDateTime parseDateTime(Number number) {
        long epochMilli = number.longValue();
        if (epochMilli == 0) {
            return null;
        }
        if (epochMilli < MILLS_THRESHOLD) {
            // 单位为秒，转为毫秒
            epochMilli = epochMilli * 1000;
        }
        final Instant instant = Instant.ofEpochMilli(epochMilli);
        return instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    public static <T> T parseDateTime(String dateStr, Function<LocalDateTime, T> function) {
        final LocalDateTime localDateTime = parseDateTime(dateStr);
        if (localDateTime == null) {
            return null;
        }
        return function.apply(localDateTime);
    }

    public static LocalDateTime parseDateTime(String dateStr) {
        final TemporalAccessor temporal = parseTemporal(dateStr);
        if (temporal == null) {
            return null;
        }
        return temporalToDateTime(temporal);
    }

    private static LocalDateTime temporalToDateTime(TemporalAccessor temporal) {
        if (temporal instanceof LocalDateTime) {
            return ((LocalDateTime) temporal);
        }
        // 时区
        final ZoneOffset offset = temporal.query(TemporalQueries.offset());
        // 日期，默认 1970-01-01
        final LocalDate date = Optional.ofNullable(temporal.query(TemporalQueries.localDate())).orElse(MIN_DATE);
        // 时间，默认 00:00:00
        final LocalTime time = Optional.ofNullable(temporal.query(TemporalQueries.localTime())).orElse(MIN_TIME);
        final LocalDateTime localDateTime = LocalDateTime.of(date, time);
        if (offset == null) {
            return localDateTime;
        }
        // 带时区，转换为 +8 的 LocalDateTime
        return localDateTime.plusSeconds(-offset.getLong(ChronoField.OFFSET_SECONDS))
                .plusSeconds(DEFAULT_OFFSET.getLong(ChronoField.OFFSET_SECONDS));
    }


    public static LocalDate parseDate(String date) {
        return parseDateTime(date, LocalDateTime::toLocalDate);
    }

    public static LocalTime parseTime(String date) {
        return parseDateTime(date, LocalDateTime::toLocalTime);
    }

    public static LocalDate parseDate(Number num) {
        return parseDateTime(num, LocalDateTime::toLocalDate);
    }

    public static LocalTime parseTime(Number num) {
        return parseDateTime(num, LocalDateTime::toLocalTime);
    }


    public static Temporal plus(Temporal temporal, long addValue, String unitName) {
        final Unit unit = Unit.from(unitName);
        return plus(temporal, addValue, unit);
    }

    public static Temporal plus(Temporal temporal, long addValue, Unit unit) {
        if (unit == null || addValue == 0) {
            return temporal;
        }
        return plus(temporal, addValue, unit.getUnit());
    }

    public static Temporal plus(Temporal temporal, long addValue, TemporalUnit unit) {
        return temporal.plus(addValue, unit);
    }


    public static LocalDateTime fixOutRange(LocalDateTime value) {
        if (value == null || DateUtils.MIN_DATE_TIME.isAfter(value) || DateUtils.MIN_DATE_TIME.isEqual(value)
            || DateUtils.MAX_DATE_TIME.isBefore(value) || DateUtils.MAX_DATE_TIME.isEqual(value)) {
            return null;
        }
        return value;
    }

    public static LocalDate fixOutRange(LocalDate value) {
        if (value == null || DateUtils.MIN_DATE.isAfter(value) || DateUtils.MIN_DATE.isEqual(value)
            || DateUtils.MAX_DATE.isBefore(value) || DateUtils.MAX_DATE.isEqual(value)) {
            return null;
        }
        return value;
    }

}
