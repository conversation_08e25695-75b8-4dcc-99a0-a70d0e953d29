package com.edc.vibe_engine.common.support;

import com.edc.vibe_engine.common.constants.StringConstants;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.StringJoiner;

import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.split;

public class StringExUtils {


    public static String splitGet(String source, String connector, int index) {
        final String[] splits = split(source, connector);
        return ArrayUtils.get(splits, index, null);
    }

    public static String[] splitComma(String source) {
        return split(source, StringConstants.COMMA);
    }

    public static String[] splitColon(String source) {
        return split(source, StringConstants.COLON);
    }


    /**
     * 移除指定的后缀, 若不包含，返回原值
     *
     * @param source
     * @param suffix
     * @return
     */
    public static String subSuffix(String source, String suffix) {
        if (isEmpty(source)) {
            return source;
        }
        if (!source.endsWith(suffix)) {
            return source;
        }
        return source.substring(0, source.length() - suffix.length());
    }

    public static String subPrefix(String source, String prefix) {
        if (isEmpty(source)) {
            return source;
        }
        if (!source.startsWith(prefix)) {
            return source;
        }
        return source.substring(prefix.length());
    }

    public static String join(String connector, String... args) {
        int length = args == null ? 0 : args.length;
        if (length > 0) {
            return merge(connector, "", "", args);
        }
        return "";
    }

    public static String joinWith(String connector, String prefix, String suffix, String... args) {
        int length = args == null ? 0 : args.length;
        if (length > 0) {
            return merge(connector, prefix, suffix, args);
        }
        return "";
    }

    private static String merge(String connector, String prefix, String suffix, String... args) {
        StringJoiner joiner = new StringJoiner(connector, prefix, suffix);
        for (String value : args) {
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            joiner.add(value);
        }
        return joiner.toString();
    }

    /**
     * These rules result in the following additional example translations from
     * Java property names to JSON element names.
     * <ul><li>&quot;userName&quot; is translated to &quot;user_name&quot;</li>
     * <li>&quot;UserName&quot; is translated to &quot;user_name&quot;</li>
     * <li>&quot;USER_NAME&quot; is translated to &quot;user_name&quot;</li>
     * <li>&quot;user_name&quot; is translated to &quot;user_name&quot; (unchanged)</li>
     * <li>&quot;user&quot; is translated to &quot;user&quot; (unchanged)</li>
     * <li>&quot;User&quot; is translated to &quot;user&quot;</li>
     * <li>&quot;USER&quot; is translated to &quot;user&quot;</li>
     * <li>&quot;_user&quot; is translated to &quot;user&quot;</li>
     * <li>&quot;_User&quot; is translated to &quot;user&quot;</li>
     * <li>&quot;__user&quot; is translated to &quot;_user&quot;
     * (the first of two underscores was removed)</li>
     * <li>&quot;user__name&quot; is translated to &quot;user__name&quot;
     * (unchanged, with two underscores)</li></ul>
     */
    public static String toSnakeCase(String input) {
        if (input == null) return input; // garbage in, garbage out
        int length = input.length();
        StringBuilder result = new StringBuilder(length * 2);
        int resultLength = 0;
        boolean wasPrevTranslated = false;
        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);
            if (i > 0 || c != '_') // skip first starting underscore
            {
                if (Character.isUpperCase(c)) {
                    if (!wasPrevTranslated && resultLength > 0 && result.charAt(resultLength - 1) != '_') {
                        result.append('_');
                        resultLength++;
                    }
                    c = Character.toLowerCase(c);
                    wasPrevTranslated = true;
                } else {
                    wasPrevTranslated = false;
                }
                result.append(c);
                resultLength++;
            }
        }
        return resultLength > 0 ? result.toString() : input;
    }

    /**
     * 将输入字符串按指定分隔符转换为驼峰命名格式。
     * 第一个字符会根据前一个字符是否为分隔符决定大小写，后续字符在分隔符后首字母大写。
     *
     * @param input  输入的字符串
     * @param symbol 分隔符
     * @return 转换后的驼峰命名字符串
     */
    public static String toCamelCase(String input, char symbol) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            if (c == symbol) {
                // 如果当前字符是分隔符，则标记下一个字符为大写
                // 如果分隔符在开头则跳过
                if (i == 0) {
                    continue;
                }

                nextUpperCase = true;
            } else {
                // 处理非分隔符字符
                if (nextUpperCase) {
                    // 如果标记为大写，则将字符转为大写并添加到结果中
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    // 如果未标记为大写，则根据情况将字符转为小写或保持原样添加到结果中
                    if (result.isEmpty() || result.charAt(result.length() - 1) == symbol) {
                        result.append(Character.toLowerCase(c));
                    } else {
                        result.append(c);
                    }
                }
            }
        }

        return result.toString();
    }
}
