package com.edc.vibe_engine.common.support;


import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;


public final class MathUtils {

    public static final int DEFAULT_SCALE = 2;
    public static final RoundingMode DEFAULT_ROUND_MODE = RoundingMode.HALF_UP;

    public static BigDecimal safeNumber(Object value) {
        final BigDecimal decimal = TypeCastUtils.castToBigDecimal(value);
        if (decimal == null) {
            return BigDecimal.ZERO;
        }
        return decimal;
    }


    private static Stream<BigDecimal> safeNumberStream(Collection<? extends Number> list) {
        if (CollectionUtils.isEmpty(list))
            return Stream.empty();
        return list.stream()
                .map(MathUtils::safeNumber);
    }

    private static <T> Stream<BigDecimal> safeNumberStream(Collection<T> list, Function<T, Number> fn) {
        if (CollectionUtils.isEmpty(list))
            return Stream.empty();
        return list.stream()
                .map(fn)
                .map(MathUtils::safeNumber);
    }


    public static BigDecimal round(Object value) {
        return round(value, DEFAULT_SCALE, DEFAULT_ROUND_MODE);
    }

    public static BigDecimal round(Object value, int scale, RoundingMode roundingMode) {
        final BigDecimal decimal = safeNumber(value);
        return decimal.setScale(scale, roundingMode);
    }

    public static <T> BigDecimal count(Collection<T> list, Predicate<T> fn) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        final long count = list.stream()
                .filter(fn)
                .count();
        return safeNumber(count);
    }


    public static <T> BigDecimal avg(Collection<T> list, Function<T, Number> fn) {
        final BigDecimal total = sum(list, fn);
        return divide(total, list.size());
    }

    public static BigDecimal avg(Collection<? extends Number> list) {
        final BigDecimal total = sum(list);
        return divide(total, list.size());
    }

    public static BigDecimal max(Collection<? extends Number> list) {
        return safeNumberStream(list)
                .reduce(BigDecimal.ZERO, MathUtils::max);
    }

    public static <T> BigDecimal max(Collection<T> list, Function<T, Number> fn) {
        return safeNumberStream(list, fn)
                .reduce(BigDecimal.ZERO, MathUtils::max);
    }

    public static BigDecimal min(Collection<? extends Number> list) {
        return safeNumberStream(list)
                .reduce(BigDecimal.ZERO, MathUtils::min);
    }

    public static <T> BigDecimal min(Collection<T> list, Function<T, Number> fn) {
        return safeNumberStream(list, fn)
                .reduce(BigDecimal.ZERO, MathUtils::min);
    }

    public static <T> BigDecimal sum(Collection<T> list, Function<T, Number> fn) {
        return safeNumberStream(list, fn)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal sum(Collection<? extends Number> list) {
        return safeNumberStream(list)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal rate(Number val, Number total) {
        return rate(val, total, DEFAULT_SCALE, DEFAULT_ROUND_MODE);
    }

    public static BigDecimal rate(Number val, Number total, int scale, RoundingMode roundingMode) {
        return divide(val, total, scale, roundingMode)
                .multiply(TypeCastUtils.castToBigDecimal(100)).setScale(scale, roundingMode);
    }

    public static BigDecimal divide(Number val, Number divisor) {
        return divide(val, divisor, DEFAULT_SCALE, DEFAULT_ROUND_MODE);
    }

    public static BigDecimal divide(Number val, Number divisor, int scale, RoundingMode roundingMode) {
        final BigDecimal useDivisor = safeNumber(divisor);
        if (useDivisor.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        final BigDecimal useValue = safeNumber(val);
        return useValue.divide(useDivisor, scale, roundingMode);
    }

    public static BigDecimal add(Number num1, Number num2) {
        return safeNumber(num1).add(safeNumber(num2));
    }

    public static BigDecimal sub(Number num1, Number num2) {
        return safeNumber(num1).subtract(safeNumber(num2));
    }

    public static BigDecimal multiply(Number num1, Number num2) {
        return safeNumber(num1).multiply(safeNumber(num2));
    }

    public static int compare(Number num1, Number num2) {
        return safeNumber(num1).compareTo(safeNumber(num2));
    }

    public static boolean gt(Number num1, Number num2) {
        return compare(num1, num2) > 0;
    }

    public static boolean gte(Number num1, Number num2) {
        return compare(num1, num2) >= 0;
    }

    public static boolean lt(Number num1, Number num2) {
        return compare(num1, num2) < 0;
    }

    public static boolean lte(Number num1, Number num2) {
        return compare(num1, num2) <= 0;
    }

    public static boolean eq(Number num1, Number num2) {
        return compare(num1, num2) == 0;
    }

    public static BigDecimal max(Number num1, Number num2) {
        final BigDecimal n1 = safeNumber(num1);
        final BigDecimal n2 = safeNumber(num2);
        return gt(n1, n2) ? n1 : n2;
    }

    public static BigDecimal min(Number num1, Number num2) {
        final BigDecimal n1 = safeNumber(num1);
        final BigDecimal n2 = safeNumber(num2);
        return lt(n1, n2) ? n1 : n2;
    }
}
