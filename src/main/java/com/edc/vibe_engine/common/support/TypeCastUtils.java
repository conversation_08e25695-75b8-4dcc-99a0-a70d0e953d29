package com.edc.vibe_engine.common.support;


import com.edc.vibe_engine.common.interfaces.IntEnum;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 基础类型转换工具类
 */
public final class TypeCastUtils {

    private static boolean returnSource(Object obj, Class<?> clazz) {
        if (obj == null || clazz == obj.getClass()) {
            return true;
        }

        return clazz.isAssignableFrom(obj.getClass());
    }

    public static String castToString(Object value) {
        if (returnSource(value, String.class)) {
            return (String) value;
        }
        if (value instanceof IntEnum) {
            return String.valueOf(((IntEnum) value).value());
        }
        if (value instanceof TemporalAccessor) {
            return DateUtils.formatTemporal(((TemporalAccessor) value));
        }
        return value.toString();
    }

    public static Byte castToByte(Object value) {
        if (returnSource(value, Byte.class)) {
            return (Byte) value;
        }

        switch (value) {
            case Boolean b -> {
                return castToInt(b).byteValue();
            }
            case Number number -> {
                return number.byteValue();
            }
            case String strVal -> {
                if (isEmptyOrNullStr(strVal)) {
                    return null;
                }
                return Byte.parseByte(strVal);
            }
            default -> {
            }
        }

        return null;
    }

    public static Character castToChar(Object value) {
        if (returnSource(value, Character.class)) {
            return (Character) value;
        }

        if (value instanceof Character) {
            return (Character) value;
        }

        if (value instanceof String strVal) {

            if (strVal.isEmpty()) {
                return null;
            }

            if (strVal.length() != 1) {
                return null;
            }

            return strVal.charAt(0);
        }

        return null;
    }

    public static Short castToShort(Object value) {
        if (returnSource(value, Short.class)) {
            return (Short) value;
        }

        switch (value) {
            case Boolean b -> {
                return castToInt(b).shortValue();
            }
            case Number number -> {
                return number.shortValue();
            }
            case String strVal -> {
                if (isEmptyOrNullStr(strVal)) {
                    return null;
                }

                return Short.parseShort(strVal);
            }
            default -> {
            }
        }

        return null;
    }

    public static BigDecimal castToBigDecimal(Object value) {
        if (returnSource(value, BigDecimal.class)) {
            return (BigDecimal) value;
        }

        if (value instanceof Boolean) {
            return ((Boolean) value) ? BigDecimal.ONE : BigDecimal.ZERO;
        }


        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof BigInteger) {
            return new BigDecimal((BigInteger) value);
        }

        if (value instanceof TemporalAccessor) {
            return new BigDecimal(DateUtils.temporalToNumber((TemporalAccessor) value));
        }

        String strVal = value.toString();
        if (strVal.isEmpty()) {
            return null;
        }

        return new BigDecimal(strVal);
    }

    public static BigInteger castToBigInteger(Object value) {
        if (returnSource(value, BigInteger.class)) {
            return (BigInteger) value;
        }

        if (value instanceof Boolean) {
            return BigInteger.valueOf(((Boolean) value) ? 1L : 0L);
        }

        if (value instanceof BigInteger) {
            return (BigInteger) value;
        }

        if (value instanceof Number) {
            return BigInteger.valueOf(((Number) value).longValue());
        }
        if (value instanceof TemporalAccessor) {
            return BigInteger.valueOf(DateUtils.temporalToNumber((TemporalAccessor) value));
        }

        String strVal = value.toString();
        if (strVal.isEmpty()) {
            return null;
        }

        return new BigInteger(strVal);
    }

    public static Float castToFloat(Object value) {
        if (returnSource(value, Float.class)) {
            return (Float) value;
        }

        if (value instanceof Boolean) {
            return castToInt(value).floatValue();
        }

        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }

        if (value instanceof TemporalAccessor) {
            return DateUtils.temporalToNumber((TemporalAccessor) value).floatValue();
        }

        if (value instanceof String) {
            String strVal = value.toString();
            if (isEmptyOrNullStr(strVal)) {
                return null;
            }

            return Float.parseFloat(strVal);
        }

        return null;
    }

    public static Double castToDouble(Object value) {
        if (returnSource(value, Double.class)) {
            return (Double) value;
        }

        if (value instanceof Boolean) {
            return castToInt(value).doubleValue();
        }

        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }

        if (value instanceof TemporalAccessor) {
            return DateUtils.temporalToNumber((TemporalAccessor) value).doubleValue();
        }

        if (value instanceof String) {
            String strVal = value.toString();
            if (isEmptyOrNullStr(strVal)) {
                return null;
            }

            return Double.parseDouble(strVal);
        }

        return null;
    }

    private static boolean isEmptyOrNullStr(String strVal) {
        if (strVal == null || strVal.isEmpty()) {
            return true;
        }

        return "null".equalsIgnoreCase(strVal);
    }


    public static Long castToLong(Object value) {
        if (returnSource(value, Long.class)) {
            return (Long) value;
        }
        return doCastToLong(value);
    }

    private static Long doCastToLong(Object value) {
        if (value instanceof Boolean) {
            return ((Boolean) value) ? 1L : 0L;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        if (value instanceof String strVal) {
            if (isEmptyOrNullStr(strVal)) {
                return null;
            }

            return Long.parseLong(strVal);
        }
        if (value instanceof TemporalAccessor) {
            return DateUtils.temporalToNumber((TemporalAccessor) value);
        }
        return null;
    }

    public static Integer castToInt(Object value) {
        if (returnSource(value, Integer.class)) {
            return (Integer) value;
        }

        final Long l = doCastToLong(value);
        if (l == null) {
            return null;
        }

        if (l > Integer.MAX_VALUE || l < Integer.MIN_VALUE) {
            throw new NumberFormatException("For input value: \"" + value + "\"" + " out of <Integer> range.");
        }
        return l.intValue();
    }


    public static Boolean castToBoolean(Object value) {
        if (returnSource(value, Boolean.class)) {
            return (Boolean) value;
        }

        switch (value) {
            case Boolean b -> {
                return b;
            }
            case Number number -> {
                return number.intValue() == 1;
            }
            case String strVal -> {

                if (isEmptyOrNullStr(strVal)) {
                    return null;
                }

                if ("true".equalsIgnoreCase(strVal)) {
                    return Boolean.TRUE;
                }
                if ("false".equalsIgnoreCase(strVal)) {
                    return Boolean.FALSE;
                }

                if ("1".equals(strVal)) {
                    return Boolean.TRUE;
                }

                if ("0".equals(strVal)) {
                    return Boolean.FALSE;
                }
            }
            default -> {
            }
        }

        if (value instanceof Character val) {
            return switch (val) {
                case '1' -> Boolean.TRUE;
                case '0' -> Boolean.FALSE;
                default -> null;
            };
        }

        return null;
    }

    //    public static long longValue(BigDecimal decimal) {
    //        if (decimal == null) {
    //            return 0;
    //        }
    //
    //        int scale = decimal.scale();
    //        if (scale >= -100 && scale <= 100) {
    //            return decimal.longValue();
    //        }
    //
    //        return decimal.longValueExact();
    //    }

    public static int intValue(BigDecimal decimal) {
        if (decimal == null) {
            return 0;
        }

        int scale = decimal.scale();
        if (scale >= -100 && scale <= 100) {
            return decimal.intValue();
        }

        return decimal.intValueExact();
    }

    //    @SuppressWarnings({"unchecked"})
    //    public static <T> T castToEnum(Object obj, Class<T> clazz) {
    //        try {
    //            if (returnSource(obj, clazz)) {
    //                return (T) obj;
    //            }
    //
    //            if (obj instanceof String && StringUtils.isNotEmpty(((String) obj))) {
    //                return (T) EnumMapper.getEnumValue(clazz, (String) obj);
    //            }
    //
    //            if (obj instanceof BigDecimal) {
    //                int intValue = intValue((BigDecimal) obj);
    //                return (T) EnumMapper.getEnumValue(clazz, intValue);
    //            }
    //
    //            if (obj instanceof Boolean) {
    //                final int intValue = castToInt(obj);
    //                return (T) EnumMapper.getEnumValue(clazz, intValue);
    //            }
    //
    //            if (obj instanceof Number) {
    //                int intValue = ((Number) obj).intValue();
    //                return (T) EnumMapper.getEnumValue(clazz, intValue);
    //            }
    //        } catch (Exception ex) {
    //            throw new RuntimeException("can not cast to : " + clazz.getName(), ex);
    //        }
    //        throw new RuntimeException("can not cast to : " + clazz.getName());
    //    }

    @SuppressWarnings({"unchecked", "removal"})
    public static <T> T cast(Object obj, Class<T> clazz) {
        if (obj == null) {
            if (clazz == boolean.class) {
                return (T) Boolean.FALSE;
            }
            if (clazz == byte.class) {
                return (T) Byte.valueOf((byte) 0);
            }
            if (clazz == short.class) {
                return (T) Short.valueOf((short) 0);
            }

            if (clazz == int.class) {
                return (T) Integer.valueOf(0);
            }

            if (clazz == long.class) {
                return (T) Long.valueOf(0);
            }

            if (clazz == float.class) {
                return (T) new Float(0F);
            }

            if (clazz == char.class) {
                return (T) new Character(Character.MIN_VALUE);
            }

            if (clazz == double.class) {
                return (T) new Double(0D);
            }
            return null;
        }

        if (clazz == Void.class) {
            return null;
        }

        if (clazz == null) {
            throw new IllegalArgumentException("clazz is null");
        }

        if (clazz == obj.getClass()) {
            return (T) obj;
        }

        if (clazz.isAssignableFrom(obj.getClass())) {
            return (T) obj;
        }

        if (obj instanceof IntEnum) {
            return cast(((IntEnum) obj).value(), clazz);
        }

        if (clazz == boolean.class || clazz == Boolean.class) {
            return (T) castToBoolean(obj);
        }

        if (clazz == byte.class || clazz == Byte.class) {
            return (T) castToByte(obj);
        }


        if (clazz == short.class || clazz == Short.class) {
            return (T) castToShort(obj);
        }

        if (clazz == int.class || clazz == Integer.class) {
            return (T) castToInt(obj);
        }

        if (clazz == long.class || clazz == Long.class) {
            return (T) castToLong(obj);
        }

        if (clazz == float.class || clazz == Float.class) {
            return (T) castToFloat(obj);
        }

        if (clazz == char.class || clazz == Character.class) {
            return (T) castToChar(obj);
        }

        if (clazz == double.class || clazz == Double.class) {
            return (T) castToDouble(obj);
        }

        if (clazz == String.class) {
            return (T) castToString(obj);
        }

        if (clazz == BigDecimal.class) {
            return (T) castToBigDecimal(obj);
        }

        if (clazz == BigInteger.class) {
            return (T) castToBigInteger(obj);
        }

        if (clazz == Date.class) {
            return (T) castToDate(obj);
        }

        if (clazz == LocalDate.class) {
            return (T) castToLocalDate(obj);
        }
        if (clazz == LocalDateTime.class) {
            return (T) castToLocalDateTime(obj);
        }
        if (clazz == LocalTime.class) {
            return (T) castToLocalTime(obj);
        }

        //        if (clazz.isEnum()) {
        //            return castToEnum(obj, clazz);
        //        }

        if (clazz == Set.class) {
            return (T) castToSet(obj);
        }

        if (clazz == List.class || clazz == Collection.class) {
            return (T) castToList(obj);
        }

        return (T) obj;
    }

    private static Date castToDate(Object obj) {
        if (returnSource(obj, Date.class)) {
            return (Date) obj;
        }
        final LocalDateTime dateTime = castToLocalDateTime(obj);
        if (dateTime == null) {
            return null;
        }
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate castToLocalDate(Object obj) {
        if (returnSource(obj, LocalDate.class)) {
            return (LocalDate) obj;
        }
        if (obj instanceof LocalDateTime) {
            return ((LocalDateTime) obj).toLocalDate();
        }
        if (obj instanceof java.sql.Date) {
            return ((java.sql.Date) obj).toLocalDate();
        }
        if (obj instanceof Date) {
            return ((Date) obj).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
        if (obj instanceof String str && !isEmptyOrNullStr(str)) {
            return DateUtils.parseDate(str);
        }
        if (obj instanceof Number) {
            return DateUtils.parseDate(((Number) obj));
        }
        return null;
    }

    public static LocalDateTime castToLocalDateTime(Object obj) {
        if (returnSource(obj, LocalDateTime.class)) {
            return (LocalDateTime) obj;
        }
        if (obj instanceof LocalDate) {
            return ((LocalDate) obj).atTime(0, 0, 0);
        }
        if (obj instanceof java.sql.Date) {
            return ((java.sql.Date) obj).toLocalDate().atTime(0, 0, 0);
        }
        if (obj instanceof Date) {
            return ((Date) obj).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
        if (obj instanceof String && !isEmptyOrNullStr(((String) obj))) {
            return DateUtils.parseDateTime((String) obj);
        }
        if (obj instanceof Number) {
            return DateUtils.parseDateTime(((Number) obj));
        }
        return null;
    }

    public static LocalTime castToLocalTime(Object obj) {
        if (returnSource(obj, LocalTime.class)) {
            return (LocalTime) obj;
        }
        return switch (obj) {
            case Time time -> time.toLocalTime();
            case Date date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            case String s when !isEmptyOrNullStr(s) -> DateUtils.parseTime(s);
            case Number number -> DateUtils.parseTime(number);
            default -> null;
        };
    }

    /**
     * 转换为 List
     *
     * @param value
     * @return
     */
    public static List<Object> castToList(Object value) {
        if (value == null)
            return Collections.emptyList();

        if (value.getClass().isArray()) {
            return Arrays.stream((Object[]) value)
                    .collect(Collectors.toList());
        }

        if (value instanceof List) {
            return (List<Object>) value;
        } else if (value instanceof Iterable) {
            List<Object> result = new ArrayList<>();
            ((Iterable<?>) value).forEach(result::add);
            return result;
        } else {
            List<Object> result = new ArrayList<>();
            result.add(value);
            return result;
        }
    }

    /**
     * 转换为 Set
     *
     * @param value
     * @return
     */
    public static Set<Object> castToSet(Object value) {
        if (value == null)
            return Collections.emptySet();

        if (value.getClass().isArray()) {
            return Arrays.stream((Object[]) value)
                    .collect(Collectors.toSet());
        }

        if (value instanceof Set) {
            return (Set<Object>) value;
        } else if (value instanceof Iterable) {
            Set<Object> result = new HashSet<>();
            ((Iterable<?>) value).forEach(result::add);
            return result;
        } else {
            Set<Object> result = new HashSet<>();
            result.add(value);
            return result;
        }
    }
}
