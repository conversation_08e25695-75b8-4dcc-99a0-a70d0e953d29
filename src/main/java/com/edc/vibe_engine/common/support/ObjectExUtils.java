
package com.edc.vibe_engine.common.support;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

/**
 * 对象工具类
 */
@SuppressWarnings("rawtypes")
public class ObjectExUtils {


    /**
     * 业务值主键值是否合理
     * 1. 字符串类型不为空
     * 2. long 类型不等于0
     *
     * @param value
     * @return
     */
    public static boolean isValidIdValue(Object value) {
        if (isEmpty(value)) {
            return false;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue() > 0;
        }
        if (value instanceof String) {
            final String str = (String) value;
            if (StringUtils.isBlank(str)) {
                return false;
            }
            return !isNumeric(str) || !Objects.equals("0", str);
        }
        return false;
    }

    public static boolean notValidIdValue(Object value) {
        return !isValidIdValue(value);
    }


    public static boolean isNumeric(Object value) {
        if (value instanceof Number) {
            return true;
        }
        return value instanceof String && StringUtils.isNumeric(((String) value));
    }

    public static boolean isSnowflakeId(Object value) {
        if (value instanceof String str) {
            return str.length() >= 18 && StringUtils.isNumeric(str);
        }
        if (value instanceof Integer || value instanceof Long) {
            final long longValue = ((Number) value).longValue();
            return longValue > 0 && getIntegerDigits(longValue) >= 18;
        }
        return false;
    }

    /**
     * 获取整数位数
     *
     * @param number
     * @return
     */
    public static int getIntegerDigits(Number number) {
        long longValue = number.longValue();
        if (longValue == 0) {
            return 1;
        }
        if (longValue < 0) {
            longValue = Math.abs(longValue);
        }

        int length = 0;
        long temp = 1;
        while (temp <= longValue) {
            length++;
            temp *= 10;
        }
        return length;
    }

    /**
     * 判断object是否为空,集合会校验size
     */
    public static boolean isNull(Object... objs) {
        for (Object obj : objs) {
            if (isEmpty(obj)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断object是否不为空,集合会校验size
     */
    public static boolean isNotNull(Object... obj) {
        return !isNull(obj);
    }

    /**
     * 对象非空判断
     */
    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }

    /**
     * 对象空判断
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        return switch (obj) {
            case CharSequence charSequence -> StringUtils.isEmpty(charSequence);
            case Collection collection -> CollectionUtils.isEmpty(collection);
            case Map map -> MapUtils.isEmpty(map);
            case Iterable iterable -> IterableUtils.isEmpty(iterable);
            case Iterator iterator -> IteratorUtils.isEmpty(iterator);
            default -> false;
        };
    }


    public static boolean isEqual(Object a, Object b) {
        if (Objects.equals(a, b)) {
            return true;
        }
        if (a instanceof Map && b instanceof Map) {
            return isEqual((Map) a, (Map) b);
        }
        if (a instanceof Collection && b instanceof Collection) {
            return isEqual((Collection) a, (Collection) b);
        }
        if (a instanceof Number && b instanceof Number) {
            return MathUtils.eq((Number) a, (Number) b);
        }
        if (a == null || b == null || a.getClass() != b.getClass()) {
            return false;
        }
        return false;
    }

    public static boolean isTrue(Object val) {
        return Boolean.TRUE.equals(TypeCastUtils.castToBoolean(val));
    }


    public static boolean notTrue(Object val) {
        return !isTrue(val);
    }


    public static <T> T defaultIfNull(T val, T defaultVal) {
        return val == null ? defaultVal : val;
    }


    public static boolean isEqual(Collection<?> a, Collection<?> b) {
        if (Objects.equals(a, b)) {
            return true;
        }
        if (a == null || b == null || a.size() != b.size()) {
            return false;
        }
        final Iterator<?> iter1 = a.iterator();
        final Iterator<?> iter2 = b.iterator();
        while (iter1.hasNext()) {
            if (!ObjectExUtils.isEqual(iter1.next(), iter2.next())) {
                return false;
            }
        }
        return true;
    }

    public static boolean isEqual(Map<?, ?> a, Map<?, ?> b) {
        if (Objects.equals(a, b)) {
            return true;
        }
        if (a == null || b == null || a.size() != b.size()) {
            return false;
        }

        return a.entrySet().stream()
                .allMatch(e -> ObjectExUtils.isEqual(e.getValue(), b.get(e.getKey())));
    }
}
