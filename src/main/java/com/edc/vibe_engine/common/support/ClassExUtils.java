package com.edc.vibe_engine.common.support;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.Stack;
import java.util.function.Consumer;

public final class ClassExUtils {
    private static final Logger logger = LoggerFactory.getLogger(ClassExUtils.class);

    /**
     * Map with primitive wrapper type as key and corresponding primitive
     * type as value, for example: Integer.class -> int.class.
     */
    private static final Map<Class<?>, Class<?>> PRIMITIVE_WRAPPER_TYPE_MAP = new IdentityHashMap<>(9);

    /**
     * Map with primitive type as key and corresponding wrapper
     * type as value, for example: int.class -> Integer.class.
     */
    private static final Map<Class<?>, Class<?>> PRIMITIVE_TYPE_TO_WRAPPER_MAP = new IdentityHashMap<>(9);

    private static final Set<Class<?>> BASIC_JAVA_TYPES = new HashSet<>(32);

    static {
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Boolean.class, boolean.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Byte.class, byte.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Character.class, char.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Double.class, double.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Float.class, float.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Integer.class, int.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Long.class, long.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Short.class, short.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Void.class, void.class);

        // Map entry iteration is less expensive to initialize than forEach with lambdas
        for (Map.Entry<Class<?>, Class<?>> entry : PRIMITIVE_WRAPPER_TYPE_MAP.entrySet()) {
            PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(entry.getValue(), entry.getKey());
            BASIC_JAVA_TYPES.add(entry.getKey());
            BASIC_JAVA_TYPES.add(entry.getValue());
        }

        BASIC_JAVA_TYPES.add(Number.class);
        BASIC_JAVA_TYPES.add(String.class);
        BASIC_JAVA_TYPES.add(Class.class);
        BASIC_JAVA_TYPES.add(Object.class);

        BASIC_JAVA_TYPES.add(BigDecimal.class);
        BASIC_JAVA_TYPES.add(BigInteger.class);
        BASIC_JAVA_TYPES.add(java.sql.Date.class);
        BASIC_JAVA_TYPES.add(Date.class);
        BASIC_JAVA_TYPES.add(LocalDate.class);
        BASIC_JAVA_TYPES.add(LocalDateTime.class);
        BASIC_JAVA_TYPES.add(LocalTime.class);
        BASIC_JAVA_TYPES.add(Enum.class);
    }

    /**
     * Resolve the given class if it is a primitive class,
     * returning the corresponding primitive wrapper type instead.
     *
     * @param clazz the class to check
     * @return the original class, or a primitive wrapper for the original primitive type
     */
    public static Class<?> resolvePrimitiveIfNecessary(Class<?> clazz) {
        AssertUtils.assertNotNull(clazz, "Class must not be null");
        return (clazz.isPrimitive() && clazz != void.class ? PRIMITIVE_TYPE_TO_WRAPPER_MAP.get(clazz) : clazz);
    }


    /**
     * Check if the given class represents a primitive wrapper,
     * i.e. Boolean, Byte, Character, Short, Integer, Long, Float, Double, or
     * Void.
     *
     * @param clazz the class to check
     * @return whether the given class is a primitive wrapper class
     */
    public static boolean isPrimitiveWrapper(Class<?> clazz) {
        AssertUtils.assertNotNull(clazz, "Class must not be null");
        return PRIMITIVE_WRAPPER_TYPE_MAP.containsKey(clazz);
    }

    /**
     * Check if the given class represents a primitive (i.e. boolean, byte,
     * char, short, int, long, float, or double), {@code void}, or a wrapper for
     * those types (i.e. Boolean, Byte, Character, Short, Integer, Long, Float,
     * Double, or Void).
     *
     * @param clazz the class to check
     * @return {@code true} if the given class represents a primitive, void, or
     * a wrapper class
     */
    public static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        AssertUtils.assertNotNull(clazz, "Class must not be null");
        return (clazz.isPrimitive() || isPrimitiveWrapper(clazz));
    }


    /**
     * 基础类型、可以通过 TypeCastUtils 转换的类型
     *
     * @param type
     * @return
     */
    public static boolean isBasicJavaType(Class<?> type) {
        return type.isEnum() || BASIC_JAVA_TYPES.contains(type);
    }

    public static boolean isCollection(Class<?> c) {
        return Collection.class.isAssignableFrom(c);
    }

    public static boolean isList(Class<?> c) {
        return List.class.isAssignableFrom(c);
    }

    public static boolean isSet(Class<?> c) {
        return Set.class.isAssignableFrom(c);
    }

    public static boolean isMap(Class<?> c) {
        return Map.class.isAssignableFrom(c);
    }

    public static String getOriginalClassName(Object object) {
        return getOriginalClassName(object.getClass());
    }

    public static String getOriginalClassName(Class<?> targetClass) {
        return getOriginalClass(targetClass).getName();
    }

    public static Class<?> getOriginalClass(Class<?> clazz) {
        if (clazz.getName().contains("$$")) {
            Class<?> superclass = clazz.getSuperclass();
            if (superclass != null && superclass != Object.class) {
                return superclass;
            }
        }
        return clazz;
    }

    public static String getSimpleOriginalClassName(Object object) {
        String fullClassName = getOriginalClassName(object);
        int lastNamespace = fullClassName.lastIndexOf('.');
        if (lastNamespace > -1) {
            return fullClassName.substring(lastNamespace + 1);
        }
        return fullClassName;
    }


    //
    //    public static Optional<Class<?>> findImplClass(Class<?> target) {
    //        if (target.isInterface() || Modifier.isAbstract(target.getModifiers())) {
    //            final ImplClass implClass = target.getAnnotation(ImplClass.class);
    //            if (implClass != null) {
    //                final Class<?> aClass = forName(implClass.value());
    //                return Optional.ofNullable(aClass);
    //            }
    //        }
    //        return Optional.empty();
    //    }

    public static Class<?> forName(String usingClassName) {
        if (StringUtils.hasText(usingClassName)) {
            try {
                return Class.forName(usingClassName);
            } catch (ClassNotFoundException ignored) {
            }
        }
        return null;
    }

    public static boolean exist(String usingClassName) {
        return forName(usingClassName) != null;
    }

    //    public static String getMethodAction(Method method) {
    //        final String className = getOriginalClass(method.getDeclaringClass()).getSimpleName();
    //        final String methodName = method.getName();
    //        return String.format("%s-%s", className, methodName);
    //    }


    public static Stack<Class<?>> getClassStack(Class<?> target) {
        Stack<Class<?>> clazzStack = new Stack<>();
        Class<?> targetClass = target;
        while (!Object.class.equals(targetClass)) {
            clazzStack.push(targetClass);
            targetClass = targetClass.getSuperclass();
        }
        return clazzStack;
    }

    public static String mkClassName(String packageName, String className) {
        return packageName + "." + className;
    }


    public static void foreachClassStack(Class<?> target, Consumer<Class<?>> classConsumer) {
        final Stack<Class<?>> clazzStack = getClassStack(target);
        while (!clazzStack.isEmpty()) {
            Class<?> current = clazzStack.pop();
            classConsumer.accept(current);
        }
    }

    public static void ifPresent(String className, Consumer<Class<?>> consumer) {
        Optional.ofNullable(forName(className)).ifPresent(consumer);
    }

    public static boolean isProxyClassName(String className) {
        return className.contains("$")
               || className.startsWith("org.springframework.cglib")
               || className.startsWith("org.springframework.aop")
               || className.startsWith("jdk.internal.reflect")
               || className.startsWith("java.lang.reflect");
    }

}
