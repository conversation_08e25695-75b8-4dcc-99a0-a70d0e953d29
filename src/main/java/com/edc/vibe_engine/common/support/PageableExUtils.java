package com.edc.vibe_engine.common.support;


import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.common.types.ListPageResult;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.function.LongSupplier;
import java.util.stream.Collectors;

/**
 * 参考 spring data 源码修改
 *
 * @see org.springframework.data.repository.support.PageableExecutionUtils
 */
@UtilityClass
public class PageableExUtils {

    /**
     * 内存分页
     *
     * @param content
     * @param pageable
     * @param <T>
     * @return
     */
    public static <T> ListPageResult<T> splitPage(List<T> content, IPageable pageable) {
        AssertUtils.assertNotNull(content, "Content must not be null!");
        AssertUtils.assertNotNull(pageable, "Pageable must not be null!");

        if (pageable.getPage() == 1 && pageable.getPageSize() > content.size()) {
            return ListPageResult.byQuery(content.size(), pageable, content);
        }
        final List<T> result = content.stream()
                .skip(pageable.getOffset())
                .limit(pageable.getLimit()).collect(Collectors.toList());

        return ListPageResult.byQuery(content.size(), pageable, result);
    }

    public static <T> ListPageResult<T> getPage(List<T> content, IPageable pageable, LongSupplier totalSupplier) {

        AssertUtils.assertNotNull(content, "Content must not be null!");
        AssertUtils.assertNotNull(pageable, "Pageable must not be null!");
        AssertUtils.assertNotNull(totalSupplier, "TotalSupplier must not be null!");

        if (pageable.getPage() == 1 && pageable.getPageSize() > content.size()) {
            return ListPageResult.byQuery(content.size(), pageable, content);
        }

        if (!content.isEmpty() && pageable.getPageSize() > content.size()) {
            return ListPageResult.byQuery(pageable.getOffset() + content.size(), pageable, content);
        }

        return ListPageResult.byQuery(totalSupplier.getAsLong(), pageable, content);
    }

}
