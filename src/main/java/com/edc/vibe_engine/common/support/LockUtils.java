package com.edc.vibe_engine.common.support;

import lombok.experimental.UtilityClass;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

@UtilityClass
public class LockUtils {

    /**
     * 读写锁逻辑控制
     *
     * @param lock
     * @param read
     * @param write
     * @param <T>
     * @return
     */
    public static <T> T executeReadWrite(ReadWriteLock lock, Supplier<T> read, Supplier<T> write) {
        T obj;
        lock.readLock().lock();
        try {
            obj = read.get();
            if (null == obj) {
                // Must release read lock before acquiring write lock
                lock.readLock().unlock(); // 释放读锁，获取写锁
                lock.writeLock().lock();
                try {
                    // Recheck state because another thread might have
                    // acquired write lock and changed state before we did.
                    obj = read.get();
                    if (null == obj) {
                        obj = write.get();
                    }
                } finally {
                    // Downgrade by acquiring read lock before releasing write lock
                    lock.readLock().lock();
                    unlock(lock.writeLock());  // Unlock write, still hold read
                }
            }
        } finally {
            unlock(lock.readLock());
        }
        return obj;
    }

    public static <T> T execute(Lock lock, Supplier<T> supplier) {
        lock.lock();
        try {
            return supplier.get();
        } finally {
            unlock(lock);
        }
    }

    public static void execute(Lock lock, Runnable runnable) {
        lock.lock();
        try {
            runnable.run();
        } finally {
            unlock(lock);
        }
    }

    public static void unlock(Lock lock) {
        if (lock instanceof ReentrantLock reentrantLock) {
            // 只有ReentrantLock提供isLocked方法
            if (reentrantLock.isLocked() && reentrantLock.isHeldByCurrentThread()) {
                reentrantLock.unlock();
            }
        } else {
            lock.unlock();
        }
    }
}
