package com.edc.vibe_engine.common.support;


import com.edc.vibe_engine.common.interfaces.IntEnum;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings({"unchecked", "rawtypes"})
@Slf4j
@UtilityClass
public final class EnumMapperUtils {

    private static final Map<Class<?>, Map<Integer, ? extends Enum<?>>> ENUM_VALUE_MAPPINGS = new ConcurrentHashMap<>(64);

    public static boolean isIntEnum(Class<?> clazz) {
        return Enum.class.isAssignableFrom(clazz) && IntEnum.class.isAssignableFrom(clazz);
    }

    @SuppressWarnings("unchecked")
    private static Map<Integer, ? extends Enum<?>> registerEnumClass(Class<?> clazz) {
        Class<? extends Enum<?>> enumClass = (Class<? extends Enum<?>>) clazz;
        return ENUM_VALUE_MAPPINGS.computeIfAbsent(enumClass, kclazz -> {
            final Enum<?>[] constants = enumClass.getEnumConstants();
            Map<Integer, Enum<?>> mappings = new HashMap<>();
            for (Enum<?> constant : constants) {
                IntEnum IntEnum = (IntEnum) constant;
                mappings.put(IntEnum.value(), constant);
            }
            return mappings;
        });
    }

    private static Enum<?> getByIndex(Class<?> clazz, int value) {
        Class<? extends Enum<?>> enumClass = (Class<? extends Enum<?>>) clazz;
        final Enum<?>[] constants = enumClass.getEnumConstants();
        if (constants.length <= value) {
            return null;
        }
        return constants[value];
    }

    public static Enum<?> getEnumValue(Class<?> enumClass, int value) {
        if (isIntEnum(enumClass)) {
            return registerEnumClass(enumClass).get(value);
        }
        return getByIndex(enumClass, value);
    }

    public static Enum<?> getEnumValue(Class<?> enumClass, String value) {
        try {
            if (StringUtils.isNumeric(value)) {
                return getEnumValue(enumClass, Integer.parseInt(value));
            }
            // 用名称解析
            return Enum.valueOf((Class<Enum>) enumClass, value);
        } catch (Exception e) {
            log.error("EnumMapper#getEnumValue error ,enumClass:{},value:{}", enumClass, value, e);
            return null;
        }
    }
}
