package com.edc.vibe_engine.data.connection;

import lombok.SneakyThrows;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.function.Supplier;

public class ConnectionSupplier implements Supplier<Connection> {
    private final String jdbcUrl;
    private final String username;
    private final String password;

    public ConnectionSupplier(String jdbcUrl, String username, String password) {
        this.jdbcUrl = jdbcUrl;
        this.username = username;
        this.password = password;
    }


    @SneakyThrows
    @Override
    public Connection get() {
        return DriverManager.getConnection(jdbcUrl, username, password);
    }
}
