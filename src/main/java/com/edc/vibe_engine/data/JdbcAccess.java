package com.edc.vibe_engine.data;

import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.common.support.PageableExUtils;
import com.edc.vibe_engine.common.types.ListPageResult;
import com.edc.vibe_engine.data.mapper.BasicMapRowMapper;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import com.edc.vibe_engine.data.sql.support.SqlParserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;


public class JdbcAccess {

    private final Logger logger = LoggerFactory.getLogger(JdbcAccess.class);
    private final JdbcTemplate jdbcTemplate;


    public JdbcAccess(DataSource dataSource) {
        this(new JdbcTemplate(dataSource));
    }

    public JdbcAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    //    public DataSource getDataSource() {
    //        return this.jdbcTemplate.getDataSource();
    //    }

    //
    //    @Override
    //    public void execute(Consumer<JdbcTemplate> consumer) {
    //        try {
    //            consumer.accept(jdbcTemplate);
    //        } catch (Exception e) {
    //            logger.error(" JdbcTemplate execute error", e);
    //            throw e;
    //        }
    //    }
    //
    //    @Override
    //    public <R> R executeWithReturn(Function<JdbcTemplate, R> function) {
    //        try {
    //            return function.apply(jdbcTemplate);
    //        } catch (Exception e) {
    //            logger.error(" JdbcTemplate execute error", e);
    //            throw e;
    //        }
    //    }

    public <T> List<T> find(String sql, RowMapper<T> rowMapper, Object... params) {
        return jdbcTemplate.query(sql, rowMapper, params);
    }

    public <T> ListPageResult<T> selectPage(String sql, RowMapper<T> rowMapper, int page, int pageSize, Object... params) {
        final IPageable pageable = IPageable.page(page, pageSize);
        String pageSql = SqlDialectHelper.appendLimitOffset(sql, pageable);

        List<T> list = jdbcTemplate.query(pageSql, rowMapper, params);
        return PageableExUtils.getPage(list, pageable, () -> {
            String countSql = SqlParserUtils.autoCountSql(sql);
            return findLong(countSql, params);
        });
    }

    //    @Override
    public void find(String sql, RowCallbackHandler rowCallbackHandler, Object... params) {
        jdbcTemplate.query(sql, rowCallbackHandler, params);
    }

    //    @Override
    public <T> T findUniqueResult(String sql, RowMapper<T> rowMapper, Object... params) {
        return jdbcTemplate.queryForObject(sql, rowMapper, params);
    }

    //    @Override
    public Map<String, Object> findUniqueMap(String sql, Object... params) {
        try {
            return jdbcTemplate.queryForObject(sql, new BasicMapRowMapper(), params);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    //    @Override
    //    public MapRecord findUniqueRecord(String sql, Object... params) {
    //        return findUniqueResult(sql, new MapRecordRowMapper(), params);
    //    }

    //    @Override
    public int findInteger(String sql, Object... params) {
        Integer number = jdbcTemplate.queryForObject(sql, Integer.class, params);
        return Optional.ofNullable(number).orElse(0);
    }

    //    @Override
    public long findLong(String sql, Object... params) {
        Long number = jdbcTemplate.queryForObject(sql, Long.class, params);
        return Optional.ofNullable(number).orElse(0L);
    }

    //    @Override
    public String findString(String sql, Object... params) {
        return jdbcTemplate.queryForObject(sql, String.class, params);
    }

    //    @Override
    public int execute(String sql, Object... params) {
        return jdbcTemplate.update(sql, params);
    }

    //    @Override
    public int execute(String sql, Consumer<KeyHolder> keyHolderConsumer, Object... params) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        final PreparedStatementCreator preparedStatementCreator = connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS);

            final ArgumentPreparedStatementSetter statementSetter = new ArgumentPreparedStatementSetter(params);
            statementSetter.setValues(ps);
            return ps;
        };
        int updatedRows = jdbcTemplate.update(preparedStatementCreator, keyHolder);
        if (keyHolderConsumer != null) {
            keyHolderConsumer.accept(keyHolder);
        }
        return updatedRows;
    }

    //    @Override
    public List<Map<String, Object>> selectMapList(String sql, Object... params) {
        return jdbcTemplate.query(sql, new BasicMapRowMapper(), params);
    }

    //    @Override
    public ListPageResult<Map<String, Object>> selectMapPage(String sql, int page, int pageSize, Object... params) {
        final IPageable pageable = IPageable.page(page, pageSize);
        String pageSql = SqlDialectHelper.appendLimitOffset(sql, pageable);

        List<Map<String, Object>> list = jdbcTemplate.query(pageSql, new BasicMapRowMapper(), params);
        return PageableExUtils.getPage(list, pageable, () -> {
            String countSql = SqlParserUtils.autoCountSql(sql);
            return findLong(countSql, params);
        });
    }

    //    @Override
    //    public List<MapRecord> selectRecordList(String sql, Object... params) {
    //        return find(sql, new MapRecordRowMapper(), params);
    //    }

    //    @Override
    //    public ListPageResult<MapRecord> selectRecordPage(String sql, int page, int pageSize, Object... params) {
    //        return selectPage(sql, new MapRecordRowMapper(), page, pageSize, params);
    //    }

    //    @Override
    public int[] batchExecute(String sql, List<Object[]> params) {
        return jdbcTemplate.batchUpdate(sql, params);
    }
}
