package com.edc.vibe_engine.data.idgen;


import com.edc.vibe_engine.data.idgen.snowflake.SnowFlakeIdProvider;

import java.util.UUID;


public final class IdGenerator {


    private static final IdProvider ID_PROVIDER = new SnowFlakeIdProvider();

    private IdGenerator() {
    }

    public static String nextSnowId() {
        return String.valueOf(ID_PROVIDER.nextUniqueId());
    }

    public static String uuid() {
        return UUID.randomUUID().toString();
    }
}
