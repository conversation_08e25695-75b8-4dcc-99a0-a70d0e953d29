package com.edc.vibe_engine.data.idgen.snowflake;


import com.edc.vibe_engine.data.idgen.IdProvider;

import java.security.SecureRandom;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class Snow<PERSON>lakeId<PERSON>rovider implements IdProvider {

    private static final long DEFAULT_START_TIMESTAMP = 1735660800000L; // 2025-01-01 00:00:00:000

    private final Map<String, SnowflakeIdGenerator> idGeneratorMap = new ConcurrentHashMap<>();

    private static final int DEFAULT_DATA_CENTER_ID;
    private static final int DEFAULT_MACHINE_ID;

    static {
        try {
            SecureRandom secureRandom = new SecureRandom();
            DEFAULT_DATA_CENTER_ID = secureRandom.nextInt(31);
            DEFAULT_MACHINE_ID = secureRandom.nextInt(31);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public SnowFlakeIdProvider() {
        getIdGenerator(DEFAULT_START_TIMESTAMP, DEFAULT_DATA_CENTER_ID, DEFAULT_MACHINE_ID);
    }

    @Override
    public long nextUniqueId() {
        return nextUniqueId(DEFAULT_DATA_CENTER_ID, DEFAULT_MACHINE_ID);
    }

    @Override
    public long nextUniqueId(long dataCenterId, long machineId) {
        return nextUniqueId(DEFAULT_START_TIMESTAMP, dataCenterId, machineId);
    }

    @Override
    public long nextUniqueId(long startTimestamp, long dataCenterId, long machineId) {
        return getIdGenerator(startTimestamp, dataCenterId, machineId).nextId();
    }

    private SnowflakeIdGenerator getIdGenerator(long startTimestamp, long dataCenterId, long machineId) {
        String key = getKey(dataCenterId, machineId);
        return idGeneratorMap.computeIfAbsent(key, k -> new SnowflakeIdGenerator(startTimestamp, dataCenterId, machineId));
    }

    private static String getKey(long dataCenterId, long machineId) {
        return dataCenterId + "-" + machineId;
    }
}
