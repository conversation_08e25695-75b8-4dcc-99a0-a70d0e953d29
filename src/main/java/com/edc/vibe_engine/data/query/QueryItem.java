package com.edc.vibe_engine.data.query;

import com.edc.vibe_engine.common.support.ObjectExUtils;
import com.edc.vibe_engine.data.enums.Keyword;
import com.edc.vibe_engine.data.query.serializer.QueryItemSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(using = QueryItemSerializer.class)
public class QueryItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 6623852458446099478L;

    /**
     * 可以是 Keyword ，也可以是字段名
     */
    private String key;
    /**
     * 可以是一个 List<WhereItem> ，也可以是条件值
     */
    private Object val;

    public QueryItem(Keyword Keyword, Object val) {
        this.key = Keyword.asSymbol();
        this.val = val;
    }

    public boolean checkIsSymbol() {
        return Keyword.isSymbol(key);
    }

    public boolean checkIsNest() {
        final Keyword kw = Keyword.from(key);
        if (kw == null) {
            return false;
        }
        return kw.isNested();
    }

    public Keyword asKeyWord() {
        return Keyword.from(key);
    }


    public boolean castOrderIsAsc() {
        if (this.val == null) {
            return true;
        } else if (Objects.equals(this.val, "ASC") || Objects.equals(this.val, "asc")) {
            return true;
        } else if (Objects.equals(this.val, "DESC") || Objects.equals(this.val, "desc")) {
            return false;
        } else {
            return ObjectExUtils.isTrue(this.val);
        }
    }
}
