package com.edc.vibe_engine.data.query.serializer;

import com.edc.vibe_engine.data.query.QueryItem;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;

public class QueryItemSerializer extends StdSerializer<QueryItem> {

    @Serial
    private static final long serialVersionUID = -630056643523532425L;

    public QueryItemSerializer() {
        this(null);
    }

    protected QueryItemSerializer(Class<QueryItem> t) {
        super(t);
    }

    @Override
    public void serialize(QueryItem e, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (e == null) {
            jsonGenerator.writeNull();
        } else {
            jsonGenerator.writeStartObject();
            jsonGenerator.writeObjectField(e.getKey(), e.getVal());
            jsonGenerator.writeEndObject();
        }
    }

}
