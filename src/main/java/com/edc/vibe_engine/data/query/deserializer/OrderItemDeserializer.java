package com.edc.vibe_engine.data.query.deserializer;

import com.edc.vibe_engine.common.exception.BaseException;
import com.edc.vibe_engine.data.query.QueryCondition;
import com.edc.vibe_engine.data.query.QueryItem;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class OrderItemDeserializer extends StdDeserializer<List<QueryItem>> {
    @Serial
    private static final long serialVersionUID = -6167799674628253008L;

    public OrderItemDeserializer() {
        this(null);
    }

    public OrderItemDeserializer(JavaType valueType) {
        super(valueType);
    }

    @Override
    public List<QueryItem> deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        try {
            List<QueryItem> result = new ArrayList<>();
            if (node.isArray()) {
                for (int i = 0; i < node.size(); i++) {
                    parseOrderItem(node.get(i), result::add);
                }
            } else {
                parseOrderItem(node, result::add);
            }
            return result;
        } catch (Exception e) {
            throw new BaseException("Fail to parse QueryCondition#order ", e);
        }

    }

    private void parseOrderItem(JsonNode node, Consumer<QueryItem> consumer) {
        if (node.isObject()) {
            node.fields()
                    .forEachRemaining(entry -> {
                        consumer.accept(new QueryItem(entry.getKey(), QueryCondition.parseValue(entry.getValue())));
                    });
        } else if (node.isTextual()) {
            final String text = node.asText();
            final String[] split = text.split(" ");
            if (split.length == 1) {
                consumer.accept(new QueryItem(split[0], true));
            } else {
                consumer.accept(new QueryItem(split[0], split[1]));
            }

        }
    }
}
