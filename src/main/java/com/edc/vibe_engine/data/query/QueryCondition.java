package com.edc.vibe_engine.data.query;

import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.data.enums.Keyword;
import com.edc.vibe_engine.data.interfaces.ICompare;
import com.edc.vibe_engine.data.interfaces.IFunc;
import com.edc.vibe_engine.data.interfaces.INested;
import com.edc.vibe_engine.data.query.deserializer.OrderItemDeserializer;
import com.edc.vibe_engine.data.query.deserializer.WhereItemDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.micrometer.common.util.StringUtils;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

@Getter
public class QueryCondition implements INested<QueryCondition, QueryCondition>,
        ICompare<QueryCondition, String>, IFunc<QueryCondition, String>, Serializable {

    @Serial
    private static final long serialVersionUID = 1427055622243959378L;

    /**
     * json 格式过滤条件
     * 兼容清单快速查询格式
     */
    @JsonDeserialize(using = WhereItemDeserializer.class)
    private List<QueryItem> where;

    /**
     * 排序
     */
    @JsonDeserialize(using = OrderItemDeserializer.class)
    private List<QueryItem> order;

    /**
     * group，服务间使用，暂不支持前端穿参
     */
    private List<String> group;


    /**
     * having，服务间使用，暂不支持前端穿参
     */
    private List<QueryItem> having;

    public QueryCondition() {
        this(new ArrayList<>(), new ArrayList<>());
    }

    public QueryCondition(List<QueryItem> where, List<QueryItem> order) {
        this.where = where;
        this.order = order;
        this.group = new ArrayList<>();
        this.having = new ArrayList<>();
    }

    public QueryCondition(List<QueryItem> where, List<QueryItem> order, List<String> group, List<QueryItem> having) {
        this.where = where;
        this.order = order;
        this.group = group;
        this.having = having;
    }

    public static QueryCondition create() {
        return new QueryCondition();
    }

    private static QueryCondition merge(QueryCondition... queryConditions) {
        final QueryCondition queryCondition = new QueryCondition();
        for (QueryCondition item : queryConditions) {
            if (item == null) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(item.getWhere())) {
                queryCondition.and(condition -> condition.getWhere().addAll(item.getWhere()));
            }
            if (CollectionUtils.isNotEmpty(item.getOrder())) {
                queryCondition.addOrderItems(item.getOrder());
            }
            if (CollectionUtils.isNotEmpty(item.getGroup())) {
                queryCondition.getGroup().addAll(item.getGroup());
            }
            if (CollectionUtils.isNotEmpty(item.getHaving())) {
                queryCondition.getHaving().addAll(item.getHaving());
            }
        }
        return queryCondition;
    }


    /**
     * 函数化
     *
     * @param condition 做不做
     * @param callBack
     * @return Children
     */
    protected final QueryCondition maybeDo(boolean condition, Runnable callBack) {
        if (condition) {
            callBack.run();
        }
        return this;
    }

    private QueryItem getLastNestEmptyItem() {
        if (where.isEmpty()) {
            return null;
        }
        QueryItem last = where.getLast();
        if (last.checkIsNest() && last.getVal() == null) {
            return last;
        }
        return null;
    }

    /**
     * 普通查询条件
     *
     * @param condition 是否执行
     * @param column    属性
     * @param keyword   SQL 关键词
     * @param val       条件值
     */
    public QueryCondition addCondition(boolean condition, String column, Keyword keyword, Object val) {
        return maybeDo(condition, () -> {
            final QueryItem curr;
            if (StringUtils.isNotEmpty(column)) {
                curr = new QueryItem(column, new QueryItem(keyword, val));
            } else {
                curr = new QueryItem(keyword, val);
            }
            addConditionItem(curr);
        });
    }

    public void addConditionItem(QueryItem curr) {
        QueryItem last = getLastNestEmptyItem();
        if (last != null) {
            last.setVal(curr);
        } else {
            where.add(curr);
        }
    }

    @Override
    public QueryCondition eq(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.EQ, val);
    }

    @Override
    public QueryCondition ne(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.NE, val);
    }

    @Override
    public QueryCondition gt(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.GT, val);
    }

    @Override
    public QueryCondition ge(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.GE, val);
    }

    @Override
    public QueryCondition lt(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.LT, val);
    }

    @Override
    public QueryCondition le(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.LE, val);
    }

    @Override
    public QueryCondition between(boolean condition, String column, Object val1, Object val2) {
        return addCondition(condition, column, Keyword.BETWEEN, TypeCastUtils.castToList(Arrays.asList(val1, val2)));
    }

    @Override
    public QueryCondition notBetween(boolean condition, String column, Object val1, Object val2) {
        return addCondition(condition, column, Keyword.NOT_BETWEEN, TypeCastUtils.castToList(Arrays.asList(val1, val2)));
    }

    @Override
    public QueryCondition like(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.LIKE, val);
    }

    @Override
    public QueryCondition notLike(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.NOT_LIKE, val);
    }

    @Override
    public QueryCondition likeLeft(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.L_LIKE, val);
    }

    @Override
    public QueryCondition likeRight(boolean condition, String column, Object val) {
        return addCondition(condition, column, Keyword.R_LIKE, val);
    }

    @Override
    public QueryCondition regexp(boolean condition, String column, String val) {
        return addCondition(condition, column, Keyword.REGEXP, val);
    }

    @Override
    public QueryCondition notRegexp(boolean condition, String column, String val) {
        return addCondition(condition, column, Keyword.NOT_REGEXP, val);
    }

    @Override
    public QueryCondition and(boolean condition, Consumer<QueryCondition> consumer) {
        return maybeDo(condition, () -> {
            QueryCondition subCond = new QueryCondition();
            consumer.accept(subCond);
            addConditionItem(new QueryItem(Keyword.AND, subCond.getWhere()));
        });
    }

    @Override
    public QueryCondition and(boolean condition, QueryCondition other) {
        return maybeDo(condition, () -> addConditionItem(new QueryItem(Keyword.AND, other.getWhere())));
    }

    @Override
    public QueryCondition or(boolean condition) {
        return maybeDo(condition, () -> addConditionItem(new QueryItem(Keyword.OR, null)));
    }

    @Override
    public QueryCondition and(boolean condition) {
        return maybeDo(condition, () -> addConditionItem(new QueryItem(Keyword.AND, null)));
    }

    @Override
    public QueryCondition or(boolean condition, Consumer<QueryCondition> consumer) {
        return maybeDo(condition, () -> {
            QueryCondition subCond = new QueryCondition();
            consumer.accept(subCond);
            addConditionItem(new QueryItem(Keyword.OR, subCond.getWhere()));
        });
    }

    @Override
    public QueryCondition or(boolean condition, QueryCondition other) {
        return maybeDo(condition, () -> addConditionItem(new QueryItem(Keyword.OR, other.getWhere())));
    }

    @Override
    public QueryCondition not(boolean condition, Consumer<QueryCondition> consumer) {
        return maybeDo(condition, () -> {
            QueryCondition subCond = new QueryCondition();
            consumer.accept(subCond);
            addConditionItem(new QueryItem(Keyword.NOT, subCond.getWhere()));
        });
    }

    @Override
    public QueryCondition isNull(boolean condition, String column) {
        return addCondition(condition, column, Keyword.IS_NULL, true);
    }

    @Override
    public QueryCondition isNotNull(boolean condition, String column) {
        return addCondition(condition, column, Keyword.IS_NOT_NULL, true);
    }

    @Override
    public QueryCondition in(boolean condition, String column, Collection<?> coll) {
        return addCondition(condition, column, Keyword.IN, TypeCastUtils.castToList(coll));
    }

    @Override
    public QueryCondition in(boolean condition, String column, Object... values) {
        return addCondition(condition, column, Keyword.IN, TypeCastUtils.castToList(Arrays.asList(values)));
    }

    @Override
    public QueryCondition includesAny(boolean condition, String column, Collection<?> coll) {
        return addCondition(condition, column, Keyword.IN_ANY, coll);
    }

    @Override
    public QueryCondition includesAny(boolean condition, String column, Object... values) {
        return addCondition(condition, column, Keyword.IN_ANY, TypeCastUtils.castToList(Arrays.asList(values)));
    }

    public QueryCondition subIn(String column, Collection<?> coll) {
        return subIn(true, column, coll);
    }

    public QueryCondition subIn(boolean condition, String column, Collection<?> coll) {
        return addCondition(condition, column, Keyword.SUB_IN, TypeCastUtils.castToList(coll));
    }

    public QueryCondition subIn(String column, Object... values) {
        return subIn(true, column, values);
    }

    public QueryCondition subIn(boolean condition, String column, Object... values) {
        return addCondition(condition, column, Keyword.SUB_IN, TypeCastUtils.castToList(Arrays.asList(values)));
    }


    @Override
    public QueryCondition notIn(boolean condition, String column, Collection<?> coll) {
        return addCondition(condition, column, Keyword.NOT_IN, TypeCastUtils.castToList(coll));
    }

    @Override
    public QueryCondition notIn(boolean condition, String column, Object... values) {
        return addCondition(condition, column, Keyword.NOT_IN, TypeCastUtils.castToList(Arrays.asList(values)));
    }


    public QueryCondition subNotIn(String column, Collection<?> coll) {
        return subNotIn(true, column, coll);
    }

    public QueryCondition subNotIn(boolean condition, String column, Collection<?> coll) {
        return addCondition(condition, column, Keyword.SUB_NOT_IN, TypeCastUtils.castToList(coll));
    }

    public QueryCondition subNotIn(String column, Object... values) {
        return subNotIn(true, column, values);
    }

    public QueryCondition subNotIn(boolean condition, String column, Object... values) {
        return addCondition(condition, column, Keyword.SUB_NOT_IN, TypeCastUtils.castToList(Arrays.asList(values)));
    }


    public QueryCondition permIn(String column) {
        return permIn(column, "");
    }

    public QueryCondition permIn(String column, String permItem) {
        return permIn(true, column, permItem);
    }

    /**
     * 过滤字段关联对象的护具权限
     *
     * @param condition 条件
     * @param column    关联字段
     * @param permItem  权限项，默认 use
     * @return
     */
    public QueryCondition permIn(boolean condition, String column, String permItem) {
        return addCondition(condition, column, Keyword.PERM_IN, permItem);
    }

    @Override
    public QueryCondition inSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.IN, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition gtSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.GT, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition geSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.GE, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition ltSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.LT, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition leSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.LE, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition notInSql(boolean condition, String column, String inValue) {
        return addCondition(condition, column, Keyword.NOT_IN, new QueryItem(Keyword.RAW, inValue));
    }

    @Override
    public QueryCondition groupBy(boolean condition, String column) {
        return maybeDo(condition, () -> group.add(column));
    }

    @Override
    public QueryCondition groupBy(boolean condition, List<String> columns) {
        return maybeDo(condition, () -> group.addAll(columns));
    }

    @Override
    public QueryCondition groupBy(boolean condition, String column, String... columns) {
        return maybeDo(condition, () -> {
            group.add(column);
            if (ArrayUtils.isNotEmpty(columns)) {
                group.addAll(Arrays.asList(columns));
            }
        });
    }

    @Override
    public QueryCondition orderBy(boolean condition, boolean isAsc, String column) {
        return maybeDo(condition, () -> addOrderItem(new QueryItem(column, isAsc)));
    }


    private void addOrderItem(QueryItem item) {
        if (order.stream().noneMatch(i -> Objects.equals(i.getKey(), item.getKey()))) {
            order.add(item);
        }
    }

    private void addOrderItems(List<QueryItem> items) {
        for (QueryItem item : items) {
            addOrderItem(item);
        }
    }

    @Override
    public QueryCondition orderBy(boolean condition, boolean isAsc, List<String> columns) {
        return maybeDo(condition, () -> {
            for (String column : columns) {
                addOrderItem(new QueryItem(column, isAsc));
            }
        });
    }

    @Override
    public QueryCondition orderBy(boolean condition, boolean isAsc, String column, String... columns) {
        return maybeDo(condition, () -> {
            addOrderItem(new QueryItem(column, isAsc));
            if (ArrayUtils.isNotEmpty(columns)) {
                for (String col : columns) {
                    addOrderItem(new QueryItem(col, isAsc));
                }
            }
        });
    }

    @Override
    public QueryCondition orderByExp(boolean condition, boolean isAsc, String orderExp) {
        return maybeDo(condition, () -> {
            order.add(new QueryItem(Keyword.RAW, new Object[]{isAsc, orderExp}));
        });
    }

    @Override
    public QueryCondition having(boolean condition, String sqlHaving, Object... params) {
        return maybeDo(condition, () -> {
            Object[] vals = new Object[params.length + 1];
            vals[0] = sqlHaving;
            System.arraycopy(params, 0, vals, 1, params.length);
            having.add(new QueryItem(Keyword.RAW, vals));
        });
    }

    @Override
    public QueryCondition func(boolean condition, Consumer<QueryCondition> consumer) {
        return maybeDo(condition, () -> consumer.accept(this));
    }

    //    public QueryCondition quickFilter(Object val) {
    //        return quickFilter(ObjectUtils.isNotEmpty(val), val);
    //    }
    //
    //    public QueryCondition quickFilter(boolean condition, Object val) {
    //        return maybeDo(condition, () -> addConditionItem(new Item(Keyword.Q_FILTER, val)));
    //    }

    public QueryCondition filterEffect(boolean inEffect) {
        return filterEffect(true, StringConstants.EMPTY, inEffect);
    }

    public QueryCondition filterEffect(String column, boolean inEffect) {
        return filterEffect(true, column, inEffect);
    }

    /**
     * 过滤数据有效性
     *
     * @param condition
     * @param column    过滤字段，为空则处理当前业务对象，不为空则处理字段关联业务对象
     * @param inEffect  true: 过滤有效数据，false 过滤失效数据
     * @return
     */
    public QueryCondition filterEffect(boolean condition, String column, boolean inEffect) {
        return addCondition(condition, column, Keyword.FILTER_EFFECT, inEffect);
    }

    //    public QueryCondition lastSql(boolean condition, String sql) {
    //        return maybeDo(condition, () -> where.add(new Item(Keyword.LAST, sql)));
    //    }

    //    public QueryCondition dynamic(ConditionValueVariableEnum val) {
    //        return dynamic(true, val);
    //    }
    //
    //    public QueryCondition dynamic(String column, ConditionValueVariableEnum val) {
    //        return dynamic(true, column, val);
    //    }
    //
    //    public QueryCondition dynamic(boolean condition, ConditionValueVariableEnum val) {
    //        return dynamic(condition, "", val);
    //    }

    //    public QueryCondition dynamic(boolean condition, String column, ConditionValueVariableEnum val) {
    //        //        if (val == ConditionValueVariableEnum.IN_EFFECT) {
    //        //            return addCondition(condition, column, Keyword.FILTER_EFFECT, true);
    //        //        }
    //        //        if (val == ConditionValueVariableEnum.OUT_EFFECT) {
    //        //            return addCondition(condition, column, Keyword.FILTER_EFFECT, false);
    //        //        }
    //        return maybeDo(condition, () -> {
    //            // 如果有默认字段的话，进行包装
    //            String useCol = StringUtils.defineIfEmpty(column, val.getColumn());
    //            Object wrap = StringUtils.isEmpty(useCol) ? val.getValue() : new Item(val.getColumn(), val.getValue());
    //            addConditionItem(new Item(Keyword.DYNAMIC, wrap));
    //        });
    //    }


    public QueryCondition relative(String column, QueryCondition relative) {
        return relative(true, column, relative);
    }

    public QueryCondition relative(boolean condition, String column, QueryCondition relative) {
        return maybeDo(condition, () -> addConditionItem(new QueryItem(column, new QueryItem(Keyword.RELATIVE, relative.getWhere()))));
    }

    public QueryCondition relative(String column, Consumer<QueryCondition> consumer) {
        return relative(true, column, consumer);
    }

    public QueryCondition relative(boolean condition, String column, Consumer<QueryCondition> consumer) {
        return maybeDo(condition, () -> {
            QueryCondition subCond = new QueryCondition();
            consumer.accept(subCond);
            addConditionItem(new QueryItem(column, new QueryItem(Keyword.RELATIVE, subCond.getWhere())));
        });
    }
    //
    //    public QueryCondition quickFuzzy(String val) {
    //        return quickFuzzy(ObjectUtils.isNotEmpty(val), val);
    //    }
    //
    //    public QueryCondition quickFuzzy(boolean condition, String val) {
    //        return maybeDo(condition, () -> addConditionItem(new Item(Keyword.Q_FUZZY, val)));
    //    }
    //
    //    public QueryCondition applyRaw(String rawSql) {
    //        return applyRaw(StringUtils.hasText(rawSql), rawSql);
    //    }
    //
    //    public QueryCondition applyRaw(boolean condition, String rawSql) {
    //        return maybeDo(condition, () -> addConditionItem(new Item(Keyword.RAW, rawSql)));
    //    }

    //    public static Item refColumn(String column) {
    //        return new Item(Keyword.REF_COL, column);
    //    }

    public void merge(QueryCondition other) {
        if (other == null || other.isEmpty())
            return;
        final QueryCondition merge = QueryCondition.merge(this, other);
        this.where = merge.getWhere();
        this.order = merge.getOrder();
        this.group = merge.getGroup();
        this.having = merge.getHaving();
    }

    @JsonIgnore
    public boolean isEmpty() {
        return this.where.isEmpty() && this.order.isEmpty();
    }

    private static final Function<Map.Entry<String, JsonNode>, QueryItem> parseWhere = entry -> {
        final String key = entry.getKey();
        final JsonNode jsonNode = entry.getValue();
        final Object val = parseVal(jsonNode);
        return new QueryItem(key, val);
    };

    public static QueryItem parseItem(ObjectNode node) {
        if (node.size() == 1) {
            return parseWhere.apply(node.fields().next());
        }
        if (node.has("key")) {
            return new QueryItem(node.get("key").asText(), parseVal(node.get("val")));
        }

        List<QueryItem> result = new ArrayList<>();
        node.fields()
                .forEachRemaining(entry -> result.add(parseWhere.apply(entry)));
        return new QueryItem(Keyword.AND, result);
    }

    public static Object parseVal(JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        if (jsonNode.isObject()) {
            return parseItem((ObjectNode) jsonNode);
        }
        if (jsonNode.isArray()) {
            return parseArray(((ArrayNode) jsonNode));
        }
        return parseValue(jsonNode);
    }

    public static Object parseValue(JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        if (jsonNode.isInt()) {
            return jsonNode.asInt();
        }
        if (jsonNode.isBoolean()) {
            return jsonNode.asBoolean();
        }
        if (jsonNode.isNumber()) {
            return jsonNode.numberValue();
        }
        if (jsonNode.isTextual()) {
            return jsonNode.asText();
        }
        return null;
    }

    private static List<Object> parseArray(ArrayNode jsonNode) {
        List<Object> result = new ArrayList<>();
        for (int i = 0; i < jsonNode.size(); i++) {
            final JsonNode node = jsonNode.get(i);
            result.add(parseVal(node));
        }
        return result;
    }

    /**
     * @return 浅拷贝
     */
    public QueryCondition mutate() {
        return new QueryCondition(new ArrayList<>(this.where), new ArrayList<>(this.order), new ArrayList<>(this.group), new ArrayList<>(this.having));
    }

    public QueryCondition mutate(List<QueryItem> where) {
        return new QueryCondition(new ArrayList<>(where), new ArrayList<>(this.order), new ArrayList<>(this.group), new ArrayList<>(this.having));
    }

    /**
     * 如果参数的某个属性不为空，则取使用输入属性，否则使用原始属性
     *
     * @param other
     * @return
     */
    public QueryCondition mutate(QueryCondition other) {
        final List<QueryItem> where;
        if (CollectionUtils.isEmpty(other.getWhere())) {
            where = new ArrayList<>(this.getWhere());
        } else {
            where = other.getWhere();
        }
        final List<QueryItem> order;
        if (CollectionUtils.isEmpty(other.getOrder())) {
            order = new ArrayList<>(this.getOrder());
        } else {
            order = other.getOrder();
        }
        final List<String> group;
        if (CollectionUtils.isEmpty(other.getGroup())) {
            group = new ArrayList<>(this.getGroup());
        } else {
            group = other.getGroup();
        }
        final List<QueryItem> having;
        if (CollectionUtils.isEmpty(other.getHaving())) {
            having = new ArrayList<>(this.getHaving());
        } else {
            having = other.getHaving();
        }
        return new QueryCondition(where, order, group, having);
    }
}
