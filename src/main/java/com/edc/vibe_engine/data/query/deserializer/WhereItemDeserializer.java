package com.edc.vibe_engine.data.query.deserializer;

import com.edc.vibe_engine.common.exception.BaseException;
import com.edc.vibe_engine.data.query.QueryCondition;
import com.edc.vibe_engine.data.query.QueryItem;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class WhereItemDeserializer extends StdDeserializer<List<QueryItem>> {


    @Serial
    private static final long serialVersionUID = 4073837554831899541L;

    public WhereItemDeserializer() {
        this(null);
    }

    public WhereItemDeserializer(JavaType valueType) {
        super(valueType);
    }

    @Override
    public List<QueryItem> deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);

        try {
            final Object val = QueryCondition.parseVal(node);
            if (val instanceof QueryItem) {
                return new ArrayList<>(Collections.singletonList((QueryItem) val));
            }
            return ((List<QueryItem>) val);
        } catch (Exception e) {
            throw new BaseException("Fail to parse QueryCondition#where ", e);
        }

    }
}
