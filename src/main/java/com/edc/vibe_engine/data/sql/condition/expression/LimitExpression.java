package com.edc.vibe_engine.data.sql.condition.expression;

import com.edc.vibe_engine.common.interfaces.ILimited;
import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;

import java.util.List;

public class LimitExpression extends AbsExpression<ILimited> {


    public LimitExpression push(ILimited limited) {
        this.reset();
        this.addPart(limited);
        return this;
    }

    @Override
    String rebuildPart(List<ILimited> parts) {
        final ILimited limited = parts.getFirst();
        if (limited instanceof IPageable pageable) {
            return SqlDialectHelper.limitOffset(pageable);
        }

        return SqlDialectHelper.limitOffset(null, (long) limited.getLimit());
    }
}
