package com.edc.vibe_engine.data.sql.block;

import com.edc.vibe_engine.data.interfaces.IComputed;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import com.edc.vibe_engine.data.sql.support.SqlParamHelper;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("unchecked")
public abstract class AbsSqlBlock<R> implements Serializable, ISqlPart {

    @Serial
    private static final long serialVersionUID = -7139180369494292258L;

    /**
     * 占位符
     */
    protected final R typedThis = (R) this;


    protected List<Object> params = new ArrayList<>();

    public List<Object> getParams() {
        return params;
    }


    public R set(String column, Object val) {
        return set(true, column, val);
    }

    abstract public R set(boolean condition, String column, Object val);

    protected String formatParam(String column, Object val, boolean json) {
        if (val instanceof IComputed) {
            return ((IComputed<?>) val).getPart(column);
        } else {
            final Object param = SqlParamHelper.casToSqlValue(val, json);
            params.add(param);
            return "?";
        }
    }

    protected final R maybeDo(boolean condition, Runnable something) {
        if (condition) {
            something.run();
        }
        return typedThis;
    }

    public R setOnInsert(String column, Object val) {
        return setOnInsert(true, column, val);
    }

    public abstract R setOnInsert(boolean condition, String column, Object val);

    public abstract boolean isEmpty();
}
