package com.edc.vibe_engine.data.sql.interfaces;

import java.util.List;

public interface ISqlExecutable {

    Object[] EMPTY_OBJECT_ARRAY = new Object[0];

    String getSql();

    List<Object> getParams();

    default boolean isEmpty() {
        return false;
    }

    default Object[] getParamArray() {
        final List<Object> params = getParams();
        if (params == null || params.isEmpty()) {
            return EMPTY_OBJECT_ARRAY;
        }
        return params.toArray(new Object[0]);
    }
}
