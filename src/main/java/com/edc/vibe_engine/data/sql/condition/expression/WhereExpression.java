package com.edc.vibe_engine.data.sql.condition.expression;

import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class WhereExpression extends AbsExpression<String> {

    private String lastPart;

    public WhereExpression and(Consumer<WhereExpression> func) {
        this.push(SqlConstants.AND);
        WhereExpression inner = new WhereExpression();
        func.accept(inner);
        this.push(inner);
        return this;
    }

    public WhereExpression and() {
        this.push(SqlConstants.AND);
        return this;
    }

    public WhereExpression or(Consumer<WhereExpression> func) {
        this.push(SqlConstants.OR);
        WhereExpression inner = new WhereExpression();
        func.accept(inner);
        this.push(inner);
        return this;
    }

    public WhereExpression or() {
        this.push(SqlConstants.OR);
        return this;
    }

    public WhereExpression not(Consumer<WhereExpression> func) {
        WhereExpression inner = new WhereExpression();
        func.accept(inner);
        if (inner.isEmpty()) {
            return this;
        }
        return this.push(SqlConstants.NOT + inner.getPart());
    }

    public WhereExpression push(WhereExpression exp) {
        final String val = exp.getPart();
        return push(val);
    }

    public WhereExpression push(ISqlPart val) {
        return this.push(val.getPart());
    }

    public WhereExpression push(String val) {
        if (SqlConstants.AND.equals(val) || SqlConstants.OR.equals(val)) {
            this.lastPart = val;
        } else {
            if (!isEmpty()) {
                addPart(StringUtils.defaultIfEmpty(this.lastPart, SqlConstants.AND));
            }
            this.addPart(val);
            this.lastPart = null;
        }
        return this;
    }

    @Override
    String rebuildPart(List<String> parts) {
        final String collect = parts.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(""));
        if (StringUtils.isEmpty(collect)) {
            return "";
        }
        if (collect.startsWith("(") && collect.endsWith(")")) {
            return collect;
        }

        return "(" + collect + ")";
    }
}
