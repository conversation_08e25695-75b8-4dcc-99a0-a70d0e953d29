package com.edc.vibe_engine.data.sql.support;

import com.edc.vibe_engine.data.exception.SqlParserException;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParser;
import net.sf.jsqlparser.parser.CCJSqlParserTreeConstants;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.Node;
import net.sf.jsqlparser.parser.ParseException;
import net.sf.jsqlparser.parser.SimpleNode;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Distinct;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.GroupByElement;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.util.deparser.ExpressionDeParser;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;

@UtilityClass
@Slf4j
public class SqlParserUtils {

    private static final Executor THREAD_POOL = Executors.newVirtualThreadPerTaskExecutor();

    private static final Cache<String, Statement> STATEMENT_CACHE = Caffeine.newBuilder()
            .maximumSize(1_000)
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, Expression> EXPRESSION_CACHE = Caffeine.newBuilder()
            .maximumSize(1_000)
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .build();


    /**
     * 获取 jsqlparser 中 count 的 SelectItem
     */
    private static final List<SelectItem<?>> COUNT_SELECT_ITEM = Collections.singletonList(
            new SelectItem<>(new Column().withColumnName("COUNT(*)")).withAlias(new Alias("total"))
    );


    @SuppressWarnings("unchecked")
    public static <T extends Statement> T parse(String sql) throws JSQLParserException {
        // 去除多余空格
        String trimSql = sql.replaceAll("\\s+", " ").replaceAll(", ", ",");
        return (T) STATEMENT_CACHE.get(trimSql, k -> doParseStatement(trimSql));
    }

    @SuppressWarnings("unchecked")
    public static <T extends Expression> T parseExpression(String sql) throws JSQLParserException {
        // 去除多余空格
        String trimSql = sql.replaceAll("\\s+", " ").replaceAll(", ", ",");
        return (T) EXPRESSION_CACHE.get(trimSql, k -> doParseExpression(trimSql));
    }

    private static Statement doParseStatement(String sql) {
        boolean allowComplexParsing = CCJSqlParserUtil.getNestingDepth(sql) <= 10;
        CCJSqlParser parser = CCJSqlParserUtil.newParser(sql).withAllowComplexParsing(allowComplexParsing);
        Statement statement = null;
        try {
            Future<Statement> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return parser.Statement();
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }, THREAD_POOL);

            statement = future.get(1000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException ex) {
            parser.interrupted = true;
            throw new SqlParserException("Time out occurred.", ex);
        } catch (Exception ex) {
            throw new SqlParserException(ex);
        }
        return statement;
    }

    private static Expression doParseExpression(String sql) {
        boolean allowComplexParsing = CCJSqlParserUtil.getNestingDepth(sql) <= 10;
        CCJSqlParser parser = CCJSqlParserUtil.newParser(sql).withAllowComplexParsing(allowComplexParsing);
        Expression expression = null;
        try {
            Future<Expression> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return parser.Expression();
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }, THREAD_POOL);

            expression = future.get(1000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException ex) {
            parser.interrupted = true;
            throw new SqlParserException("Time out occurred.", ex);
        } catch (Exception ex) {
            throw new SqlParserException(ex);
        }
        return expression;
    }

    public static List<SelectItem<?>> parseSelectItems(String selectSql) {

        try {
            Select select = parse(selectSql);
            PlainSelect selectBody = select.getPlainSelect();
            return selectBody.getSelectItems();
        } catch (JSQLParserException e) {
            throw new RuntimeException(String.format("SqlParserUtils#parseSelectItems failed, sql:{%s}. ", selectSql), e);
        }
    }


    /**
     * 截取自 mybatisplus 中 PaginationInnerInterceptor 类
     * 获取自动优化的 countSql
     *
     * @param sql sql
     * @return countSql
     */
    public String autoCountSql(String sql) {
        try {
            Select select = parse(sql);
            // https://github.com/baomidou/mybatis-plus/issues/3920  分页增加union语法支持
            if (select instanceof SetOperationList) {
                return lowLevelCountSql(sql);
            }
            PlainSelect plainSelect = (PlainSelect) select;

            // 优化 order by 在非分组情况下
            List<OrderByElement> orderBy = plainSelect.getOrderByElements();
            if (CollectionUtils.isNotEmpty(orderBy)) {
                boolean canClean = true;
                for (OrderByElement order : orderBy) {
                    // order by 里带参数,不去除order by
                    Expression expression = order.getExpression();
                    if (!(expression instanceof Column) && expression.toString().contains(SqlConstants.ASK)) {
                        canClean = false;
                        break;
                    }
                }
                if (canClean) {
                    plainSelect.setOrderByElements(null);
                }
            }
            Distinct distinct = plainSelect.getDistinct();
            GroupByElement groupBy = plainSelect.getGroupBy();
            // 包含 distinct、groupBy 不优化
            if (null != distinct || null != groupBy) {
                return lowLevelCountSql(select.toString());
            }
            //#95 Github, selectItems contains #{} ${}, which will be translated to ?, and it may be in a function: power(#{myInt},2)
            for (SelectItem<?> item : plainSelect.getSelectItems()) {
                if (item.toString().contains(SqlConstants.ASK)) {
                    return lowLevelCountSql(select.toString());
                }
            }

            // 包含 join 连表,进行判断是否移除 join 连表
            List<Join> joins = plainSelect.getJoins();
            if (CollectionUtils.isNotEmpty(joins)) {
                boolean canRemoveJoin = true;
                String whereS = Optional.ofNullable(plainSelect.getWhere()).map(Expression::toString).orElse(SqlConstants.EMPTY);
                // 不区分大小写
                whereS = whereS.toLowerCase();
                for (Join join : joins) {
                    if (!join.isLeft()) {
                        canRemoveJoin = false;
                        break;
                    }
                    FromItem rightItem = join.getRightItem();
                    String str = "";
                    if (rightItem instanceof Table table) {
                        str = Optional.ofNullable(table.getAlias()).map(Alias::getName).orElse(table.getName()) + SqlConstants.POINT;
                    } else if (rightItem instanceof ParenthesedSelect subSelect) {
                        /* 如果 left join 是子查询，并且子查询里包含 ?(代表有入参) 或者 where 条件里包含使用 join 的表的字段作条件,就不移除 join */
                        if (subSelect.toString().contains(SqlConstants.ASK)) {
                            canRemoveJoin = false;
                            break;
                        }
                        str = subSelect.getAlias().getName() + SqlConstants.POINT;
                    }
                    // 不区分大小写
                    str = str.toLowerCase();

                    if (whereS.contains(str)) {
                        /* 如果 where 条件里包含使用 join 的表的字段作条件,就不移除 join */
                        canRemoveJoin = false;
                        break;
                    }

                    for (Expression expression : join.getOnExpressions()) {
                        if (expression.toString().contains(SqlConstants.ASK)) {
                            /* 如果 join 里包含 ?(代表有入参) 就不移除 join */
                            canRemoveJoin = false;
                            break;
                        }
                    }
                }

                if (canRemoveJoin) {
                    plainSelect.setJoins(null);
                }
            }

            // 优化 SQL
            plainSelect.setSelectItems(COUNT_SELECT_ITEM);
            return select.toString();
        } catch (JSQLParserException e) {
            // 无法优化使用原 SQL
            log.warn("optimize this sql to a count sql has exception, sql:\"{}\", exception:\n{}", sql, e.getCause());
        } catch (Exception e) {
            log.warn("optimize this sql to a count sql has error, sql:\"{}\", exception:\n{}", sql, e);
        }
        return lowLevelCountSql(sql);
    }


    /**
     * 获取 COUNT 原生 SQL 包装
     *
     * @param originalSql ignore
     * @return ignore
     */
    public static String lowLevelCountSql(String originalSql) {
        return String.format("SELECT COUNT(*) FROM (%s) TOTAL", originalSql);
    }

    //    public static String rewriteSelect(String sql, BiFunction<String, Boolean, String> mapFn) {
    //        try {
    //            Select select = parse(sql);
    //            StringBuilder buffer = new StringBuilder();
    //            ExpressionDeParser expressionDeParser = new ExpressionDeParser() {
    //                @Override
    //                public void visit(Column column) {
    //                    final String mapColumn;
    //                    // 在函数里面，或者已经包含别名
    //                    final String name = SqlDialectHelper.unescape(column.getFullyQualifiedName());
    //                    if (isNodeInFunctionItem(column.getASTNode()) || isNodeHasAlias(column.getASTNode())) {
    //                        mapColumn = mapFn.apply(name, false);
    //                    } else {
    //                        mapColumn = mapFn.apply(name, true);
    //                    }
    //                    // 有缓存，不能修改原对象
    //                    buffer.append(mapColumn);
    //                }
    //            };
    //            SelectDeParser deParser = new SelectDeParser(expressionDeParser, buffer) {
    //
    //                @Override
    //                public void visit(AllColumns column) {
    //                    final String mapColumn = mapFn.apply(SqlConstants.ASTERISK, true);
    //                    buffer.append(mapColumn);
    //                }
    //            };
    //            expressionDeParser.setSelectVisitor(deParser);
    //            expressionDeParser.setBuffer(buffer);
    //            select.getSelectBody().accept(deParser);
    //
    //            return buffer.toString();
    //        } catch (JSQLParserException e) {
    //            throw new RuntimeException(String.format("SqlParserUtils#rewriteColumn failed, sql:{%s}. ", sql), e);
    //        }
    //    }

    public static String rewriteExpression(String expSql, BiFunction<String, Boolean, String> mapFn) {
        try {
            Expression expression = parseExpression(expSql);
            StringBuilder buffer = new StringBuilder();
            ExpressionDeParser expressionDeParser = new ExpressionDeParser() {
                @Override
                public void visit(Column column) {
                    final String mapColumn = mapFn.apply(SqlDialectHelper.unescape(column.getFullyQualifiedName()), false);
                    buffer.append(mapColumn);
                }
            };
            expressionDeParser.setBuffer(buffer);

            expression.accept(expressionDeParser);

            return buffer.toString();
        } catch (JSQLParserException e) {
            throw new RuntimeException(String.format("SqlParserUtils#rewriteExpression failed, sql:{%s}. ", expSql), e);
        }
    }

    public static boolean isNodeHasAlias(SimpleNode node) {
        return findNodeAlias(node) != null;
    }

    /**
     * @param node
     * @return
     */
    public static Alias findNodeAlias(SimpleNode node) {
        if (node.jjtGetValue() instanceof SelectItem) {
            return ((SelectItem) node.jjtGetValue()).getAlias();
        }
        final Node parent = node.jjtGetParent();
        if (parent instanceof SimpleNode) {
            return findNodeAlias(((SimpleNode) parent));
        }
        return null;
    }

    /**
     * 判断该 node 是否包含在 SelectItem 中
     *
     * @param node
     * @return
     */
    public static boolean isNodeInSelectItem(SimpleNode node) {
        if (node.getId() == CCJSqlParserTreeConstants.JJTSELECTITEM) {
            return true;
        }
        if (node.jjtGetParent() instanceof SimpleNode) {
            return isNodeInSelectItem(((SimpleNode) node.jjtGetParent()));
        }
        return false;
    }

    /**
     * 判断该 node 是否包含在 FunctionItem 中
     *
     * @param node 节点
     * @return boolean
     */
    public static boolean isNodeInFunctionItem(SimpleNode node) {
        if (node.getId() == CCJSqlParserTreeConstants.JJTFUNCTION) {
            return true;
        }
        if (node.jjtGetParent() instanceof SimpleNode) {
            return isNodeInFunctionItem(((SimpleNode) node.jjtGetParent()));
        }
        return false;
    }


}
