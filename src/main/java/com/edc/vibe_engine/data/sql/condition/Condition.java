package com.edc.vibe_engine.data.sql.condition;


import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;

import java.io.Serial;

public class Condition extends AbsCondition<String, String, Condition> {


    @Serial
    private static final long serialVersionUID = -2623514212625523702L;

    public Condition() {
    }

    @Override
    protected String transformColumn(String column) {
        final int lasted = column.lastIndexOf(".");
        if (lasted >= 0) {
            return column.substring(0, lasted) + "." + SqlDialectHelper.escape(column.substring(lasted + 1));
        }
        return SqlDialectHelper.escape(column);
    }

    @Override
    protected Condition instance() {
        return new Condition();
    }

    @Override
    protected String columnToString(boolean appAlias, String mColumn) {
        return mColumn;
    }
}
