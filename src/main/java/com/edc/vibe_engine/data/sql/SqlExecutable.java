package com.edc.vibe_engine.data.sql;

import com.edc.vibe_engine.data.sql.block.InsertBlock;
import com.edc.vibe_engine.data.sql.block.UpdateBlock;
import com.edc.vibe_engine.data.sql.block.UpsertBlock;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.data.sql.condition.expression.WhereExpression;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.enums.SqlKeyword;
import com.edc.vibe_engine.data.sql.interfaces.ISqlExecutable;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class SqlExecutable implements ISqlExecutable {

    private static final SqlExecutable EMPTY = new SqlExecutable(Collections.emptyList(), Collections.emptyList());

    private static final ISqlPart SQL_ASTERISK = () -> SqlConstants.ASTERISK;
    private final List<ISqlPart> segments;
    private final List<Object> args;

    public static SqlExecutable create(List<ISqlPart> segments, List<Object> args) {
        return new SqlExecutable(segments, args);
    }

    public static SqlExecutable createSelect(String tableName, Condition condition) {
        return createSelect(tableName, SQL_ASTERISK, condition);
    }

    public static SqlExecutable createSelect(String tableName, Collection<String> selectFields, Condition condition) {
        return createSelect(tableName, () -> selectFields.stream()
                .distinct()
                .map(SqlDialectHelper::escape)
                .collect(Collectors.joining(SqlConstants.COMMA)), condition);
    }

    public static SqlExecutable createSelect(String tableName, ISqlPart selectFields, Condition condition) {
        List<ISqlPart> result = new ArrayList<>();
        result.add(SqlKeyword.SELECT);
        //        if (distinct) {
        //            result.add(SqlKeyword.DISTINCT);
        //        }
        result.add(selectFields);
        result.add(SqlKeyword.FROM);
        result.add(() -> SqlDialectHelper.escape(tableName));
        result.add(condition);
        return SqlExecutable.create(result, condition.getParams());
    }

    //    public static List<SqlExecutable> createPatchList(List<PatchInfo> wraps) {
    //        List<SqlExecutable> sqlList = new ArrayList<>();
    //        for (PatchInfo wrap : wraps) {
    //            sqlList.add(createPatch(wrap));
    //        }
    //        return sqlList;
    //    }
    //
    //    public static List<SqlExecutable> createPatchList(String tableName, String primaryField, List<Map<String, Object>> patchs) {
    //        List<SqlExecutable> sqlList = new ArrayList<>();
    //        for (Map<String, Object> patch : patchs) {
    //            sqlList.add(createPatch(tableName, primaryField, patch));
    //        }
    //        return sqlList;
    //    }
    //
    //    public static SqlExecutable createPatch(PatchInfo wrap) {
    //        return createPatch(wrap.getBiz(), wrap.getPrimaryField(), wrap.getPatch());
    //    }
    //
    //    public static SqlExecutable createPatch(String tableName, String primaryField, Map<String, Object> patch) {
    //        AssertUtils.assertFalse(CollectionUtils.isEmpty(patch), "patch body [table={}] is empty!", tableName);
    //        final PatchAction action = PatchHelper.getAction(patch);
    //
    //        final Map<String, Object> patchData = PatchHelper.getPatchData(patch);
    //        String primaryValue = PatchHelper.getPrimaryId(primaryField, patchData);
    //        if (ObjectUtils.notValidIdValue(primaryValue)) {
    //            primaryValue = PatchHelper.getPrimaryId(primaryField, patch);
    //        }
    //        AssertUtils.assertTrue(ObjectUtils.isValidIdValue(primaryValue), "patch primary [table={},action={},primaryField={},primaryValue={}] not valid!", tableName, action, primaryField, primaryValue);
    //
    //
    //        switch (action) {
    //            case ADD: {
    //                AssertUtils.assertTrue(CollectionUtils.isNotEmpty(patchData), "patch data [table={},action={}] is empty!", tableName, action);
    //                patchData.put(primaryField, primaryValue);
    //                InsertBlock block = new InsertBlock();
    //                patchData.forEach(block::set);
    //                return SqlExecutable.createInsert(tableName, block);
    //            }
    //            case EDIT: {
    //                AssertUtils.assertTrue(CollectionUtils.isNotEmpty(patchData), "patch data [table={},action={}] is empty!", tableName, action);
    //                patchData.remove(primaryField);
    //                UpdateBlock block = new UpdateBlock();
    //                patchData.forEach(block::acceptPatch);
    //                return SqlExecutable.createUpdate(tableName, buildPrimaryCondition(primaryField, patch), block);
    //            }
    //            case DEL: {
    //                return SqlExecutable.createDelete(tableName, buildPrimaryCondition(primaryField, patch));
    //            }
    //            default:
    //                throw new UnsupportedOperationException("action:" + action);
    //        }
    //    }

    //    private static Condition buildPrimaryCondition(String primaryField, Map<String, Object> patch) {
    //        return new Condition().eq(primaryField, PatchHelper.getPrimaryId(primaryField, patch));
    //    }

    public static SqlExecutable createInsert(String tableName, Consumer<InsertBlock> blockConsumer) {
        InsertBlock insertBlock = new InsertBlock();
        blockConsumer.accept(insertBlock);

        return createInsert(tableName, insertBlock);
    }

    public static SqlExecutable createInsert(String tableName, InsertBlock insertBlock) {
        if (insertBlock.isEmpty()) {
            return EMPTY;
        }

        List<ISqlPart> result = new ArrayList<>();
        result.add(SqlKeyword.INSERT);
        result.add(() -> SqlDialectHelper.escape(tableName));

        result.add(insertBlock);

        return create(result, insertBlock.getParams());
    }

    public static SqlExecutable createUpdate(String tableName, Consumer<Condition> conditionConsumer, Consumer<UpdateBlock> blockConsumer) {
        UpdateBlock updateBlock = new UpdateBlock();
        blockConsumer.accept(updateBlock);
        Condition condition = new Condition();
        conditionConsumer.accept(condition);
        return createUpdate(tableName, condition, updateBlock);
    }

    public static SqlExecutable createUpdate(String tableName, Condition condition, Consumer<UpdateBlock> blockConsumer) {
        UpdateBlock updateBlock = new UpdateBlock();
        blockConsumer.accept(updateBlock);
        return createUpdate(tableName, condition, updateBlock);
    }

    public static SqlExecutable createUpdate(String tableName, Condition condition, UpdateBlock updateBlock) {
        if (updateBlock.isEmpty()) {
            return EMPTY;
        }

        List<ISqlPart> result = new ArrayList<>();
        result.add(SqlKeyword.UPDATE);
        result.add(() -> SqlDialectHelper.escape(tableName));
        result.add(SqlKeyword.SET);
        result.add(updateBlock);


        final WhereExpression where = condition.getWhere();
        if (where != null && !where.isEmpty()) {
            result.add(SqlKeyword.WHERE);
            result.add(where);
        }

        List<Object> args = new ArrayList<>();
        args.addAll(updateBlock.getParams());
        args.addAll(condition.getParams());

        return SqlExecutable.create(result, args);
    }

    public static SqlExecutable createUpsert(String tableName, Consumer<UpsertBlock> blockConsumer) {
        UpsertBlock upsertBlock = new UpsertBlock();
        blockConsumer.accept(upsertBlock);
        return createUpsert(tableName, upsertBlock);
    }

    public static SqlExecutable createUpsert(String tableName, UpsertBlock upsertBlock) {
        List<ISqlPart> result = new ArrayList<>();
        result.add(SqlKeyword.INSERT);
        result.add(() -> SqlDialectHelper.escape(tableName));
        result.add(upsertBlock);
        final List<String> updateColumns = upsertBlock.getUpdateColumns();
        if (CollectionUtils.isNotEmpty(updateColumns)) {
            result.add(() -> SqlDialectHelper.onDuplicateUpdate(tableName, updateColumns));
        }
        return SqlExecutable.create(result, upsertBlock.getParams());
    }

    public static SqlExecutable createDelete(String tableName, Consumer<Condition> conditionConsumer) {
        Condition condition = new Condition();
        conditionConsumer.accept(condition);
        return createDelete(tableName, condition);
    }

    public static SqlExecutable createDelete(String tableName, Condition condition) {
        List<ISqlPart> result = new ArrayList<>();
        result.add(SqlKeyword.DELETE);
        result.add(SqlKeyword.FROM);
        result.add(() -> SqlDialectHelper.escape(tableName));

        final WhereExpression where = condition.getWhere();
        if (where != null && !where.isEmpty()) {
            result.add(SqlKeyword.WHERE);
            result.add(where);
        }
        return SqlExecutable.create(result, condition.getParams());
    }

    public SqlExecutable(List<ISqlPart> segments, List<Object> args) {
        this.segments = segments;
        this.args = args;
    }


    @Override
    public String getSql() {
        return segments.stream().map(ISqlPart::getPart)
                .filter(StringUtils::isNoneBlank)
                .collect(Collectors.joining(SqlConstants.BLANK));
    }

    @Override
    public List<Object> getParams() {
        return args;
    }

    @Override
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(this.segments) || CollectionUtils.isEmpty(this.args);
    }


    @Override
    public String toString() {
        return String.format("SQL: %s ::: %s", getSql(), getParams().toString());
    }
}
