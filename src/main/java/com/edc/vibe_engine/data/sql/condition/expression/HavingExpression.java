
package com.edc.vibe_engine.data.sql.condition.expression;

import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class HavingExpression extends AbsExpression<String> {


    public HavingExpression push(ISqlPart val) {
        return this.push(val.getPart());
    }

    public HavingExpression push(String val) {
        this.addPart(val);
        return this;
    }

    @Override
    String rebuildPart(List<String> parts) {
        return parts.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
    }
}
