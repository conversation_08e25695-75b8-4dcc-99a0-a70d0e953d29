package com.edc.vibe_engine.data.sql.enums;

import lombok.Getter;

@Getter
public enum SqlDialect {

    MYSQL("mysql", "jdbc:mysql://", "com.mysql.jdbc.Driver"),
    PGSQL("postgresql", "jdbc:postgresql://", "org.postgresql.Driver"),
    ;

    private final String name;
    private final String protocol;
    private final String driverClass;


    SqlDialect(String name, String protocol, String driverClass) {
        this.name = name;
        this.protocol = protocol;
        this.driverClass = driverClass;
    }

    public static SqlDialect fromName(String name) {
        for (SqlDialect value : SqlDialect.values()) {
            if (value.name().equalsIgnoreCase(name) || value.getName().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return MYSQL;
    }

    public static SqlDialect fromJdbc(String jdbc) {
        for (SqlDialect value : SqlDialect.values()) {
            if (jdbc.startsWith(value.getProtocol())) {
                return value;
            }
        }
        throw new UnsupportedOperationException("Unsupported jdbc dialect " + jdbc);
    }
}
