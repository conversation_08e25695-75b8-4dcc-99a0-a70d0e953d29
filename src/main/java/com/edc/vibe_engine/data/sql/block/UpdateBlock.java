package com.edc.vibe_engine.data.sql.block;


import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

public class UpdateBlock extends AbsSqlBlock<UpdateBlock> {
    @Serial
    private static final long serialVersionUID = 5659698053114795342L;
    /**
     * SQL 更新字段内容
     */
    private final List<String> sqlSet = new ArrayList<>();

    @Override
    public UpdateBlock set(boolean condition, String column, Object val) {
        return maybeDo(condition, () -> {
            final String useCol = SqlDialectHelper.escape(column);
            String sql = formatParam(useCol, val, false);
            sqlSet.add(useCol + SqlConstants.EQUAL + sql);
        });
    }

    public UpdateBlock resetPath(String column, String oldPath, String newPath) {
        return resetPath(true, column, oldPath, newPath);
    }

    public UpdateBlock resetPath(boolean condition, String column, String oldPath, String newPath) {
        return maybeDo(condition, () -> {
            // `path` =  concat(?, substring(path, ?))
            params.add(newPath + "/");
            params.add(oldPath.length() + 2);
            final String useCol = SqlDialectHelper.escape(column);

            final String format;
            if (SqlDialectHelper.getCurrent() == SqlDialect.PGSQL) {
                format = " concat(?::text, substring(%s, ?)) ";
            } else {
                format = " concat(?, substring(%s, ?)) ";
            }
            sqlSet.add(useCol + SqlConstants.EQUAL + String.format(format, useCol));
        });
    }

    public UpdateBlock jsonSet(String column, String path, Object val) {
        return jsonSet(true, column, path, val);
    }

    public UpdateBlock jsonSet(boolean condition, String column, String path, Object val) {
        return maybeDo(condition, () -> {
            String sql = formatParam(column, val, true);
            sqlSet.add(SqlDialectHelper.jsonSet(column, sql, path));
        });
    }

    @Override
    public UpdateBlock setOnInsert(boolean condition, String column, Object val) {
        // update 不需要处理
        return this;
    }

    @Override
    public boolean isEmpty() {
        return sqlSet.isEmpty();
    }

    @Override
    public String getPart() {
        AssertUtils.assertFalse(CollectionUtils.isEmpty(sqlSet), "sqlSet is empty");
        return String.join(SqlConstants.COMMA, sqlSet);
    }


    public UpdateBlock jsonArrayAppend(String column, String path, Object val) {
        return jsonArrayAppend(true, column, path, val);
    }

    public UpdateBlock jsonArrayAppend(String column, Object val) {
        return jsonArrayAppend(true, column, null, val);
    }

    public UpdateBlock jsonArrayAppend(boolean condition, String column, String path, Object val) {
        return maybeDo(condition, () -> {
            String sql = formatParam(column, val, true);
            sqlSet.add(SqlDialectHelper.jsonArrayAppend(column, sql, path));
        });
    }

    //    public void acceptPatch(String column, Object val) {
    //        final String[] arr = StringUtils.split(column, "::");
    //        if (arr.length > 1) {
    //            if (Objects.equals(arr[0], PatchHelper.FUN_JSON_ARRAY_APPEND)) {
    //                this.jsonArrayAppend(arr[1], val);
    //            } else if (Objects.equals(arr[0], PatchHelper.FUN_JSON_SET)) {
    //                this.jsonSet(arr[1], arr[2], val);
    //            }
    //        } else {
    //            this.set(arr[0], val);
    //        }
    //    }
}
