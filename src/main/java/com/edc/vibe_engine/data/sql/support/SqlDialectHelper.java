package com.edc.vibe_engine.data.sql.support;


import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.interfaces.ILimited;
import com.edc.vibe_engine.common.interfaces.IPageable;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import com.edc.vibe_engine.data.sql.enums.SqlKeyword;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

public class SqlDialectHelper {


    public static final String SUFFIX_MYSQL_CONNECT_OPTIONS = "?rewriteBatchedStatements=true&useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&tinyInt1isBit=false&allowMultiQueries=true&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true";
    public static final String SUFFIX_PGSQL_CONNECT_OPTIONS = "?stringtype=unspecified&reWriteBatchedInserts=true";
    private static SqlDialect global = SqlDialect.PGSQL;

    //    private static final ThreadLocal<SqlDialect> CURR_SQL_DIALECT = new ThreadLocal<>();
    //
    public static void setGlobal(SqlDialect global) {
        SqlDialectHelper.global = global;
    }
    //
    //    public static SqlDialect getGlobal() {
    //        return global;
    //    }

    public static SqlDialect getCurrent() {
        return global;
    }

    //    public static void guardDialect(SqlDialect dialect, Runnable supplier) {
    //        guardDialect(dialect, () -> {
    //            supplier.run();
    //            return null;
    //        });
    //    }

    //    public static <T> T guardDialect(SqlDialect dialect, Supplier<T> supplier) {
    //        try {
    //            CURR_SQL_DIALECT.set(dialect);
    //            return supplier.get();
    //        } finally {
    //            CURR_SQL_DIALECT.remove();
    //        }
    //    }

    public static String escape(String name) {
        return escape(getCurrent(), name);
    }

    public static String escape(SqlDialect sqlDialect, String name) {
        if (sqlDialect == SqlDialect.PGSQL) {
            if (name.contains("\"")) {
                return name;
            }
            return "\"" + name + "\"";
        } else {
            if (name.contains("`")) {
                return name;
            }
            return "`" + name + "`";
        }
    }


    public static String unescape(String name) {
        return unescape(getCurrent(), name);
    }

    public static String unescape(SqlDialect sqlDialect, String name) {
        return name.replace("\"", "").replace("`", "");
    }


    public static String jsonColumn(String field, String path) {
        return jsonColumn(getCurrent(), field, path);
    }

    public static String jsonColumn(SqlDialect sqlDialect, String field, String path) {
        final String jsonPath = toJsonPath(sqlDialect, path);
        if (sqlDialect == SqlDialect.PGSQL) {
            return String.format("%s#>'%s'", escape(sqlDialect, field), jsonPath);
        }
        return String.format("%s ->> '%s'", escape(sqlDialect, field), jsonPath);
    }


    public static String jsonContains(String field, String value, String path, boolean exist) {
        return jsonContains(getCurrent(), field, value, path, exist);
    }

    public static String jsonContains(SqlDialect sqlDialect, String field, String value, String path, boolean exist) {
        final String jsonPath = toJsonPath(sqlDialect, path);
        final String prefix = exist ? StringUtils.EMPTY : SqlConstants.NOT;
        if (sqlDialect == SqlDialect.PGSQL) {
            return String.format("%s COALESCE(%s#>'%s' @> %s::jsonb, false)", prefix, escape(sqlDialect, field), jsonPath, value);
        } else {
            return String.format("%s IFNULL(JSON_CONTAINS(%s, %s,'%s'), false)", prefix, escape(sqlDialect, field), value, jsonPath);
        }
    }

    public static String jsonEqual(String field, String value, String path, boolean isEq) {
        return jsonEqual(getCurrent(), field, value, path, isEq);
    }

    public static String jsonEqual(SqlDialect sqlDialect, String field, String value, String path, boolean isEq) {
        final String jsonPath = toJsonPath(sqlDialect, path);
        final String prefix = isEq ? SqlConstants.EMPTY : SqlConstants.NOT;
        if (sqlDialect == SqlDialect.PGSQL) {
            return String.format("%s COALESCE(%s#>'%s' = %s::jsonb, false)", prefix, escape(sqlDialect, field), jsonPath, value);
        } else {
            return String.format("%s IFNULL(JSON_CONTAINS(%s, %s,'%s'), false)", prefix, escape(sqlDialect, field), value, jsonPath);
        }
    }

    private static String toJsonPath(SqlDialect sqlDialect, String path) {
        if (sqlDialect == SqlDialect.PGSQL) {
            if (StringUtils.isEmpty(path)) {
                return "{}";
            }
            return Arrays.stream(StringUtils.split(path, "."))
                    .collect(Collectors.joining(",", "{", "}"));
        } else {
            if (StringUtils.isEmpty(path)) {
                return SqlConstants.DOLLAR_TAG;
            }
            if (path.startsWith(SqlConstants.DOLLAR_TAG)) {
                return path;
            }
            return SqlConstants.DOLLAR_TAG + "." + path;
        }
    }


    public static String jsonIsNull(String field) {
        return jsonIsNull(getCurrent(), field);
    }

    public static String jsonIsNull(SqlDialect sqlDialect, String field) {
        if (sqlDialect == SqlDialect.PGSQL) {
            return String.format("%s::jsonb = 'null'::jsonb", escape(sqlDialect, field));
        } else {
            return String.format("JSON_TYPE(%s) = 'NULL'", escape(sqlDialect, field));
        }
    }

    public static String jsonNotNull(String field) {
        return jsonNotNull(getCurrent(), field);
    }

    public static String jsonNotNull(SqlDialect sqlDialect, String field) {
        if (sqlDialect == SqlDialect.PGSQL) {
            return String.format("%s::jsonb != 'null'::jsonb", escape(sqlDialect, field));
        } else {
            return String.format("JSON_TYPE(%s) != 'NULL'", escape(sqlDialect, field));
        }
    }

    public static String jsonSet(String field, String value, String path) {
        return jsonSet(getCurrent(), field, value, path);
    }

    public static String jsonSet(SqlDialect sqlDialect, String field, String value, String path) {
        final String jsonPath = toJsonPath(sqlDialect, path);
        final String column = escape(sqlDialect, field);
        if (sqlDialect == SqlDialect.PGSQL) {
            if (jsonPath.equals("{}")) {
                return String.format("%s = %s::jsonb", column, value);
            } else {
                return String.format("%s = jsonb_set(%s, '%s', %s)", column, column, jsonPath, value);
            }
        } else {
            return String.format("%s = JSON_SET(%s, '%s', %s)", column, column, jsonPath, value);
        }
    }

    public static String jsonArrayAppend(String field, String value, String path) {
        return jsonArrayAppend(getCurrent(), field, value, path);
    }

    public static String jsonArrayAppend(SqlDialect sqlDialect, String field, String value, String path) {
        final String jsonPath = toJsonPath(sqlDialect, path);
        final String column = escape(sqlDialect, field);
        if (sqlDialect == SqlDialect.PGSQL) {
            if (jsonPath.equals("{}")) {
                return String.format("%s = %s || %s::jsonb", column, column, value);
            } else {
                return String.format("%s = jsonb_set(%s,'%s',%s#>'%s' || %s::jsonb)", column, column, jsonPath, column, jsonPath, value);
            }
        } else {
            return String.format("%s = JSON_ARRAY_APPEND(%s, '%s', CAST(%s as json))", column, column, jsonPath, value);
        }
    }


    public static String onDuplicateUpdate(String tableName, List<String> updateColumns) {
        return onDuplicateUpdate(getCurrent(), tableName, updateColumns);
    }

    public static String onDuplicateUpdate(SqlDialect sqlDialect, String tableName, List<String> updateColumns) {
        if (CollectionUtils.isEmpty(updateColumns)) {
            return StringUtils.EMPTY;
        }
        StringBuilder builder = new StringBuilder(80);
        if (sqlDialect == SqlDialect.PGSQL) {
            // pg 支持指定冲突条件，目前先按默认主键约束
            builder.append(" ON CONFLICT ON CONSTRAINT ").append(tableName).append("_pk")
                    .append(" DO UPDATE SET ");
            for (int i = 0; i < updateColumns.size(); i++) {
                final String column = escape(sqlDialect, updateColumns.get(i));
                builder.append(column).append('=').append("excluded.").append(column);
                if (i < updateColumns.size() - 1) {
                    builder.append(',');
                }
            }
            return builder.toString();
        } else {
            builder.append(SqlConstants.DUPLICATE_UPDATE).append(' ');
            for (int i = 0; i < updateColumns.size(); i++) {
                final String column = escape(sqlDialect, updateColumns.get(i));
                builder.append(column).append('=').append("VALUES(").append(column).append(')');
                if (i < updateColumns.size() - 1) {
                    builder.append(',');
                }
            }
            return builder.toString();
        }
    }

    public static String appendLimitOffset(String sql, ILimited limited) {
        return appendLimitOffset(getCurrent(), sql, limited);
    }

    public static String appendLimitOffset(SqlDialect sqlDialect, String sql, ILimited limited) {
        return sql + limitOffset(sqlDialect, limited);
    }

    public static String limitOffset(ILimited limited) {
        return limitOffset(getCurrent(), limited);
    }

    public static String limitOffset(Long offset, Long size) {
        return limitOffset(getCurrent(), offset, size);
    }

    public static String limitOffset(SqlDialect sqlDialect, ILimited limited) {
        if (limited == null)
            return StringConstants.EMPTY;
        if (limited instanceof IPageable pageable) {
            return limitOffset(sqlDialect, (long) pageable.getOffset(), (long) pageable.getPageSize());
        } else {
            return limitOffset(null, (long) limited.getLimit());
        }
    }

    public static String limitOffset(SqlDialect sqlDialect, Long offset, Long size) {
        if (offset != null && size != null) {
            return String.format(" LIMIT %d OFFSET %d ", size, offset);
        }
        if (offset != null) {
            return String.format(" OFFSET %d ", offset);
        }
        if (size != null) {
            return String.format(" LIMIT %d ", size);
        }
        return "";
    }

    public static String keywordAsSql(SqlKeyword keyword) {
        return keywordAsSql(getCurrent(), keyword);
    }

    public static String keywordAsSql(SqlDialect sqlDialect, SqlKeyword keyword) {
        if (sqlDialect == SqlDialect.PGSQL) {
            if (keyword == SqlKeyword.LIKE) {
                return SqlConstants.ILIKE;
            }
            if (keyword == SqlKeyword.NOT_LIKE) {
                return SqlConstants.NOT_ILIKE;
            }
            if (keyword == SqlKeyword.REGEXP) {
                return " ~ ";
            }
            if (keyword == SqlKeyword.NOT_REGEXP) {
                return " !~ ";
            }
            return keyword.getKeyword();
        } else {
            return keyword.getKeyword();
        }
    }

    public static String buildSimpleUpsertSql(String tableName, String idKey, List<String> columns) {
        StringJoiner fields = new StringJoiner(",");
        StringJoiner arg = new StringJoiner(",");
        for (String column : columns) {
            fields.add(escape(column));
            arg.add("?");
        }
        final String sql = String.format("insert into %s(%s) values (%s)", escape(tableName), fields, arg);

        final ArrayList<String> updates = new ArrayList<>(columns);
        updates.remove(idKey);

        final String update = onDuplicateUpdate(tableName, updates);
        return sql + update;
    }

    public static SqlDialect getSqlDialect(DataSource dataSource) {
        try {
            final String jdbcUrl = dataSource.getConnection().getMetaData().getURL();
            return SqlDialect.fromJdbc(jdbcUrl);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public static void testJdbcDriver(SqlDialect dialect) {
        try {
            Class.forName(dialect.getDriverClass());
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(String.format("Driver for name [%s] not found!", dialect), e);
        }
    }

    public static String getJdbcUrlPath(SqlDialect dialect, String catalog) {
        if (dialect == SqlDialect.PGSQL) {
            if (StringUtils.isNotBlank(catalog)) {
                return catalog;
            } else {
                return "postgres";
            }
        } else {
            return catalog;
        }
    }

    public static String getJdbcUrlQueryString(SqlDialect dialect, String schema) {
        if (dialect == SqlDialect.PGSQL) {
            if (StringUtils.isEmpty(schema)) {
                return SUFFIX_PGSQL_CONNECT_OPTIONS;
            } else {
                return SUFFIX_PGSQL_CONNECT_OPTIONS + "&currentSchema=" + schema;
            }
        }
        return SUFFIX_MYSQL_CONNECT_OPTIONS;
    }
}
