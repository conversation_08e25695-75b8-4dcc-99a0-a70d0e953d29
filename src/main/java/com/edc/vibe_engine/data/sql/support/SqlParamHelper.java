package com.edc.vibe_engine.data.sql.support;


import com.edc.vibe_engine.common.interfaces.IntEnum;
import com.edc.vibe_engine.common.support.ClassExUtils;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.json.JSON;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

public class SqlParamHelper {

    public static Object casToSqlValue(Object val, boolean isJson) {
        if (isJson) {
            return JSON.toJSON(val);
        } else if (val == null) {
            return null;
        } else if (val instanceof IntEnum) {
            return ((IntEnum) val).value();
        } else if (val.getClass().isEnum()) {
            return ((Enum<?>) val).name();
        } else if (val instanceof Timestamp) {
            return TypeCastUtils.castToLocalDateTime(val);
        } else if (val instanceof Date) {
            return TypeCastUtils.castToLocalDate(val);
        } else if (val instanceof Time) {
            return TypeCastUtils.castToLocalTime(val);
        } else if (val instanceof java.util.Date) {
            return TypeCastUtils.castToLocalDateTime(val);
        } else if (ClassExUtils.isBasicJavaType(val.getClass())) {
            return val;
        } else {
            return JSON.toJSON(val);
        }
    }
}
