package com.edc.vibe_engine.data.sql.block;


import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class UpsertBlock extends AbsSqlBlock<UpsertBlock> {
    @Serial
    private static final long serialVersionUID = 3134711477655854432L;
    /**
     * SQL 更新字段内容
     */
    private final List<String> insertColumns = new ArrayList<>();
    private final List<String> insertValues = new ArrayList<>();
    private final List<String> updateColumns = new ArrayList<>();

    @Override
    public UpsertBlock set(boolean condition, String column, Object val) {
        return maybeDo(condition, () -> {
            String sql = formatParam(column, val, false);
            insertColumns.add(column);
            insertValues.add(sql);
            updateColumns.add(column);
        });
    }

    @Override
    public UpsertBlock setOnInsert(boolean condition, String column, Object val) {
        return maybeDo(condition, () -> {
            String sql = formatParam(column, val, false);
            insertColumns.add(column);
            insertValues.add(sql);
        });
    }

    @Override
    public boolean isEmpty() {
        return insertColumns.isEmpty();
    }

    @Override
    public String getPart() {
        AssertUtils.assertFalse(isEmpty(), "insert is empty");
        // (f1,f2,f3)
        final String insertSql = insertColumns.stream().map(SqlDialectHelper::escape).collect(Collectors.joining(SqlConstants.COMMA));
        final String valueSql = String.join(SqlConstants.COMMA, insertValues);
        return String.format("(%s) values (%s)", insertSql, valueSql);
    }

    public List<String> getUpdateColumns() {
        return updateColumns;
    }
}
