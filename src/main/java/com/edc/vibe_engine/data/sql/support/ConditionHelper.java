package com.edc.vibe_engine.data.sql.support;


import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.support.ClassExUtils;
import com.edc.vibe_engine.common.support.StringExUtils;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.data.enums.Keyword;
import com.edc.vibe_engine.data.query.QueryCondition;
import com.edc.vibe_engine.data.query.QueryItem;
import com.edc.vibe_engine.data.sql.condition.Condition;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;


@UtilityClass
public class ConditionHelper {

    public Condition convertToCondition(QueryCondition queryCondition) {
        return convertToCondition(queryCondition, Function.identity(), (k, v) -> v);
    }

    public Condition convertToCondition(QueryCondition queryCondition, Function<String, String> columnFn) {
        return convertToCondition(queryCondition, columnFn, (k, v) -> v);
    }

    public Condition convertToCondition(QueryCondition queryCondition, Function<String, String> columnFn, BiFunction<String, Object, Object> paramFn) {
        Condition result = new Condition();

        final List<QueryItem> where = queryCondition.getWhere();
        adapterWhere(result, StringConstants.EMPTY, where, columnFn, paramFn);

        // 处理 orderBy 条件
        final List<QueryItem> orderBy = queryCondition.getOrder();
        if (!CollectionUtils.isEmpty(orderBy)) {
            orderBy.forEach(item -> {
                result.orderBy(true, item.castOrderIsAsc(), columnFn.apply(item.getKey()));
            });
        }

        return result;
    }


    /**
     * 转换查询 where 条件
     *
     * @param condition
     * @param parentKey
     * @param where     查询项，可能是一个或列表
     * @param columnFn
     * @param paramFn
     */
    public void adapterWhere(Condition condition, String parentKey, Object where, Function<String, String> columnFn, BiFunction<String, Object, Object> paramFn) {
        flatItemValue(where, item -> adapterWhereItem(condition, parentKey, item, columnFn, paramFn));
    }

    /**
     * 转换查询条件项
     *
     * @param condition
     * @param column    上一级的关键字
     * @param entry     条件项
     * @param columnFn
     * @param paramFn
     */
    private void adapterWhereItem(Condition condition, String column, QueryItem entry, Function<String, String> columnFn, BiFunction<String, Object, Object> paramFn) {
        final String key = entry.getKey();
        if (Keyword.isSymbol(key)) {
            final Keyword keyword = Keyword.from(key);
            if (keyword == null) {
                return;
            }
            switch (keyword) {
                case AND:
                    if (ObjectUtils.isEmpty(entry.getVal())) {
                        condition.and();
                    } else {
                        condition.and(isNotEmpty(entry.getVal()), i -> adapterWhere(i, column, entry.getVal(), columnFn, paramFn));
                    }
                    break;
                case OR:
                    if (ObjectUtils.isEmpty(entry.getVal())) {
                        condition.or();
                    } else {
                        condition.or(i -> adapterWhere(i, column, entry.getVal(), columnFn, paramFn));
                    }
                    break;
                case NOT:
                    condition.not(i -> adapterWhere(i, column, entry.getVal(), columnFn, paramFn));
                    break;
                case IN:
                    condition.in(columnFn.apply(column), castParamList(column, entry, paramFn));
                    break;
                case NOT_IN:
                    condition.notIn(columnFn.apply(column), castParamList(column, entry, paramFn));
                    break;
                case IN_ANY:
                    condition.jsonArrayIn(columnFn.apply(column), castParamList(column, entry, paramFn));
                    break;
                case LIKE:
                    condition.like(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                case NOT_LIKE:
                    condition.notLike(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                case EQ:
                    condition.eq(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                case NE:
                    condition.ne(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                case GT: {
                    condition.gt(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                }
                break;
                case GE: {
                    condition.ge(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                }
                break;
                case LT: {
                    condition.lt(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                }
                break;
                case LE: {
                    condition.le(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                }
                break;
                case IS_NULL:
                    condition.isNull(columnFn.apply(column));
                    break;
                case IS_NOT_NULL:
                    condition.isNotNull(columnFn.apply(column));
                    break;
                //                case EXISTS -> condition.exists(isRawSql(entry.getVal()), getRawSql(entry.getVal()));
                //                case NOT_EXISTS -> condition.notExists(isRawSql(entry.getVal()), getRawSql(entry.getVal()));
                case BETWEEN: {
                    final List<Object> objectList = TypeCastUtils.castToList(entry.getVal());
                    condition.between(isNotEmpty(objectList), columnFn.apply(column), paramFn.apply(column, objectList.get(0)), paramFn.apply(column, objectList.get(1)));
                }
                break;
                case NOT_BETWEEN: {
                    final List<Object> objectList = TypeCastUtils.castToList(entry.getVal());
                    condition.notBetween(isNotEmpty(objectList), columnFn.apply(column), paramFn.apply(column, objectList.get(0)), paramFn.apply(column, objectList.get(1)));
                }
                break;
                case L_LIKE:
                    condition.likeLeft(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                case R_LIKE:
                    condition.likeRight(columnFn.apply(column), paramFn.apply(column, entry.getVal()));
                    break;
                //                case LAST:
                //                    condition.last((String) entry.getVal());
                //                    break;
                // 快速过滤条件，适配前端交互，处理增强
                //                case Q_FILTER -> adapterQuickFilter(condition, entry.getVal());
                // 模糊匹配条件，目前没有使用
                //                case Q_FUZZY -> condition.quickFuzzy(String.valueOf(entry.getVal()));
                // 原始 sql 条件，可能有 sql 注入风险
                //                case RAW -> condition.apply(isNotEmpty(entry.getVal()), String.valueOf(entry.getVal()));
                // 动态条件, 目前主要处理权限
                //                case DYNAMIC -> adapterDynamic(condition, column, entry);
                // 跨服务条件
                //                case RELATIVE -> adapterRelativeWhere(condition, column, entry.getVal());
                // 无数据权限
                case NO_AUTH:
                    condition.apply("1=0");
                    break;
                default:
                    break;
            }
        } else {
            final String joinColumn = StringExUtils.join(StringConstants.DOLLAR, column, key);
            final Object val = entry.getVal();
            if (ClassExUtils.isBasicJavaType(val.getClass())) {
                // 基础类型，直接按 eq 处理
                condition.eq(columnFn.apply(joinColumn), paramFn.apply(joinColumn, val));
            } else {
                adapterWhere(condition, joinColumn, val, columnFn, paramFn);
            }
        }
    }

    private static List<Object> castParamList(String column, QueryItem entry, BiFunction<String, Object, Object> paramFn) {
        return TypeCastUtils.castToList(entry.getVal()).stream()
                .map(v -> paramFn.apply(column, v))
                .collect(Collectors.toList());
    }


    /**
     * 获取 sql 条件
     *
     * @param value
     * @return
     */
    public static String getRawSql(Object value) {
        if (value instanceof QueryItem) {
            return String.valueOf(((QueryItem) value).getVal());
        }
        return StringUtils.EMPTY;
    }

    public static <T> boolean tryApplyRawValue(Object value, Consumer<T> consumer) {
        if (isRawItemValue(value)) {
            consumer.accept((T) ((QueryItem) value).getVal());
            return true;
        }
        return false;
    }

    /**
     * 判断给定的值是否为原始项值。
     * <p>
     * 该函数用于检查传入的对象是否为 `QueryCondition.Item` 类型的实例，并且该实例的关键字是否为 `Keyword.RAW`。
     *
     * @param value 要检查的对象，可以是任意类型。
     * @return 如果 `value` 是 `QueryCondition.Item` 类型且其关键字为 `Keyword.RAW`，则返回 `true`；否则返回 `false`。
     */
    public static boolean isRawItemValue(Object value) {
        // 检查 value 是否为 QueryCondition.Item 类型的实例
        if (value instanceof QueryItem item) {
            // 判断 item 的关键字是否为 RAW
            return item.asKeyWord() == Keyword.RAW;
        }
        return false;
    }

    /**
     * 展开查询条件
     *
     * @param val
     * @param consumer
     */
    public static void flatItemValue(Object val, Consumer<QueryItem> consumer) {
        if (ObjectUtils.isEmpty(val))
            return;
        if (val instanceof Collection<?> items) {
            for (Object obj : items) {
                if (obj instanceof QueryItem)
                    consumer.accept(((QueryItem) obj));
            }
        } else {
            if (val instanceof QueryItem) {
                consumer.accept(((QueryItem) val));
            }
        }
    }

}
