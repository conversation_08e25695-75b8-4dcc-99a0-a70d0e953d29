/*
 * Copyright (c) 2011-2022, baomi<PERSON>u (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.sql.enums;


import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;

/**
 * SQL 保留关键字枚举
 *
 * <AUTHOR>
 * @since 2018-05-28
 */
public enum SqlKeyword implements ISqlPart {
    WHERE(SqlConstants.WHERE),
    SELECT(SqlConstants.SELECT),
    FROM(SqlConstants.FROM),
    DISTINCT(SqlConstants.DISTINCT),
    UPDATE(SqlConstants.UPDATE),
    DELETE(SqlConstants.DELETE),
    INSERT(SqlConstants.INSERT_INTO),
    SET(SqlConstants.SET),

    AND(SqlConstants.AND),
    OR(SqlConstants.OR),
    NOT("NOT"),
    IN(SqlConstants.IN),
    NOT_IN("NOT IN"),
    LIKE(SqlConstants.LIKE),
    NOT_LIKE("NOT LIKE"),
    EQ(SqlConstants.EQUAL),
    NE("<>"),
    GT(SqlConstants.GT),
    GE(SqlConstants.GTE),
    LT(SqlConstants.LT),
    LE(SqlConstants.LTE),
    IS_NULL("IS NULL"),
    IS_NOT_NULL("IS NOT NULL"),
    GROUP_BY("GROUP BY"),
    HAVING("HAVING"),
    ORDER_BY("ORDER BY"),
    EXISTS("EXISTS"),
    NOT_EXISTS("NOT EXISTS"),
    BETWEEN("BETWEEN"),
    NOT_BETWEEN("NOT BETWEEN"),
    ASC("ASC"),
    DESC("DESC"),

    FIND_IN_SET("FIND_IN_SET"),

    /**
     * 正则匹配
     */
    REGEXP("REGEXP"),
    NOT_REGEXP("NOT REGEXP"),

    ;

    private final String keyword;

    SqlKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getKeyword() {
        return this.keyword;
    }

    @Override
    public String getPart() {
        return SqlDialectHelper.keywordAsSql(this);
    }

    public static boolean isSymbol(String symbol) {
        return symbol.startsWith("__");
    }

    public static SqlKeyword from(String symbol) {
        if (isSymbol(symbol)) {
            return valueOf(symbol.substring(2));
        }
        return null;
    }

    /**
     * @return 转为系统字符
     */
    public String asSymbol() {
        return "__" + this.name();
    }
}
