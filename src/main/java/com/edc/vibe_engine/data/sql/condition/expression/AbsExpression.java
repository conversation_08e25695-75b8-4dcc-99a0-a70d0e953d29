package com.edc.vibe_engine.data.sql.condition.expression;

import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;

import java.util.ArrayList;
import java.util.List;

public abstract class AbsExpression<T> implements ISqlPart {

    private final List<T> parts = new ArrayList<>();

    private String cachePart;

    public boolean isEmpty() {
        return parts.isEmpty();
    }

    protected void addPart(T part) {
        parts.add(part);
        cachePart = null;
    }

    protected void reset() {
        parts.clear();
        cachePart = null;
    }


    @Override
    public String getPart() {
        if (cachePart == null) {
            cachePart = this.isEmpty() ? "" : this.rebuildPart(this.parts);
        }
        return cachePart;
    }

    abstract String rebuildPart(List<T> parts);
}
