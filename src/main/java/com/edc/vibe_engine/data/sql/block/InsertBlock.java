package com.edc.vibe_engine.data.sql.block;


import com.edc.vibe_engine.common.support.AssertUtils;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

public class InsertBlock extends AbsSqlBlock<InsertBlock> {
    @Serial
    private static final long serialVersionUID = 3134711477655854432L;
    /**
     * SQL 更新字段内容
     */
    private final List<String> insertColumns = new ArrayList<>();
    private final List<String> insertValues = new ArrayList<>();

    @Override
    public InsertBlock set(boolean condition, String column, Object val) {
        return maybeDo(condition, () -> {
            final String useCol = SqlDialectHelper.escape(column);
            String sql = formatParam(useCol, val, false);
            insertColumns.add(useCol);
            insertValues.add(sql);
        });
    }

    @Override
    public InsertBlock setOnInsert(boolean condition, String column, Object val) {
        return set(condition, column, val);
    }

    @Override
    public boolean isEmpty() {
        return insertColumns.isEmpty();
    }

    @Override
    public String getPart() {
        AssertUtils.assertFalse(CollectionUtils.isEmpty(insertColumns), "sqlInsert is empty");
        // (f1,f2,f3)
        return SqlConstants.LEFT_PARENTHESIS + String.join(SqlConstants.COMMA, insertColumns) + SqlConstants.RIGHT_PARENTHESIS +
               // values(?,?,?)
               SqlConstants.VALUES + SqlConstants.LEFT_PARENTHESIS + String.join(SqlConstants.COMMA, insertValues) + SqlConstants.RIGHT_PARENTHESIS;
    }
}
