
package com.edc.vibe_engine.data.sql.condition.expression;

import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class OrderExpression extends AbsExpression<Tuple2<String, Boolean>> {


    public OrderExpression push(ISqlPart val, boolean isAsc) {
        return this.push(val.getPart(), isAsc);
    }

    public OrderExpression push(String val, boolean isAsc) {
        this.addPart(Tuple.of(val, isAsc));
        return this;
    }

    @Override
    String rebuildPart(List<Tuple2<String, Boolean>> parts) {
        return parts.stream()
                .map(pair -> {
                    if (pair._2) {
                        return pair._1 + " ASC";
                    } else {
                        return pair._1 + " DESC";
                    }
                })
                .filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
    }
}
