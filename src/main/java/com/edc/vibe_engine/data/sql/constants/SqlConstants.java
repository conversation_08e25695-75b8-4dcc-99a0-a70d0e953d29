package com.edc.vibe_engine.data.sql.constants;


public class SqlConstants {


    //查询相关
    public static final String SELECT = "SELECT";
    public static final String UNION = " union ";
    public static final String AS = " as ";
    public static final String DISTINCT = " DISTINCT ";
    public static final String WHERE = " WHERE ";
    public static final String GROUP_BY = " GROUP BY ";
    public static final String HAVING = " HAVING ";
    public static final String ORDER_BY = " ORDER BY ";
    public static final String FROM = "FROM";
    public static final String LEFT_JOIN = " LEFT JOIN ";
    public static final String INNER_JOIN = " INNER JOIN ";
    public static final String JOIN_ON = " ON ";


    public static final String INSERT_INTO = "INSERT INTO";
    public static final String DUPLICATE_UPDATE = "ON DUPLICATE KEY UPDATE";
    public static final String UPDATE = "UPDATE";
    public static final String SET = "SET";
    public static final String AND = " AND ";
    public static final String OR = " OR ";

    public static final String ON = " ON ";
    public static final String VALUES = " VALUES ";
    public static final String DELETE = "DELETE";

    public static final String LEFT_PARENTHESIS = "(";
    public static final String RIGHT_PARENTHESIS = ")";
    public static final String ACCENT = "`";
    public static final String EQUAL = " = ";
    public static final String LT = " < ";
    public static final String GT = " > ";
    public static final String GTE = " >= ";
    public static final String LTE = " <= ";

    public static final String LIKE = " LIKE ";
    public static final String ILIKE = " ILIKE ";

    public static final String NOT = " NOT ";
    public static final String NOT_LIKE = " NOT LIKE ";
    public static final String NOT_ILIKE = " NOT ILIKE ";

    public static final String IN = " in ";
    public static final String ASK = "?";
    public static final String EQUAL_ASK = "=?";
    public static final String COMMA = ",";
    public static final String ASTERISK = "*";
    public static final String BLANK = " ";

    public static final String DOLLAR_TAG = "$";
    public static final String SINGLE_QUOTA = "'";
    public static final String POINT = ".";
    public static final String VALUE_TAG = "\"";

    public static final String PERCENT = "%";


    public static final String EMPTY = "";
}
