package com.edc.vibe_engine.data.sql.condition;

import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.data.interfaces.ICompare;
import com.edc.vibe_engine.data.interfaces.IFunc;
import com.edc.vibe_engine.data.interfaces.IJoin;
import com.edc.vibe_engine.data.interfaces.IJson;
import com.edc.vibe_engine.data.interfaces.INested;
import com.edc.vibe_engine.data.sql.condition.expression.GroupExpression;
import com.edc.vibe_engine.data.sql.condition.expression.HavingExpression;
import com.edc.vibe_engine.data.sql.condition.expression.OrderExpression;
import com.edc.vibe_engine.data.sql.condition.expression.WhereExpression;
import com.edc.vibe_engine.data.sql.constants.SqlConstants;
import com.edc.vibe_engine.data.sql.enums.SqlKeyword;
import com.edc.vibe_engine.data.sql.enums.SqlLike;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import com.edc.vibe_engine.data.sql.support.SqlInjectionUtils;
import com.edc.vibe_engine.data.sql.support.SqlParamHelper;
import com.edc.vibe_engine.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.joining;

/**
 * @param <F> 字段类型
 * @param <M> 字段转换为 BizField
 * @param <R> 返回值
 */
@SuppressWarnings("unchecked")
public abstract class AbsCondition<F, M, R extends AbsCondition<F, M, R>> implements INested<R, R>,
        ICompare<R, F>, IJoin<R>, IFunc<R, F>, IJson<R, F>, ISqlPart {

    /**
     * 占位符
     */
    protected final R typedThis = (R) this;

    private final WhereExpression where;
    private final GroupExpression group;
    private final OrderExpression order;
    private final HavingExpression having;

    protected List<Object> params;

    public AbsCondition() {
        this.where = new WhereExpression();
        this.group = new GroupExpression();
        this.order = new OrderExpression();
        this.having = new HavingExpression();
        this.params = new ArrayList<>();
    }

    /**
     * 函数化的做事
     *
     * @param condition 做不做
     * @param something 做什么
     * @return Children
     */
    protected final R maybeDo(boolean condition, Runnable something) {
        if (condition) {
            something.run();
        }
        return typedThis;
    }

    protected abstract M transformColumn(F column);

    protected String transformExpression(String expressionSql) {
        return expressionSql;
    }

    /**
     * 普通查询条件
     *
     * @param condition  是否执行
     * @param column     属性
     * @param sqlKeyword SQL 关键词
     * @param val        条件值
     */
    protected R addCondition(boolean condition, F column, SqlKeyword sqlKeyword, Object val) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doAddCondition(mColumn, sqlKeyword, val);
        });
    }

    protected void doAddCondition(M mColumn, SqlKeyword sqlKeyword, Object val) {
        where.push("%s %s %s".formatted(columnToString(mColumn), sqlKeyword.getPart(), formatParam(castParam(mColumn, val, false))));
    }

    public void addJoinCondition(M mColumn, SqlKeyword sqlKeyword, M tColumn) {
        where.push("%s %s %s".formatted(columnToString(mColumn), sqlKeyword.getPart(), columnToString(tColumn)));

    }


    /**
     * 内部自用
     * <p>拼接 LIKE 以及 值</p>
     */
    protected R likeValue(boolean condition, SqlKeyword keyword, F column, Object val, SqlLike sqlLike) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doLikeValue(keyword, mColumn, val, sqlLike);
        });
    }

    protected void doLikeValue(SqlKeyword keyword, M mColumn, Object val, SqlLike sqlLike) {
        where.push("%s %s %s".formatted(columnToString(mColumn), keyword.getPart(), formatParam(concatLike(val, sqlLike))));
    }

    /**
     * 用%连接like
     *
     * @param str 原字符串
     * @return like 的值
     */
    public static String concatLike(Object str, SqlLike type) {
        return switch (type) {
            case FULL -> String.valueOf(str);
            case LEFT -> SqlConstants.PERCENT + str;
            case RIGHT -> str + SqlConstants.PERCENT;
            default -> SqlConstants.PERCENT + str + SqlConstants.PERCENT;
        };
    }

    /**
     * @param mColumn
     * @param val
     * @param inJson  当前是否强制 json 处理
     * @return
     */
    protected Object castParam(M mColumn, Object val, boolean inJson) {
        return val;
    }

    protected Collection<?> castParams(M mColumn, Collection<?> vals, boolean inJson) {
        return vals.stream().map(v -> castParam(mColumn, v, inJson)).collect(Collectors.toList());
    }

    protected String formatParam(Object val) {
        params.add(SqlParamHelper.casToSqlValue(val, false));
        return "?";
    }

    @Override
    public R eq(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.EQ, val);
    }

    @Override
    public R ne(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.NE, val);
    }

    @Override
    public R gt(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.GT, val);
    }

    @Override
    public R ge(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.GE, val);
    }

    @Override
    public R lt(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.LT, val);
    }

    @Override
    public R le(boolean condition, F column, Object val) {
        return addCondition(condition, column, SqlKeyword.LE, val);
    }

    @Override
    public R between(boolean condition, F column, Object val1, Object val2) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doBetween(mColumn, val1, val2);
        });
    }

    protected void doBetween(M mColumn, Object val1, Object val2) {
        where.push("%s BETWEEN %s and %s".formatted(columnToString(mColumn), formatParam(castParam(mColumn, val1, false)), formatParam(castParam(mColumn, val2, false))));
    }

    @Override
    public R notBetween(boolean condition, F column, Object val1, Object val2) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doNotBetween(mColumn, val1, val2);
        });
    }

    protected void doNotBetween(M mColumn, Object val1, Object val2) {
        where.push("%s NOT BETWEEN %s and %s".formatted(columnToString(mColumn), formatParam(castParam(mColumn, val1, false)), formatParam(castParam(mColumn, val2, false))));
    }

    @Override
    public R like(boolean condition, F column, Object val) {
        return likeValue(condition, SqlKeyword.LIKE, column, val, SqlLike.DEFAULT);
    }

    @Override
    public R notLike(boolean condition, F column, Object val) {
        return likeValue(condition, SqlKeyword.NOT_LIKE, column, val, SqlLike.DEFAULT);
    }

    @Override
    public R likeLeft(boolean condition, F column, Object val) {
        return likeValue(condition, SqlKeyword.LIKE, column, val, SqlLike.LEFT);
    }

    @Override
    public R likeRight(boolean condition, F column, Object val) {
        return likeValue(condition, SqlKeyword.LIKE, column, val, SqlLike.RIGHT);
    }

    @Override
    public R notRegexp(boolean condition, F column, String val) {
        return addCondition(condition, column, SqlKeyword.NOT_REGEXP, val);
    }

    @Override
    public R regexp(boolean condition, F column, String val) {
        return addCondition(condition, column, SqlKeyword.REGEXP, val);
    }

    @Override
    public R jsonContains(boolean condition, F column, Object val, String path) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doJsonContains(mColumn, val, path, true);
        });
    }

    @Override
    public R jsonArrayIn(boolean condition, F column, Collection<?> vals, String path) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doJsonArrayIn(mColumn, vals, path);
        });
    }

    @Override
    public R jsonArrayNotIn(boolean condition, F column, Collection<?> vals, String path) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doJsonArrayNotIn(mColumn, vals, path);
        });
    }

    protected void doJsonArrayIn(M mColumn, Collection<?> vals) {
        doJsonArrayIn(mColumn, vals, null);
    }

    protected void doJsonArrayIn(M mColumn, Collection<?> vals, String path) {
        final WhereExpression exp = new WhereExpression();
        for (Object o : vals) {
            exp.or(sub -> doJsonContains(sub, mColumn, o, path, true));
        }
        where.push(exp);
    }

    protected void doJsonArrayNotIn(M mColumn, Collection<?> vals) {
        doJsonArrayNotIn(mColumn, vals, null);
    }

    protected void doJsonArrayNotIn(M mColumn, Collection<?> vals, String path) {
        final WhereExpression exp = new WhereExpression();
        for (Object o : vals) {
            exp.and(sub -> doJsonContains(sub, mColumn, o, path, false));
        }
        where.push(exp);
    }

    protected void doJsonContains(M mColumn, Object val, String path, boolean exist) {
        doJsonContains(this.where, mColumn, val, path, exist);
    }

    protected void doJsonContains(WhereExpression exp, M mColumn, Object val, String path, boolean exist) {
        final Object param = castParam(mColumn, val, true);
        final String column = columnToString(false, mColumn);
        final String value = formatParam(JSON.toJSON(param));
        exp.push(SqlDialectHelper.jsonContains(column, value, path, exist));
    }

    protected void doJsonEq(M mColumn, Object val, String path, boolean isEq) {
        final Object param = castParam(mColumn, val, true);
        final String column = columnToString(false, mColumn);
        final String value = formatParam(JSON.toJSON(param));
        where.push(SqlDialectHelper.jsonEqual(column, value, path, isEq));
    }

    protected void doJsonIsNull(M mColumn) {
        where.push(SqlDialectHelper.jsonIsNull(columnToString(false, mColumn)));
    }

    protected void doJsonIsNotNull(M mColumn) {
        where.push(SqlDialectHelper.jsonNotNull(columnToString(false, mColumn)));
    }

    /**
     * 子类返回一个自己的新对象
     */
    protected abstract R instance();


    /**
     * 内部自用
     * <p>NOT 关键词</p>
     */
    protected R not(boolean condition) {
        return maybeDo(condition, () -> where.push(SqlKeyword.NOT));
    }

    /**
     * 内部自用
     * <p>拼接 AND</p>
     */
    public R and(boolean condition) {
        return maybeDo(condition, () -> where.push(SqlKeyword.AND));
    }

    /**
     * 格式化 sql
     *
     * @param sqlStr 可能是sql片段
     * @param params 参数
     * @return sql片段
     */
    @SuppressWarnings("SameParameterValue")
    protected final String formatSqlMaybeWithParam(String sqlStr, Object... params) {
        if (StringUtils.isEmpty(sqlStr)) {
            // todo 何时会这样?
            return null;
        }
        if (ObjectUtils.isNotEmpty(params)) {
            for (Object param : params) {
                formatParam(param);
            }
        }
        return sqlStr;
    }

    /**
     * 获取in表达式 包含括号
     *
     * @param value 集合
     */
    protected String inExpression(Collection<?> value) {
        if (CollectionUtils.isEmpty(value)) {
            return "()";
        }
        return value.stream().map(this::formatParam)
                .collect(joining(SqlConstants.COMMA, SqlConstants.LEFT_PARENTHESIS, SqlConstants.RIGHT_PARENTHESIS));
    }

    @Override
    public R and(boolean condition, Consumer<R> consumer) {
        return maybeDo(condition, () -> {
            final R instance = instance();
            consumer.accept(instance);
            mergeAnd(instance);
        });
    }

    @Override
    public R and(boolean condition, R other) {
        return maybeDo(condition, () -> mergeAnd(other));
    }

    private void mergeAnd(R other) {
        where.and(sub -> sub.push(other.getWhere()));
        params.addAll(other.getParams());
    }

    @Override
    public R or(boolean condition, Consumer<R> consumer) {
        return maybeDo(condition, () -> {
            final R instance = instance();
            consumer.accept(instance);
            mergeOr(instance);
        });
    }

    @Override
    public R or(boolean condition, R other) {
        return maybeDo(condition, () -> mergeOr(other));
    }

    private void mergeOr(R other) {
        where.or(sub -> sub.push(other.getWhere()));
        params.addAll(other.getParams());
    }

    @Override
    public R not(boolean condition, Consumer<R> consumer) {
        return maybeDo(condition, () -> {
            final R instance = instance();
            consumer.accept(instance);
            where.not(sub -> sub.push(instance.getWhere()));
        });
    }

    public List<Object> getParams() {
        return params;
    }

    @Override
    public R or(boolean condition) {
        return maybeDo(condition, () -> where.or());
    }

    @Override
    public R apply(boolean condition, String applySql, Object... values) {
        return maybeDo(condition, () -> {
            where.push(formatSqlMaybeWithParam(applySql, values));
        });
    }

    //    @Override
    //    public R last(boolean condition, boolean replaceExist, String lastSql) {
    //        if (condition) {
    //            if (this.lastSql == null || replaceExist) {
    //                this.lastSql = () -> SqlConstants.BLANK + lastSql;
    //            }
    //        }
    //        return typedThis;
    //    }

    @Override
    public R exists(boolean condition, String existsSql, Object... values) {
        return maybeDo(condition, () -> {
            where.push("EXISTS (%s)".formatted(formatSqlMaybeWithParam(existsSql, values)));
        });
    }

    @Override
    public R notExists(boolean condition, String existsSql, Object... values) {
        return maybeDo(condition, () -> {
            where.push("NOT EXISTS (%s)".formatted(formatSqlMaybeWithParam(existsSql, values)));
        });
    }


    @Override
    public R isNull(boolean condition, F column) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doIsNull(mColumn);
        });
    }

    protected void doIsNull(M mColumn) {
        where.push("%s IS NULL".formatted(columnToString(mColumn)));
    }

    @Override
    public R isNotNull(boolean condition, F column) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doIsNotNull(mColumn);
        });
    }

    protected void doIsNotNull(M mColumn) {
        where.push("%s IS NOT NULL".formatted(columnToString(mColumn)));
    }

    @Override
    public R in(boolean condition, F column, Collection<?> coll) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doIn(mColumn, coll);
        });
    }

    protected void doIn(M mColumn, Collection<?> coll) {
        where.push("%s IN %s".formatted(columnToString(mColumn), inExpression(castParams(mColumn, coll, false))));
    }

    protected void doNotIn(M mColumn, Collection<?> coll) {
        where.push("%s NOT IN %s".formatted(columnToString(mColumn), inExpression(castParams(mColumn, coll, false))));
    }

    @Override
    public R in(boolean condition, F column, Object... values) {
        return in(condition, column, TypeCastUtils.castToList(values));
    }

    @Override
    public R includesAny(boolean condition, F column, Object... values) {
        return jsonArrayIn(condition, column, TypeCastUtils.castToList(values));
    }

    @Override
    public R includesAny(boolean condition, F column, Collection<?> coll) {
        return jsonArrayIn(condition, column, coll);
    }

    @Override
    public R notIn(boolean condition, F column, Collection<?> coll) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doNotIn(mColumn, coll);
        });
    }

    @Override
    public R notIn(boolean condition, F column, Object... values) {
        return notIn(condition, column, TypeCastUtils.castToList(values));
    }

    @Override
    public R inSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doInSql(mColumn, inValue);
        });
    }

    protected void doInSql(M mColumn, String inValue) {
        where.push("%s IN (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R gtSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doGtSql(mColumn, inValue);
        });
    }

    protected void doGtSql(M mColumn, String inValue) {
        where.push("%s > (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R geSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doGeSql(mColumn, inValue);
        });
    }

    protected void doGeSql(M mColumn, String inValue) {
        where.push("%s >= (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R ltSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doLtSql(mColumn, inValue);
        });
    }

    protected void doLtSql(M mColumn, String inValue) {
        where.push("%s < (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R leSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doLeSql(mColumn, inValue);
        });
    }

    protected void doLeSql(M mColumn, String inValue) {
        where.push("%s <= (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R notInSql(boolean condition, F column, String inValue) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doNotInSql(mColumn, inValue);
        });
    }

    protected void doNotInSql(M mColumn, String inValue) {
        where.push("%s NOT IN (%s)".formatted(columnToString(mColumn), inValue));
    }

    @Override
    public R groupBy(boolean condition, F column) {
        return maybeDo(condition, () -> applyGroupBy(column));
    }

    public static boolean maybeExpressionSql(CharSequence cs) {
        if (StringUtils.isEmpty(cs)) {
            return false;
        }
        final int sz = cs.length();
        for (int i = 0; i < sz; i++) {
            final char ch = cs.charAt(i);
            if (ch == '(' || ch == ')' || ch == ' ' || ch == ',') {
                return true;
            }
        }
        return false;
    }

    private void applyGroupBy(F column) {
        if (column instanceof String && maybeExpressionSql(((String) column))) {
            final String[] splits = StringUtils.split(((String) column), SqlConstants.COMMA);
            for (String s : splits) {
                group.push(transformExpression(s));
            }
        } else {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doGroupBy(mColumn);
        }
    }

    protected void doGroupBy(M mColumn) {
        group.push(columnToString(mColumn));
    }

    @Override
    public R groupBy(boolean condition, List<F> columns) {
        return maybeDo(condition, () -> {
            for (F column : columns) {
                // 中间字段类型
                applyGroupBy(column);
            }
        });
    }

    @Override
    public R groupBy(boolean condition, F column, F... columns) {
        return maybeDo(condition, () -> {
            applyGroupBy(column);
            if (ObjectUtils.isNotEmpty(columns)) {
                for (F f : columns) {
                    applyGroupBy(f);
                }

            }
        });
    }


    @Override
    public R orderBy(boolean condition, boolean isAsc, F column) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doOrderBy(mColumn, isAsc);
        });
    }

    protected void doOrderBy(M mColumn, boolean isAsc) {
        final String colStr = SqlInjectionUtils.replaceInjection(columnToString(false, mColumn));
        order.push(colStr, isAsc);
    }

    @Override
    public R orderBy(boolean condition, boolean isAsc, List<F> columns) {
        return maybeDo(condition, () -> {
            for (F column : columns) {
                // 中间字段类型
                final M mColumn = transformColumn(column);
                doOrderBy(mColumn, isAsc);
            }
        });
    }

    @Override
    public R orderBy(boolean condition, boolean isAsc, F column, F... columns) {
        return maybeDo(condition, () -> {
            // 中间字段类型
            final M mColumn = transformColumn(column);
            doOrderBy(mColumn, isAsc);

            if (ObjectUtils.isNotEmpty(columns)) {
                for (F f : columns) {
                    final M mf = transformColumn(f);
                    doOrderBy(mf, isAsc);
                }
            }
        });
    }

    @Override
    public R orderByExp(boolean condition, boolean isAsc, String orderExp) {
        return maybeDo(condition, () -> order.push(formatSqlMaybeWithParam(transformExpression(orderExp)), isAsc));
    }

    @Override
    public R having(boolean condition, String sqlHaving, Object... params) {
        return maybeDo(condition, () -> {
            having.push(formatSqlMaybeWithParam(transformExpression(sqlHaving), params));
        });
    }

    @Override
    public R func(boolean condition, Consumer<R> consumer) {
        return maybeDo(condition, () -> consumer.accept(typedThis));
    }

    protected String columnToString(M mColumn) {
        return columnToString(false, mColumn);
    }

    /**
     * 获取 columnName
     */
    protected abstract String columnToString(boolean appAlias, M mColumn);

    public WhereExpression getWhere() {
        return where;
    }

    public GroupExpression getGroup() {
        return group;
    }

    public OrderExpression getOrder() {
        return order;
    }

    public HavingExpression getHaving() {
        return having;
    }

    @Override
    public String getPart() {
        StringBuilder builder = new StringBuilder();

        final WhereExpression where = getWhere();
        if (where != null && !where.isEmpty()) {
            builder.append(SqlConstants.WHERE);
            builder.append(where.getPart());
        }
        final GroupExpression group = getGroup();
        if (group != null && !group.isEmpty()) {
            builder.append(SqlConstants.GROUP_BY);
            builder.append(group.getPart());
        }
        final HavingExpression having = getHaving();
        if (having != null && !having.isEmpty()) {
            builder.append(SqlConstants.HAVING);
            builder.append(having.getPart());
        }
        final OrderExpression order = getOrder();
        if (order != null && !order.isEmpty()) {
            builder.append(SqlConstants.ORDER_BY);
            builder.append(order.getPart());
        }

        return builder.toString();
    }

    //    public ISqlPart getLastSql() {
    //        return lastSql;
    //    }
}
