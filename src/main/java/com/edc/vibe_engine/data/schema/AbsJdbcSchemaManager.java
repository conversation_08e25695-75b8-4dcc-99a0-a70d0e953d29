package com.edc.vibe_engine.data.schema;

import com.edc.vibe_engine.common.support.ObjectExUtils;
import com.edc.vibe_engine.data.exception.DatabaseMigrationException;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import com.edc.vibe_engine.data.schema.types.DBField;
import com.edc.vibe_engine.data.schema.types.DBIndex;
import com.edc.vibe_engine.data.schema.types.DBTable;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import com.edc.vibe_engine.json.JSON;
import io.vavr.CheckedFunction1;
import io.vavr.CheckedFunction3;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public abstract class AbsJdbcSchemaManager implements JdbcSchemaManager {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Supplier<Connection> connectionSupplier;

    public AbsJdbcSchemaManager(Supplier<Connection> connectionSupplier) {
        this.connectionSupplier = connectionSupplier;
    }

    public <T> T fetch(CheckedFunction1<Connection, T> func) throws Throwable {
        //        try (Connection connection = DriverManager.getConnection(jdbcConfig.buildUrl(), jdbcConfig.getUsername(), jdbcConfig.getPassword())) {
        //            return func.apply(connection);
        //        }
        try (Connection connection = connectionSupplier.get()) {
            return func.apply(connection);
        }
    }


    protected <T> T withMetaData(CheckedFunction3<String, String, DatabaseMetaData, T> consumer) {
        try {
            return fetch(connection -> {
                final String schema = connection.getSchema();
                final String catalog = connection.getCatalog();
                final DatabaseMetaData metaData = connection.getMetaData();
                return consumer.apply(catalog, schema, metaData);
            });
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    protected <T> T query(CheckedFunction1<Statement, T> consumer) {
        try {
            return fetch(connection -> {
                try (Statement statement = connection.createStatement()) {
                    return consumer.apply(statement);
                }
            });
        } catch (Throwable e) {
            logger.error("execute Statement error:", e);
            throw new DatabaseMigrationException("Execute Statement Error.", e);
        }
    }


    public void executeSql(String ddl) {
        try {
            fetch(connection -> {
                try (PreparedStatement statement = connection.prepareStatement(ddl)) {
                    statement.execute();
                }
                return null;
            });
        } catch (Throwable e) {
            logger.error("DatabaseTableManager execute statement error, ddl:{} :", ddl, e);
            throw new DatabaseMigrationException("Execute Statement Error.", e);
        }
    }

    public String getAltTableName(DBTable current, DBTable prev) {
        return prev == null ? current.getName() : prev.getName();
    }

    @Override
    public void upgrade(DBTable current, DBTable prev, Consumer<String> ddlConsumer) {
        Consumer<String> innerConsumer = ddl -> {
            if (StringUtils.isNotEmpty(ddl)) {
                ddlConsumer.accept(ddl);
            }
        };
        final DBTable mapTable = mapTable(current);

        final String altTableName = getAltTableName(mapTable, prev);
        boolean tableExists = exists(altTableName);
        if (tableExists) {
            innerConsumer.accept(modifyTable(mapTable, prev));
        } else {
            innerConsumer.accept(createTable(mapTable));
        }
    }

    @Override
    public List<String> tableList() {
        return withMetaData((currentCatalog, currentSchema, metaData) -> {
            final ResultSet tables = metaData.getTables(currentCatalog, currentSchema, "%", new String[]{"TABLE"});

            List<String> result = new ArrayList<>();
            while (tables.next()) {
                result.add(tables.getString("TABLE_NAME"));
            }
            return result;
        });
    }

    protected DBTable mapTable(DBTable current) {
        return current;
    }

    public String createTable(DBTable table) {
        fixTable(table);
        String sql = buildCreateTableSql(table);
        try {
            executeSql(sql);
        } catch (DatabaseMigrationException e) {
            logger.error("createTable error, sql:{} ", sql, e);
            throw new DatabaseMigrationException("createTable error.", e);
        }
        return sql;
    }


    public String modifyTable(DBTable current, DBTable prev) {
        fixTable(current);
        final String altTableName = getAltTableName(current, prev);

        final Map<String, DBField> oldNameFieldMap = Optional.ofNullable(prev)
                .map(oldTable -> oldTable.getFieldList().stream().collect(Collectors.toMap(DBField::getName, Function.identity())))
                .orElseGet(HashMap::new);
        DBTable schema = schema(altTableName);

        schema.getFieldList()
                .forEach(dbField -> {
                    final DBField field = oldNameFieldMap.get(dbField.getName());
                    if (field != null) {
                        dbField.setCode(field.getCode());
                    }
                });
        try {
            final String modifyTableSql = buildModifyTableSql(current, schema);
            if (StringUtils.isNotEmpty(modifyTableSql)) {
                executeSql(modifyTableSql);
            }
            return modifyTableSql;
        } catch (Exception e) {
            logger.error("modifyTable error,currentSchema:{},existSchema:{}", JSON.toJSON(current), JSON.toJSON(schema));
            throw e;
        }

    }

    private void fixTable(DBTable table) {
        final List<DBField> fieldList = table.getFieldList().stream()
                .map(f -> {
                    final DBField dbField = this.fixField(table, f);
                    if (dbField.getType() == DBFieldType.TIMESTAMP && dbField.getSize() == 0) {
                        dbField.setSize(6);
                    }
                    return dbField;
                }).collect(Collectors.toList());
        final List<DBIndex> indexList = table.getIndexList().stream()
                .map(idx -> this.fixIndex(table.getName(), idx))
                .collect(Collectors.toList());
        table.setFieldList(fieldList);
        table.setIndexList(indexList);
        if (table.getComment() == null) {
            table.setComment("");
        }
    }

    @Override
    public boolean exists(String tableName) {
        return withMetaData((currentCatalog, currentSchema, metaData) -> {
            final ResultSet tables = metaData.getTables(currentCatalog, currentSchema, tableName, new String[]{"TABLE"});
            return tables.next();
        });
    }

    @Override
    public void drop(String tableName, Consumer<String> ddlConsumer) {
        boolean tableExists = exists(tableName);
        if (tableExists) {
            String sql = buildDropTableSql(tableName);
            try {
                ddlConsumer.accept(sql);
                executeSql(sql);
            } catch (DatabaseMigrationException e) {
                logger.error("dropTable error, sql:{} ", sql, e);
                throw new DatabaseMigrationException("createTable error.", e);
            }

        }
    }

    abstract String buildCreateTableSql(DBTable table);

    abstract String buildModifyTableSql(DBTable current, DBTable prev);


    protected String buildDropTableSql(String tableName) {
        return String.format("drop table if exists %s;", escapeSqlName(tableName));
    }

    @Override
    public DBTable schema(String tableName) {
        return withMetaData((currentCatalog, currentSchema, metaData) -> {
            final ResultSet tables = metaData.getTables(currentCatalog, currentSchema, tableName, new String[]{"TABLE"});
            if (tables.next()) {
                DBTable dbTable = new DBTable();
                final String name = tables.getString("TABLE_NAME");
                final String comment = tables.getString("REMARKS");
                //                final String catalog = tables.getString("TABLE_CAT");

                dbTable.setName(name);
                dbTable.setComment(comment == null ? "" : comment);

                StringJoiner pkNames = new StringJoiner(",");
                final ResultSet primaryKeys = metaData.getPrimaryKeys(currentCatalog, currentSchema, tableName);
                while (primaryKeys.next()) {
                    pkNames.add(primaryKeys.getString("COLUMN_NAME"));
                }

                dbTable.setPrimaryKey(pkNames.toString());

                List<DBField> fieldList = new ArrayList<>();
                final ResultSet columns = metaData.getColumns(currentCatalog, currentSchema, tableName, "%");
                //                toMapList(columns);

                while (columns.next()) {
                    DBField field = new DBField();
                    field.setName(columns.getString("COLUMN_NAME"));
                    field.setComment(columns.getString("REMARKS"));
                    final String typeName = columns.getString("TYPE_NAME");
                    final DBFieldType type = DBFieldType.fromJdbcType(columns.getInt("DATA_TYPE"), typeName);
                    field.setType(type);

                    //                    final boolean isGenerated = columns.getBoolean("IS_GENERATEDCOLUMN");
                    //                    field.setGenerated(isGenerated);
                    final boolean isAutoincrement = columns.getBoolean("IS_AUTOINCREMENT");
                    field.setAutoIncrement(isAutoincrement);
                    final int digits = columns.getInt("DECIMAL_DIGITS");
                    final int columnSize = columns.getInt("COLUMN_SIZE");
                    if (type == DBFieldType.TIMESTAMP) {
                        if (sqlDialect() == SqlDialect.MYSQL) {
                            field.setSize(Math.max(columnSize - 20, 0));
                        } else {
                            field.setSize(digits);
                        }
                    } else if (type == DBFieldType.TIME) {
                        field.setSize(digits);
                    } else {
                        if (type.isHasSize()) {
                            field.setSize(columnSize);
                        }
                        if (type.isHasDigit()) {
                            field.setDigits(digits);
                        }
                    }
                    field.setAllowNull(columns.getBoolean("IS_NULLABLE"));
                    if (!isAutoincrement) {
                        field.setDefaultValue(columns.getString("COLUMN_DEF"));
                    }
                    fieldList.add(fixField(dbTable, field));
                }
                dbTable.setFieldList(fieldList);


                Map<String, DBIndex> indexMap = new LinkedHashMap<>();
                final ResultSet indexInfo = metaData.getIndexInfo(currentCatalog, currentSchema, tableName, false, true);

                while (indexInfo.next()) {
                    if (indexInfo.getShort("TYPE") == DatabaseMetaData.tableIndexStatistic) {
                        continue;
                    }
                    final String indexName = indexInfo.getString("INDEX_NAME");
                    // 过滤主键索引
                    if (indexName.equals("PRIMARY") || indexName.equals(tableName + "_pk")) {
                        continue;
                    }

                    final boolean isUnique = !indexInfo.getBoolean("NON_UNIQUE");
                    final String columnName = indexInfo.getString("COLUMN_NAME");
                    final DBIndex dbIndex = indexMap.computeIfAbsent(indexName, k -> {
                        final DBIndex index = new DBIndex();
                        index.setName(k);
                        index.setUnique(isUnique);
                        index.setFields(new ArrayList<>());
                        return index;
                    });

                    dbIndex.getFields().add(columnName);
                }
                dbTable.setIndexList(indexMap.values().stream().map(dbIndex -> fixIndex(name, dbIndex)).collect(Collectors.toList()));

                return dbTable;
            }

            return null;
        });
    }

    public abstract SqlDialect sqlDialect();

    protected DBField fixField(DBTable table, DBField field) {
        //        if (field.getType() == DBFieldType.DATETIME) {
        //            field.setType(DBFieldType.TIMESTAMP);
        //        } else if (field.getType() == DBFieldType.LONGVARCHAR) {
        //            field.setType(DBFieldType.TEXT);
        //        } else if (field.getType() == DBFieldType.TINYINT) {
        //            field.setType(DBFieldType.BOOLEAN);
        //        }
        final String[] split = StringUtils.split(",", table.getPrimaryKey());
        if (Arrays.asList(split).contains(field.getName())) {
            field.setAllowNull(false);
            field.setDefaultValue(null);
        } else if (field.getDefaultValue() == null && !field.isAllowNull()) {
            // 没有默认值的话，就不能设置不为空
            field.setAllowNull(true);
        }
        if (field.getType() == DBFieldType.BOOLEAN && StringUtils.isNotEmpty(field.getDefaultValue())) {
            field.setDefaultValue(String.valueOf(ObjectExUtils.isTrue(field.getDefaultValue())));
        }

        if (field.getComment() == null) {
            field.setComment("");
        }
        if (field.getType() == DBFieldType.VARCHAR && field.getSize() == 0) {
            field.setSize(400);
        }
        return field;
    }

    protected DBIndex fixIndex(String tableName, DBIndex index) {
        if (index.getName().length() > 63) {
            index.setName(index.getName().substring(0, 63));
        }
        return index;
    }

    protected String escapeSqlName(String name) {
        return SqlDialectHelper.escape(sqlDialect(), name);
    }
}
