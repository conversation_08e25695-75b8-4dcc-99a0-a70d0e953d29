package com.edc.vibe_engine.data.schema;

import com.edc.vibe_engine.data.schema.types.DBTable;

import java.util.List;
import java.util.function.Consumer;

public interface JdbcSchemaManager {
    void upgrade(DBTable current, DBTable prev, Consumer<String> ddlConsumer);

    List<String> tableList();

    boolean exists(String tableName);

    void drop(String tableName, Consumer<String> ddlConsumer);

    DBTable schema(String tableName);
}
