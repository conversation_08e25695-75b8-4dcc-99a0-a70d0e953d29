package com.edc.vibe_engine.data.schema.types;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Singular;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Setter
@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DBTable implements Serializable {
    @Serial
    private static final long serialVersionUID = -1330678697890439672L;
    private String code;
    private String name;
    private String primaryKey;

    @Builder.Default
    private String comment = "";
    /**
     * 字段
     */
    @Singular("field")
    private List<DBField> fieldList;

    /**
     * 索引
     */
    @Singular("index")
    private List<DBIndex> indexList;

}
