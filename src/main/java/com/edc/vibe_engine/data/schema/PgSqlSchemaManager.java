package com.edc.vibe_engine.data.schema;

import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.support.StringExUtils;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import com.edc.vibe_engine.data.schema.types.DBField;
import com.edc.vibe_engine.data.schema.types.DBIndex;
import com.edc.vibe_engine.data.schema.types.DBTable;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class PgSqlSchemaManager extends AbsJdbcSchemaManager {

    public PgSqlSchemaManager(Supplier<Connection> connectionSupplier) {
        super(connectionSupplier);
    }


    @Override
    public String buildCreateTableSql(DBTable table) {
        StringBuilder sb = new StringBuilder(240)
                .append("CREATE TABLE ")
                .append(escapeSqlName(table.getName()))
                .append('(');

        final List<DBField> fieldList = table.getFieldList();
        for (int i = 0; i < fieldList.size(); i++) {
            final DBField dbField = fieldList.get(i);
            appendAddFieldSchema(sb, dbField);
            if (i < fieldList.size() - 1) {
                sb.append(',');
            }
        }
        if (StringUtils.isNotEmpty(table.getPrimaryKey())) {
            final String pkCols = Arrays.stream(StringUtils.split(table.getPrimaryKey(), StringConstants.COMMA))
                    .map(this::escapeSqlName).collect(Collectors.joining(StringConstants.COMMA));
            sb.append(String.format(",constraint %s primary key (%s)", getTablePkName(table.getName()), pkCols));
        }
        sb.append(");");

        appendTableComment(sb, table, false);

        for (DBField dbField : fieldList) {
            appendFieldComment(sb, dbField, table.getName(), false);
        }

        if (CollectionUtils.isNotEmpty(table.getIndexList())) {
            for (DBIndex dbIndex : table.getIndexList()) {
                if (Objects.equals(dbIndex.getName(), "pk")) {
                    continue;
                }
                appendCreateIndex(sb, table.getName(), dbIndex);
            }
        }
        return sb.toString();
    }

    private void appendCreateIndex(StringBuilder sb, String table, DBIndex dbIndex) {
        final String indexField = dbIndex.getFields().stream().map(this::escapeSqlName).collect(Collectors.joining(StringConstants.COMMA));
        sb.append(" CREATE ");
        if (dbIndex.isUnique()) {
            sb.append(" UNIQUE ");
        }
        sb.append(String.format(" INDEX %s ON %s (%s); ", escapeSqlName(fixIndexName(table, dbIndex.getName())), escapeSqlName(table), indexField))
                .append(StringConstants.LINE_SEPARATOR);
    }

    private String fixIndexName(String table, String indexName) {
        return indexName
                .replaceFirst(table + "_", table + "__");
    }

    private void appendDropIndex(StringBuilder sb, String table, DBIndex dbIndex) {
        sb.append(String.format("drop index if exists %s;", escapeSqlName(dbIndex.getName())))
                .append(StringConstants.LINE_SEPARATOR);
        sb.append(String.format("drop index if exists %s;", escapeSqlName(fixIndexName(table, dbIndex.getName()))))
                .append(StringConstants.LINE_SEPARATOR);
    }

    private void appendFieldComment(StringBuilder sb, DBField dbField, String table, boolean isChange) {
        if (StringUtils.isEmpty(dbField.getComment())) {
            if (isChange) {
                sb.append(String.format("comment on column %s.%s is null;", escapeSqlName(table), escapeSqlName(dbField.getName())))
                        .append(StringConstants.LINE_SEPARATOR);
            }
        } else {
            sb.append(String.format("comment on column %s.%s is '%s';", escapeSqlName(table), escapeSqlName(dbField.getName()), dbField.getComment()))
                    .append(StringConstants.LINE_SEPARATOR);
        }
    }

    private void appendTableComment(StringBuilder sb, DBTable dbTable, boolean isChange) {
        if (StringUtils.isEmpty(dbTable.getComment())) {
            if (isChange) {
                sb.append(String.format("comment on table %s is null;", escapeSqlName(dbTable.getName())))
                        .append(StringConstants.LINE_SEPARATOR);
            }
        } else {
            sb.append(String.format(" comment on table %s is '%s';", escapeSqlName(dbTable.getName()), dbTable.getComment()))
                    .append(StringConstants.LINE_SEPARATOR);
        }
    }

    private String getFullTypeName(DBField dbField) {
        final DBFieldType type = dbField.getType();
        if (dbField.isAutoIncrement()) {
            if (type == DBFieldType.INTEGER) {
                return "SERIAL";
            }
            if (type == DBFieldType.BIGINT) {
                return "BIGSERIAL";
            }
        }
        final StringBuilder typeName = new StringBuilder(castTypeName(type));

        if (type.isHasSize() && dbField.getSize() > 0) {
            typeName.append('(').append(dbField.getSize());
            if (type.isHasDigit() && dbField.getDigits() > 0) {
                typeName.append(',').append(dbField.getDigits());
            }
            typeName.append(")");
        } else if (type == DBFieldType.TIMESTAMP) {
            // pg TIMESTAMP 默认长度是 6
            typeName.append("(6)");
        }
        return typeName.toString();
    }

    private String getDefaultValue(DBField dbField) {
        final DBFieldType type = dbField.getType();
        final String defined = StringUtils.defaultIfEmpty(dbField.getDefaultValue(), "");
        if (type.isQuoteDefaultValue()) {
            return String.format("'%s'", defined);
        } else {
            return defined;
        }
    }

    private void appendAddFieldSchema(StringBuilder sb, DBField dbField) {
        sb.append(escapeSqlName(dbField.getName())).append(' ')
                .append(getFullTypeName(dbField));
        if (!dbField.isAutoIncrement() && dbField.getDefaultValue() != null) {
            sb.append(" DEFAULT ").append(getDefaultValue(dbField));
        }
        if (!dbField.isAllowNull()) {
            sb.append(" NOT NULL ");
        }
    }

    private void appendDropField(StringBuilder sb, String table, String field) {
        sb.append(String.format("alter table %s drop column %s;", escapeSqlName(table), escapeSqlName(field)));
    }

    private void appendRenameField(StringBuilder sb, String table, String oldField, String newField) {
        sb.append(String.format("alter table %s rename column %s to %s ;", table, escapeSqlName(oldField), escapeSqlName(newField)))
                .append(StringConstants.LINE_SEPARATOR);
    }


    @Override
    public String buildModifyTableSql(DBTable current, DBTable prev) {
        final String currentTableName = current.getName();
        final String prevTableName = prev.getName();
        StringBuilder sb = new StringBuilder(240);
        final String currTableName = escapeSqlName(currentTableName);
        final boolean isTableNameChange = !Objects.equals(currentTableName, prevTableName);
        if (isTableNameChange) {
            sb.append(String.format("alter table %s rename to %s;", escapeSqlName(prevTableName), currTableName))
                    .append(StringConstants.LINE_SEPARATOR);
        }
        final boolean isPrimaryKeyChange = !Objects.equals(current.getPrimaryKey(), prev.getPrimaryKey());
        if (isTableNameChange || isPrimaryKeyChange) {
            sb.append(String.format("alter table %s drop constraint if exists %s;", escapeSqlName(currTableName), getTablePkName(prevTableName)))
                    .append(StringConstants.LINE_SEPARATOR);
            if (StringUtils.isNotEmpty(current.getPrimaryKey())) {
                final String pkCols = Arrays.stream(StringUtils.split(current.getPrimaryKey(), StringConstants.COMMA))
                        .map(this::escapeSqlName).collect(Collectors.joining(StringConstants.COMMA));
                sb.append(String.format("alter table %s add constraint %s primary key (%s);", escapeSqlName(currTableName), getTablePkName(currentTableName), pkCols))
                        .append(StringConstants.LINE_SEPARATOR);
            }
        }

        if (!Objects.equals(current.getComment(), prev.getComment())) {
            appendTableComment(sb, current, true);
        }

        final Map<String, DBField> oldFields = prev.getFieldList().stream()
                .collect(Collectors.toMap(f -> StringUtils.defaultIfEmpty(f.getCode(), f.getName()), Function.identity()));
        //        final Set<String> existsNames = prev.getFieldList().stream().map(DbField::getName).collect(Collectors.toSet());

        final List<Pair<DBField, DBField>> fieldPairs = new ArrayList<>();
        for (DBField newField : current.getFieldList()) {
            DBField oldField = oldFields.remove(newField.getCode());
            if (oldField == null) {
                oldField = oldFields.remove(newField.getName());
            }
            fieldPairs.add(Pair.of(newField, oldField));
        }
        //        if (allowFieldDelete) {
        oldFields.forEach((k, v) -> appendDropField(sb, currTableName, v.getName()));
        //        } else {
        //            oldFields.forEach((k, v) -> {
        //                if (v.getName().endsWith("_bak")) {
        //                    return;
        //                }
        //                String bakFieldName = v.getName() + "_bak";
        //                while (existsNames.contains(bakFieldName)) {
        //                    bakFieldName = bakFieldName + "_bak";
        //                }
        //                existsNames.add(bakFieldName);
        //                appendRenameField(sb, currTableName, v.getName(), bakFieldName);
        //                if (!v.isAllowNull()) {
        //                    appendIsNullable(sb, true, currTableName, bakFieldName);
        //                }
        //            });
        //        }
        // 更新字段优先
        fieldPairs.sort(Comparator.comparingInt(p -> p.getValue() != null ? 0 : 1));

        for (Pair<DBField, DBField> pair : fieldPairs) {
            final DBField newField = pair.getLeft();
            final DBField oldField = pair.getRight();
            if (oldField == null) {
                sb.append(String.format("alter table %s add ", currTableName));
                appendAddFieldSchema(sb, newField);
                sb.append(';').append(StringConstants.LINE_SEPARATOR);
                appendFieldComment(sb, newField, currentTableName, false);
            } else if (!DBField.isEqual(newField, oldField)) {
                final String currFieldName = escapeSqlName(newField.getName());
                if (!Objects.equals(newField.getName(), oldField.getName())) {
                    appendRenameField(sb, currTableName, oldField.getName(), newField.getName());
                }

                final String newTypeName = getFullTypeName(newField);
                final String oldTypeName = getFullTypeName(oldField);
                // 自增，不允许修改类型
                if (!oldField.isAutoIncrement() && !Objects.equals(newTypeName, oldTypeName)) {
                    appendDropDefault(sb, currTableName, currFieldName);
                    sb.append(String.format("alter table %s alter column %s type %s using %s::%s;", currTableName, currFieldName, newTypeName, currFieldName, newTypeName))
                            .append(StringConstants.LINE_SEPARATOR);
                }
                final String newDefaultValue = newField.getDefaultValue();
                final String oldDefaultValue = oldField.getDefaultValue();
                // 自增，不允许修改默认值
                if (!oldField.isAutoIncrement() && !Objects.equals(newDefaultValue, oldDefaultValue)) {
                    if (newDefaultValue == null) {
                        appendDropDefault(sb, currTableName, currFieldName);
                    } else {
                        final String defaultValue = getDefaultValue(newField);
                        sb.append(String.format("alter table %s alter column %s set default %s;", currTableName, currFieldName, defaultValue))
                                .append(StringConstants.LINE_SEPARATOR);
                    }
                }
                if (!Objects.equals(newField.isAllowNull(), oldField.isAllowNull())) {
                    appendIsNullable(sb, newField.isAllowNull(), currTableName, currFieldName);
                }

                if (!Objects.equals(newField.getComment(), oldField.getComment())) {
                    appendFieldComment(sb, newField, currentTableName, true);
                }
            }
        }

        Map<String, DBIndex> existsIndexMap = prev.getIndexList().stream()
                .collect(Collectors.toMap(idx -> StringExUtils.subPrefix(idx.getName(), prev.getName() + "_"), Function.identity()));

        for (DBIndex dbIndex : current.getIndexList()) {

            final DBIndex oldIndex = existsIndexMap.remove(StringExUtils.subPrefix(dbIndex.getName(), currentTableName + "_"));
            if (oldIndex == null) {
                appendCreateIndex(sb, currentTableName, dbIndex);
            } else if (!DBIndex.isEqual(dbIndex, oldIndex)) {
                // 索引结构一样，因为表名变更导致名称变更
                if (DBIndex.isEqualIndex(dbIndex, oldIndex)) {
                    sb.append(String.format("alter index %s rename to %s;", escapeSqlName(oldIndex.getName()), escapeSqlName(fixIndexName(currentTableName, dbIndex.getName()))))
                            .append(StringConstants.LINE_SEPARATOR);
                } else {
                    appendDropIndex(sb, prevTableName, oldIndex);
                    appendCreateIndex(sb, currentTableName, dbIndex);
                }
            }
        }

        existsIndexMap.forEach((k, index) -> {
            appendDropIndex(sb, prevTableName, index);
        });
        return sb.toString();
    }

    private static void appendDropDefault(StringBuilder sb, String currTableName, String currFieldName) {
        sb.append(String.format("alter table %s alter column %s drop default;", currTableName, currFieldName))
                .append(StringConstants.LINE_SEPARATOR);
    }

    private void appendIsNullable(StringBuilder sb, boolean isAllowNull, String currTableName, String currFieldName) {
        if (isAllowNull) {
            sb.append(String.format("alter table %s alter column %s drop not null;", currTableName, currFieldName))
                    .append(StringConstants.LINE_SEPARATOR);
        } else {
            sb.append(String.format("alter table %s alter column %s set not null;", currTableName, currFieldName))
                    .append(StringConstants.LINE_SEPARATOR);
        }
    }

    private static String castTypeName(DBFieldType type) {
        if (type == DBFieldType.JSON) {
            return "jsonb";
        }
        if (type == DBFieldType.BLOB) {
            return "bytea";
        }
        return type.name().toLowerCase();
    }

    private String getTablePkName(String table) {
        return escapeSqlName(table + "_pk");
    }

    @Override
    protected DBField fixField(DBTable table, DBField field) {
        if (StringUtils.isNotEmpty(field.getDefaultValue())) {
            String defaultValue = StringExUtils.splitGet(field.getDefaultValue(), StringConstants.BI_COLON, 0);
            if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                defaultValue = defaultValue.substring(1, defaultValue.length() - 1);
            }
            field.setDefaultValue(defaultValue);
        } else {
            // pgsql text 类型可以有默认值
            if (field.getType().isText()) {
                field.setDefaultValue("");
            } else {
                field.setDefaultValue(null);
            }
        }
        return super.fixField(table, field);
    }

    @Override
    protected DBIndex fixIndex(String tableName, DBIndex index) {
        // 应报表库需求，调整索引名称格式为双下划线 __
        // 为了兼容原来单下划线的格式，处理时替换回单下划线
        final String prefix = tableName + "_";
        final String indexName = index.getName()
                .replaceFirst(tableName + "__", prefix);
        if (!indexName.startsWith(prefix)) {
            index.setName(prefix + indexName);
        } else {
            index.setName(indexName);
        }
        return super.fixIndex(tableName, index);
    }

    @Override
    public SqlDialect sqlDialect() {
        return SqlDialect.PGSQL;
    }
}
