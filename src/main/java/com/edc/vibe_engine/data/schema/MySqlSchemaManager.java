package com.edc.vibe_engine.data.schema;

import com.edc.vibe_engine.common.constants.StringConstants;
import com.edc.vibe_engine.common.support.StringExUtils;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import com.edc.vibe_engine.data.schema.types.DBField;
import com.edc.vibe_engine.data.schema.types.DBIndex;
import com.edc.vibe_engine.data.schema.types.DBTable;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class MySqlSchemaManager extends AbsJdbcSchemaManager {

    public MySqlSchemaManager(Supplier<Connection> connectionSupplier) {
        super(connectionSupplier);
    }

    @Override
    public String buildCreateTableSql(DBTable table) {
        StringBuilder sb = new StringBuilder(100)
                .append("CREATE TABLE ")
                .append(escapeSqlName(table.getName()))
                .append('(');
        for (DBField dbField : table.getFieldList()) {
            appendFieldSql(sb, dbField);
        }
        final String pkCols = Arrays.stream(StringUtils.split(table.getPrimaryKey(), StringConstants.COMMA))
                .map(this::escapeSqlName).collect(Collectors.joining(StringConstants.COMMA));
        if (StringUtils.isNotEmpty(pkCols)) {
            sb.append(" PRIMARY KEY (").append(pkCols).append(")");
        } else {
            sb.deleteCharAt(sb.length() - 1);
        }
        sb.append(")");
        if (StringUtils.isNotEmpty(table.getComment())) {
            sb.append(String.format(" comment '%s' ", table.getComment()));
        }
        sb.append("ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;");


        for (DBIndex dbIndex : table.getIndexList()) {
            sb.append(createIndexSql(table.getName(), dbIndex));
        }
        return sb.toString();
    }

    @Override
    public String buildModifyTableSql(DBTable current, DBTable prev) {
        final TableDiff tableDiff = compareDiff(current, prev);
        if (!tableDiff.hasChange()) {
            return "";
        }
        StringBuilder sb = new StringBuilder(128);
        if (tableDiff.isTableNameChange() || tableDiff.getFieldChangeCount() > 0) {

            sb.append("ALTER TABLE ")
                    .append(escapeSqlName(tableDiff.altTableName));
            if (tableDiff.isTableNameChange()) {
                sb.append(" RENAME TO ").append(escapeSqlName(tableDiff.modifyTableName)).append(',');
            }
            if (!CollectionUtils.isEmpty(tableDiff.deleteFields)) {
                for (DBField dbField : tableDiff.deleteFields) {
                    sb.append(" DROP ").append(escapeSqlName(dbField.getName())).append(',');
                }
            }
            if (!CollectionUtils.isEmpty(tableDiff.updateFields)) {
                for (DBField dbField : tableDiff.updateFields) {
                    if (StringUtils.isEmpty(dbField.getOriginalName())) {
                        sb.append(" MODIFY ");
                    } else {
                        sb.append(" CHANGE ");
                    }
                    appendFieldSql(sb, dbField);
                }
            }
            if (!CollectionUtils.isEmpty(tableDiff.addFields)) {
                for (DBField dbField : tableDiff.addFields) {
                    sb.append(" ADD ");
                    appendFieldSql(sb, dbField);
                }
            }
            sb.deleteCharAt(sb.length() - 1).append(";").append(StringConstants.LINE_SEPARATOR);
        }
        if (!CollectionUtils.isEmpty(tableDiff.deleteIndexes)) {
            for (DBIndex deleteIndex : tableDiff.deleteIndexes) {
                sb.append(dropIndexSql(escapeSqlName(tableDiff.modifyTableName), deleteIndex));
            }
        }
        if (!CollectionUtils.isEmpty(tableDiff.addIndexes)) {
            for (DBIndex addIndex : tableDiff.addIndexes) {
                sb.append(createIndexSql(escapeSqlName(tableDiff.modifyTableName), addIndex));
            }
        }


        return sb.toString();
    }


    public void appendFieldSql(StringBuilder sb, DBField dbField) {
        boolean isAutoIdField = dbField.isAutoIncrement();
        if (StringUtils.isNotEmpty(dbField.getOriginalName()) && !Objects.equals(dbField.getOriginalName(), dbField.getName())) {
            sb.append(escapeSqlName(dbField.getOriginalName())).append(' ');
        }
        final DBFieldType type = dbField.getType();
        sb.append(escapeSqlName(dbField.getName()))
                .append(' ')
                .append(castTypeName(type));
        if (type.isHasSize() && dbField.getSize() > 0) {
            sb.append('(').append(dbField.getSize());
            if (type.isHasDigit() && dbField.getDigits() > 0) {
                sb.append(',').append(dbField.getDigits());
            }
            sb.append(") ");
        }
        if (StringUtils.isNotEmpty(dbField.getCollate())) {
            sb.append(String.format(" collate %s ", dbField.getCollate()));
        }

        sb.append(isAutoIdField ? " AUTO_INCREMENT " : "")
                .append(!dbField.isAllowNull() ? " NOT NULL " : "");
        final String defaultValue = dbField.getDefaultValue();
        if (defaultValue != null) {
            sb.append(" DEFAULT ");
            if (type.isQuoteDefaultValue()) {
                sb.append(String.format(" '%s' ", defaultValue));
            } else {
                sb.append(defaultValue);
            }
        }
        if (StringUtils.isNotEmpty(dbField.getComment())) {
            sb.append(" COMMENT '")
                    .append(dbField.getComment())
                    .append('\'');
        }
        sb.append(',');
    }

    private static String castTypeName(DBFieldType type) {
        if (type == DBFieldType.TIMESTAMP) {
            return "datetime";
        }
        if (type == DBFieldType.TEXT) {
            return "mediumtext";
        }
        if (type == DBFieldType.BLOB) {
            return "longblob";
        }
        return type.name().toLowerCase();
    }

    private String dropIndexSql(String tableName, DBIndex dbIndex) {
        return " DROP INDEX "
               + escapeSqlName(dbIndex.getName())
               + " ON "
               + escapeSqlName(tableName)
               + ';';
    }

    private String createIndexSql(String tableName, DBIndex dbIndex) {
        StringBuilder sb = new StringBuilder(50);
        if (dbIndex.isUnique()) {
            sb.append("CREATE UNIQUE INDEX ");
        } else {
            sb.append("CREATE INDEX ");
        }
        sb.append(escapeSqlName(dbIndex.getName()))
                .append(" ON ")
                .append(escapeSqlName(tableName))
                .append('(')
                .append(String.join(",", dbIndex.getFields()))
                .append(");");
        return sb.toString();
    }


    TableDiff compareDiff(DBTable current, DBTable prev) {
        final String altTableName = getAltTableName(current, prev);
        final Map<String, DBField> oldFields = prev.getFieldList().stream()
                .collect(Collectors.toMap(f -> StringUtils.defaultIfEmpty(f.getCode(), f.getName()), Function.identity()));
        final Set<String> existsNames = prev.getFieldList().stream().map(DBField::getName).collect(Collectors.toSet());

        TableDiff tableDiff = new TableDiff();
        tableDiff.altTableName = altTableName;
        tableDiff.modifyTableName = current.getName();

        for (DBField newField : current.getFieldList()) {
            DBField oldField = oldFields.remove(newField.getCode());
            if (oldField == null) {
                oldField = oldFields.remove(newField.getName());
            }

            if (oldField == null) {
                tableDiff.addFields.add(newField);
            } else if (!DBField.isEqual(newField, oldField)) {
                if (!newField.getName().equals(oldField.getName())) {
                    newField.setOriginalName(oldField.getName());
                }
                tableDiff.updateFields.add(newField);
            }
        }

        //        if (allowFieldDelete) {
        oldFields.forEach((k, v) -> {
            tableDiff.deleteFields.add(v);
        });
        //        } else {
        //            oldFields.forEach((k, v) -> {
        //                if (v.getName().endsWith("_bak")) {
        //                    return;
        //                }
        //                final String oldName = v.getName();
        //                String bakFieldName = oldName + "_bak";
        //                while (existsNames.contains(bakFieldName)) {
        //                    bakFieldName = bakFieldName + "_bak";
        //                }
        //                existsNames.add(bakFieldName);
        //
        //                final DbField updateField = v.toBuilder()
        //                        .originalName(oldName)
        //                        .allowNull(true)
        //                        .name(bakFieldName).build();
        //
        //                tableDiff.updateFields.add(updateField);
        //            });
        //        }

        Map<String, DBIndex> existsIndexMap = prev.getIndexList().stream()
                .collect(Collectors.toMap(DBIndex::getName, Function.identity()));

        for (DBIndex dbIndex : current.getIndexList()) {
            final DBIndex oldIndex = existsIndexMap.remove(dbIndex.getName());
            if (oldIndex == null) {
                tableDiff.addIndexes.add(dbIndex);
            } else if (!DBIndex.isEqual(dbIndex, oldIndex)) {
                tableDiff.deleteIndexes.add(oldIndex);
                tableDiff.addIndexes.add(dbIndex);
            }
        }

        existsIndexMap.forEach((k, index) -> {
            tableDiff.deleteIndexes.add(index);
        });

        return tableDiff;
    }

    @Override
    public SqlDialect sqlDialect() {
        return SqlDialect.MYSQL;
    }

    @Override
    protected DBField fixField(DBTable table, DBField field) {
        // mysql text 类型不允许有默认值
        if (StringUtils.isEmpty(field.getDefaultValue())) {
            if (field.getType().isShortText()) {
                field.setDefaultValue("");
            } else {
                field.setDefaultValue(null);
            }
        }
        return super.fixField(table, field);
    }

    @Override
    protected DBIndex fixIndex(String tableName, DBIndex index) {
        index.setName(StringExUtils.subPrefix(index.getName(), tableName + "__"));
        return super.fixIndex(tableName, index);
    }


    public static class TableDiff {

        /**
         * 修改表名
         */
        public String altTableName;
        public String modifyTableName;

        public List<DBField> addFields = new ArrayList<>();
        public List<DBField> updateFields = new ArrayList<>();
        public List<DBField> deleteFields = new ArrayList<>();


        public List<DBIndex> addIndexes = new ArrayList<>();
        public List<DBIndex> deleteIndexes = new ArrayList<>();

        public boolean isTableNameChange() {
            return !Objects.equals(altTableName, modifyTableName);
        }

        public boolean hasChange() {
            return isTableNameChange() || getFieldChangeCount() > 0 || getIndexChangeCount() > 0;
        }

        public int getFieldChangeCount() {
            return addFields.size() + updateFields.size() + deleteFields.size();
        }

        public int getIndexChangeCount() {
            return addIndexes.size() + deleteIndexes.size();
        }
    }

}
