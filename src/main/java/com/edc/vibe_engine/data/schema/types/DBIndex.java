package com.edc.vibe_engine.data.schema.types;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Singular;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 索引对象
 */
@Setter
@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DBIndex implements Serializable {
    @Serial
    private static final long serialVersionUID = 7767810983886970213L;
    /**
     * 索引名称
     */
    private String name;
    /**
     * 索引字段
     */
    @Singular("field")
    private List<String> fields;
    /**
     * 是否唯一索引
     */
    private boolean unique;

    /**
     * 索引方法类型
     */
    @Builder.Default
    private String method = IndexMethod.B_TREE.getSqlMethodName();


    public static boolean isEqual(DBIndex newIndex, DBIndex oldIndex) {
        if (!Objects.equals(newIndex.getName(), oldIndex.getName())) return false;
        return isEqualIndex(newIndex, oldIndex);
    }

    public static boolean isEqualIndex(DBIndex newIndex, DBIndex oldIndex) {
        final List<String> newFields = new ArrayList<>(newIndex.getFields());
        final List<String> oldFields = new ArrayList<>(oldIndex.getFields());
        Collections.sort(newFields);
        Collections.sort(oldFields);
        if (!Objects.equals(newFields, oldFields)) return false;
        if (!Objects.equals(newIndex.isUnique(), oldIndex.isUnique())) return false;
        return Objects.equals(newIndex.getMethod(), oldIndex.getMethod());
    }


    @Getter
    public enum IndexMethod {

        //    /**
        //     * 哈希
        //     */
        //    HASH(0, "哈希", "HASH"),
        /**
         * B树
         */
        B_TREE(0, "enum.index_method.b_tree", "BTREE"),
        ;


        /**
         * 值
         */
        private final int value;
        /**
         * 名称
         */
        private final String name;

        /**
         * 数据库方法名
         */
        private final String sqlMethodName;


        IndexMethod(int value, String name, String sqlMethodName) {
            this.value = value;
            this.name = name;
            this.sqlMethodName = sqlMethodName;
        }

        public static IndexMethod getByValue(int value) {
            for (IndexMethod object : IndexMethod.values()) {
                if (object.getValue() == value) {
                    return object;
                }
            }
            return null;
        }


        public static IndexMethod getBySqlMethodName(String sqlMethodName) {
            for (IndexMethod value : IndexMethod.values()) {
                if (Objects.equals(value.getSqlMethodName(), sqlMethodName)) {
                    return value;
                }
            }
            return null;
        }
    }

}
