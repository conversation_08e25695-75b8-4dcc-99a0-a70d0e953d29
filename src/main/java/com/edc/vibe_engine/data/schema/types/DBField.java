package com.edc.vibe_engine.data.schema.types;


import com.edc.vibe_engine.common.support.ObjectExUtils;
import com.edc.vibe_engine.common.support.TypeCastUtils;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DBField implements Serializable {
    @Serial
    private static final long serialVersionUID = -7728779911219399162L;

    /**
     * 从上个发布版本取id，上个发布版本的字段名与当前数据库名称一致
     * id与新修改了名称的字段一致，
     * 这样才能把新修改名称的字段与当前数据库字段关联起来
     */
    private String code;

    private String name;

    /**
     * 修改名称，旧字段名
     */
    private String originalName;

    private DBFieldType type;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 字符集
     */
    private String collate;
    private boolean allowNull;
    private int size;
    private boolean autoIncrement;
    private int digits;

    @Builder.Default
    private String comment = "";

    public static boolean isEqual(DBField newField, DBField oldField) {
        // 非数字字段不需要对比保留小数位数
        if (newField.isAllowNull() != oldField.isAllowNull()) return false;
        final DBFieldType type = oldField.getType();
        // 日期/文本类型无size,跳过
        if (type.isHasSize() && newField.getSize() != oldField.getSize())
            return false;
        if (newField.isAutoIncrement() != oldField.isAutoIncrement()) return false;
        // 非数字字段不需要对比保留小数位数
        if (type.isHasDigit() && newField.getDigits() != oldField.getDigits())
            return false;
        if (!Objects.equals(newField.getType(), oldField.getType())) return false;
        if (!Objects.equals(newField.getCollate(), oldField.getCollate())) return false;
        if (type.isHasDigit()) {
            // 带精度，转换成数值后比对
            final BigDecimal newDecimal = TypeCastUtils.castToBigDecimal(newField.getDefaultValue());
            final BigDecimal oldDecimal = TypeCastUtils.castToBigDecimal(oldField.getDefaultValue());
            if (!ObjectExUtils.isEqual(newDecimal, oldDecimal))
                return false;
        } else {
            if (!Objects.equals(newField.getDefaultValue(), oldField.getDefaultValue()))
                return false;
        }

        if (!Objects.equals(newField.getName(), oldField.getName())) return false;
        // comment sql 中默认值为空字符串
        return Objects.equals(StringUtils.defaultIfEmpty(newField.getComment(), ""), StringUtils.defaultIfEmpty(oldField.getComment(), ""));
    }
}
