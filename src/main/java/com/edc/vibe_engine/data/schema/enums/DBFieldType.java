package com.edc.vibe_engine.data.schema.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.sql.Types;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据库字段类型
 *
 * @see org.apache.ibatis.type.JdbcType
 */
public enum DBFieldType {

    //    ARRAY(Types.ARRAY),


    SMALLINT(Types.SMALLINT, false, false),

    INTEGER(Types.INTEGER, false, false, "INT"),

    BIGINT(Types.BIGINT, false, false),

    FLOAT(Types.FLOAT, false, false),

    DOUBLE(Types.DOUBLE, false, false),

    DECIMAL(Types.DECIMAL, true, true, "NUMERIC"),

    CHAR(Types.CHAR, true, false),
    VARCHAR(Types.VARCHAR, true, false),
    TEXT(Types.LONGVARCHAR, false, false, "LONGVARCHAR", "MEDIUMTEXT", "LONGTEXT"),

    DATE(Types.DATE, false, false),
    TIME(Types.TIME, true, false),
    TIMESTAMP(Types.TIMESTAMP, true, false, "DATETIME"),

    BLOB(Types.BLOB, false, false, "MEDIUMBLOB", "LONGBLOB", "BYTEA"),
    BOOLEAN(Types.BOOLEAN, false, false, "BOOL", "TINYINT"),


    //    CLOB(Types.CLOB),
    //    CURSOR(-10), // Oracle
    //    UNDEFINED(Integer.MIN_VALUE + 1000),
    //    NVARCHAR(Types.NVARCHAR), // JDK6
    //    NCHAR(Types.NCHAR), // JDK6
    //    NCLOB(Types.NCLOB), // JDK6
    //    STRUCT(Types.STRUCT),
    //    JAVA_OBJECT(Types.JAVA_OBJECT),
    //    DISTINCT(Types.DISTINCT),
    //    REF(Types.REF),
    //    DATALINK(Types.DATALINK),
    //    ROWID(Types.ROWID), // JDK6
    //    SQLXML(Types.SQLXML), // JDK6
    //    DATETIMEOFFSET(-155), // SQL Server 2008
    //    TIME_WITH_TIMEZONE(Types.TIME_WITH_TIMEZONE), // JDBC 4.2 JDK8
    //    TIMESTAMP_WITH_TIMEZONE(Types.TIMESTAMP_WITH_TIMEZONE) // JDBC 4.2 JDK8


    JSON(Types.JAVA_OBJECT, false, false, "JSONB"),
    BINARY(Types.BINARY, true, false),
    UNKNOWN(Types.OTHER, false, false),


    ;

    private final int jdbcType;
    private final boolean hasSize;
    private final boolean hasDigit;
    private final Set<String> aliaNames;

    DBFieldType(int jdbcType, boolean hasSize, boolean hasDigit, String... aliaNames) {
        this.jdbcType = jdbcType;
        this.hasSize = hasSize;
        this.hasDigit = hasDigit;
        this.aliaNames = new HashSet<>(Arrays.asList(aliaNames));
    }


    public int jdbcType() {
        return this.jdbcType;
    }

    public boolean isHasSize() {
        return hasSize;
    }

    public boolean isHasDigit() {
        return hasDigit;
    }

    public boolean isQuoteDefaultValue() {
        return isNumeric() || isText() || isDate();
    }

    public boolean isJson() {
        return this == JSON;
    }


    public boolean isDate() {
        return this == DATE || this == TIME || this == TIMESTAMP;
    }

    public boolean isText() {
        return isShortText() || isLongText();
    }

    public boolean isLongText() {
        return this == TEXT;
    }

    public boolean isShortText() {
        return this == CHAR || this == VARCHAR;
    }

    public boolean isNumeric() {
        return this == DECIMAL;
    }

    public boolean isNumber() {
        return this == SMALLINT || this == INTEGER || this == BIGINT || this == DOUBLE || isNumeric();
    }

    public boolean isMatchTypeName(String typeName) {
        final String upperCase = typeName.toUpperCase();
        return this.name().equals(upperCase) || this.aliaNames.contains(upperCase);
    }

    @JsonCreator
    public static DBFieldType from(String text) {
        for (DBFieldType value : DBFieldType.values()) {
            if (value.isMatchTypeName(text)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static DBFieldType fromJdbcType(int type, String text) {
        if (JSON.isMatchTypeName(text)) {
            return JSON;
        }
        if (TEXT.isMatchTypeName(text)) {
            return TEXT;
        }
        if (BLOB.isMatchTypeName(text)) {
            return BLOB;
        }
        if (BOOLEAN.isMatchTypeName(text)) {
            return BOOLEAN;
        }
        for (DBFieldType value : DBFieldType.values()) {
            if (value.isMatchTypeName(text)) {
                return value;
            }
            if (value.jdbcType == type) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
