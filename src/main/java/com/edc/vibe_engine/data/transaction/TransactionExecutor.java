package com.edc.vibe_engine.data.transaction;

import com.edc.vibe_engine.data.idgen.IdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.DefaultTransactionStatus;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * todo 异步事务
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class TransactionExecutor {

    public static final String PREFIX = "__PAAS__";
    public static final int DEFAULT_TIMEOUT = 60;
    private final PlatformTransactionManager transactionManager;

    private static final Map<String, Set<Runnable>> INNER_AFTER_COMMITS = new ConcurrentHashMap<>(64);

    private static final ThreadLocal<Boolean> AFTER_COMMIT_ACTIVE = new NamedThreadLocal<>("After Commit Active");

    public <T> T runWithRequiresNew(Function<TransactionStatus, T> function) {
        return runWithRequiresNew(function, Exception.class);
    }


    public <T> T runWithRequiresNew(Function<TransactionStatus, T> function, Class<? extends Throwable> rollbackClazz) {
        // 开启新事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        definition.setTimeout(DEFAULT_TIMEOUT);
        definition.setName(getInnerTransactionName());
        return runWithTransaction(definition, function, rollbackClazz);
    }

    private static String getInnerTransactionName() {
        return PREFIX + IdGenerator.nextSnowId();
    }

    /**
     * @return 当前是否在执行 afterCommit 过程中
     */
    public static boolean isAfterCommitActive() {
        return (AFTER_COMMIT_ACTIVE.get() != null);
    }

    private static boolean isInnerTransactionName(String name) {
        return StringUtils.isNotEmpty(name) && name.startsWith(PREFIX);
    }

    public <T> T runWithRequired(Function<TransactionStatus, T> function) {
        return runWithRequired(function, Exception.class);
    }

    public <T> T runWithRequired(Function<TransactionStatus, T> function, Class<? extends Throwable> rollbackClazz) {
        // 如果当前是在机制的 afterCommit 回调中，且需要处理事务，则需要起一个新的事务来处理
        final int propagationBehavior = isAfterCommitActive() ? TransactionDefinition.PROPAGATION_REQUIRES_NEW : TransactionDefinition.PROPAGATION_REQUIRED;
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition(propagationBehavior);
        definition.setTimeout(DEFAULT_TIMEOUT);
        definition.setName(getInnerTransactionName());
        return runWithTransaction(definition, function, rollbackClazz);
    }

    private Runnable wrapAfterCommit(Runnable runnable) {
        return () -> {
            AFTER_COMMIT_ACTIVE.set(true);
            runnable.run();
            AFTER_COMMIT_ACTIVE.remove();
        };
    }


    public void registerAfterCommit(final Runnable runnable) {
        final String txId = TransactionSynchronizationManager.getCurrentTransactionName();
        if (isInnerTransactionName(txId)) {
            // 如果是内部发起的事务，注册到内部的回调集合中，执行时连接资源已经真正释放了
            final Set<Runnable> runnables = INNER_AFTER_COMMITS.computeIfAbsent(txId, k -> new LinkedHashSet<>());
            runnables.add(runnable);
        } else {
            final Runnable afterCommit = wrapAfterCommit(runnable);
            // 否则做一层包装，注册到 spring 事务回调中，执行时连接资源其实还未释放，内部执行时若需要事务，则会起一个新的事务
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    afterCommit.run();
                }
            });
        }
    }

    /**
     * @param definition
     * @param function
     * @param rollbackClazz
     * @param <T>
     * @return
     */
    @SuppressWarnings("PMD.AvoidCatchingThrowable")
    protected <T> T runWithTransaction(TransactionDefinition definition, Function<TransactionStatus, T> function, Class<? extends Throwable> rollbackClazz) {
        DefaultTransactionStatus status = (DefaultTransactionStatus) this.transactionManager.getTransaction(definition);
        T retVal;
        try {
            retVal = function.apply(status);
        } catch (Throwable ex) {
            if (rollbackOn(ex, rollbackClazz)) {
                try {
                    transactionManager.rollback(status);
                } catch (TransactionSystemException e) {
                    log.error("Application exception overridden by rollback exception", ex);
                    e.initApplicationException(ex);
                    throw e;
                } catch (RuntimeException | Error e) {
                    log.error("Application exception overridden by rollback exception", ex);
                    throw e;
                }
            } else {
                // We don't roll back on this exception.
                // Will still roll back if TransactionStatus.isRollbackOnly() is true.
                try {
                    doCommit(status);
                } catch (TransactionSystemException e) {
                    log.error("Application exception overridden by commit exception", ex);
                    e.initApplicationException(ex);
                    throw e;
                } catch (RuntimeException | Error e) {
                    log.error("Application exception overridden by commit exception", ex);
                    throw e;
                }
            }
            throw ex;
        }

        doCommit(status);
        return retVal;
    }

    private void doCommit(DefaultTransactionStatus status) {
        final String txId = TransactionSynchronizationManager.getCurrentTransactionName();
        transactionManager.commit(status);
        if (status.isNewTransaction()) {
            // 当前是事务的起点，执行回调
            final Set<Runnable> runnables = INNER_AFTER_COMMITS.remove(txId);
            if (CollectionUtils.isNotEmpty(runnables)) {
                for (Runnable runnable : runnables) {
                    runnable.run();
                }
            }
        }
    }

    private boolean rollbackOn(Throwable ex, Class<? extends Throwable> rollbackFor) {
        if (rollbackFor != null) {
            return rollbackFor.isAssignableFrom(ex.getClass());
        }
        return (ex instanceof RuntimeException || ex instanceof Error);
    }
}
