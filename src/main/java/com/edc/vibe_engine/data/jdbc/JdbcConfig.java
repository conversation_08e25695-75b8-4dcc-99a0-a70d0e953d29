//package com.edc.vibe_engine.data.jdbc;
//
//
//import com.edc.vibe_engine.common.support.AssertUtils;
//import com.edc.vibe_engine.data.sql.enums.SqlDialect;
//import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
//import lombok.Getter;
//import lombok.Setter;
//import org.apache.commons.lang3.StringUtils;
//
//@Getter
//@Setter
//public class JdbcConfig {
//
//    private SqlDialect dialect;
//
//    private String address;
//
//    private int port;
//
//    private String database;
//    private String schema;
//
//    private String username;
//
//    private String password;
//
//    public JdbcConfig() {
//    }
//
//    public JdbcConfig(SqlDialect dialect, String address, int port, String database, String schema, String username, String password) {
//        this.dialect = dialect;
//        this.address = address;
//        this.database = database;
//        this.port = port;
//        this.schema = schema;
//        this.username = username;
//        this.password = password;
//    }
//
//    public JdbcConfig(SqlDialect dialect, String address, int port) {
//        this.dialect = dialect;
//        this.address = address;
//        this.port = port;
//    }
//
//    public JdbcConfig schema(String schema) {
//        this.schema = schema;
//        return this;
//    }
//
//    public JdbcConfig schema(String catalog, String schema) {
//        this.database = catalog;
//        this.schema = schema;
//        return this;
//    }
//
//    public JdbcConfig auth(String user, String password) {
//        this.username = user;
//        this.password = password;
//        return this;
//    }
//
//    public String buildUrl() {
//        valid("buildUrl", StringUtils.isNoneEmpty(schema));
//        SqlDialectHelper.testJdbcDriver(dialect);
//        return dialect.getProtocol() +
//               address +
//               ':' +
//               port +
//               '/' +
//               SqlDialectHelper.getJdbcUrlPath(dialect, database) +
//               SqlDialectHelper.getJdbcUrlQueryString(dialect, schema);
//    }
//
//    public JdbcConfig mutate(String catalog, String schema) {
//        return new JdbcConfig(dialect, address, port, catalog, schema, username, password);
//    }
//
//    public void valid(String msgTag, boolean validSchema) {
//        AssertUtils.assertNotNull(dialect, "{} dialect is null!", msgTag);
//        AssertUtils.assertHasText(address, "{} address is empty!", msgTag);
//        AssertUtils.assertTrue(port > 0, "{} port is 0!", msgTag);
//        if (!validSchema) {
//            return;
//        }
//        AssertUtils.assertHasText(schema, "{} schema is empty!", msgTag);
//        //        if (dialect == SqlDialect.PGSQL) {
//        //            AssertUtils.assertHasText(catalog, "{} catalog is empty!", msgTag);
//        //        }
//    }
//
//}
