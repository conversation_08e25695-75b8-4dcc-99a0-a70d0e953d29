//package com.edc.vibe_engine.data.mapper;
//
//import com.edc.vibe_engine.record.interfaces.IRecord;
//import com.exe.cloud.data.record.MapRecord;
//
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.util.Map;
//
//public class MapRecordRowMapper extends AbsRowMapper<IRecord> {
//    @Override
//    public MapRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
//        Map<String, Object> result = getResultMap(rs);
//        return new MapRecord(result);
//    }
//}
