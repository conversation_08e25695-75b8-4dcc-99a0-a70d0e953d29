package com.edc.vibe_engine.data.mapper;

import com.edc.vibe_engine.data.support.RowMapperUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public abstract class AbsRowMapper<T> implements RowMapper<T> {

    protected Map<String, Object> getResultMap(ResultSet rs) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        RowMapperUtils.forEachColumn(rs, result::putIfAbsent);
        return result;
    }
}
