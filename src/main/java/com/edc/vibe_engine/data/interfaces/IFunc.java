/*
 * Copyright (c) 2011-2022, baomi<PERSON>u (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.interfaces;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * 查询条件封装
 *
 * <AUTHOR> miemie <PERSON>
 * @since 2017-05-26
 */
@SuppressWarnings("unchecked")
public interface IFunc<R, F> extends Serializable {

    /**
     * ignore
     */
    default R isNull(F column) {
        return isNull(true, column);
    }

    /**
     * 字段 IS NULL
     * <p>例: isNull("name")</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @return children
     */
    R isNull(boolean condition, F column);

    /**
     * ignore
     */
    default R isNotNull(F column) {
        return isNotNull(true, column);
    }

    /**
     * 字段 IS NOT NULL
     * <p>例: isNotNull("name")</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @return children
     */
    R isNotNull(boolean condition, F column);

    /**
     * ignore
     */
    default R in(F column, Collection<?> coll) {
        return in(true, column, coll);
    }

    /**
     * 字段 IN (value.get(0), value.get(1), ...)
     * <p>例: in("id", Arrays.asList(1, 2, 3, 4, 5))</p>
     *
     * <li> 注意！集合为空若存在逻辑错误，请在 condition 条件中判断 </li>
     * <li> 如果集合为 empty 则不会进行 sql 拼接 </li>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param coll      数据集合
     * @return children
     */
    R in(boolean condition, F column, Collection<?> coll);

    /**
     * ignore
     */
    default R in(F column, Object... values) {
        return in(true, column, values);
    }

    /**
     * 字段 IN (v0, v1, ...)
     * <p>例: in("id", 1, 2, 3, 4, 5)</p>
     *
     * <li> 注意！数组为空若存在逻辑错误，请在 condition 条件中判断 </li>
     * <li> 如果动态数组为 empty 则不会进行 sql 拼接 </li>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param values    数据数组
     * @return children
     */
    R in(boolean condition, F column, Object... values);

    /**
     * ignore
     */
    default R includesAny(F column, Collection<?> coll) {
        return includesAny(true, column, coll);
    }

    /**
     * 数组字段，包含入参数组值的任意一项
     * <p>例: anyIn("user_ids", Arrays.asList(1, 2, 3, 4, 5))</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param coll      数据集合
     * @return children
     */
    R includesAny(boolean condition, F column, Collection<?> coll);

    /**
     * ignore
     */
    default R includesAny(F column, Object... values) {
        return includesAny(true, column, values);
    }

    /**
     * 数组字段，包含入参数组值的任意一项
     * <p>例: anyIn("user_ids", 1, 2, 3, 4, 5)</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param values    数据数组
     * @return children
     */
    R includesAny(boolean condition, F column, Object... values);

    /**
     * ignore
     */
    default R notIn(F column, Collection<?> coll) {
        return notIn(true, column, coll);
    }

    /**
     * 字段 NOT IN (value.get(0), value.get(1), ...)
     * <p>例: notIn("id", Arrays.asList(1, 2, 3, 4, 5))</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param coll      数据集合
     * @return children
     */
    R notIn(boolean condition, F column, Collection<?> coll);

    /**
     * ignore
     */
    default R notIn(F column, Object... value) {
        return notIn(true, column, value);
    }

    /**
     * 字段 NOT IN (v0, v1, ...)
     * <p>例: notIn("id", 1, 2, 3, 4, 5)</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param values    数据数组
     * @return children
     */
    R notIn(boolean condition, F column, Object... values);

    /**
     * ignore
     */
    default R inSql(F column, String inValue) {
        return inSql(true, column, inValue);
    }

    /**
     * 字段 IN ( sql语句 )
     * <p>!! sql 注入方式的 in 方法 !!</p>
     * <p>例1: inSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例2: inSql("id", "select id from table where id &lt; 3")</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param inValue   sql语句
     * @return children
     */
    R inSql(boolean condition, F column, String inValue);

    /**
     * 字段 &gt; ( sql语句 )
     * <p>例1: gtSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例1: gtSql("id", "select id from table where name = 'JunJun'")</p>
     *
     * @param condition
     * @param column
     * @param inValue
     * @return
     */
    R gtSql(boolean condition, F column, String inValue);

    /**
     * ignore
     */
    default R gtSql(F column, String inValue) {
        return gtSql(true, column, inValue);
    }

    /**
     * 字段 >= ( sql语句 )
     * <p>例1: geSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例1: geSql("id", "select id from table where name = 'JunJun'")</p>
     *
     * @param condition
     * @param column
     * @param inValue
     * @return
     */
    R geSql(boolean condition, F column, String inValue);

    /**
     * ignore
     */
    default R geSql(F column, String inValue) {
        return geSql(true, column, inValue);
    }

    /**
     * 字段 &lt; ( sql语句 )
     * <p>例1: ltSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例1: ltSql("id", "select id from table where name = 'JunJun'")</p>
     *
     * @param condition
     * @param column
     * @param inValue
     * @return
     */
    R ltSql(boolean condition, F column, String inValue);

    /**
     * ignore
     */
    default R ltSql(F column, String inValue) {
        return ltSql(true, column, inValue);
    }

    /**
     * 字段 <= ( sql语句 )
     * <p>例1: leSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例1: leSql("id", "select id from table where name = 'JunJun'")</p>
     *
     * @param condition
     * @param column
     * @param inValue
     * @return
     */
    R leSql(boolean condition, F column, String inValue);

    /**
     * ignore
     */
    default R leSql(F column, String inValue) {
        return leSql(true, column, inValue);
    }

    /**
     * ignore
     */
    default R notInSql(F column, String inValue) {
        return notInSql(true, column, inValue);
    }

    /**
     * 字段 NOT IN ( sql语句 )
     * <p>!! sql 注入方式的 not in 方法 !!</p>
     * <p>例1: notInSql("id", "1, 2, 3, 4, 5, 6")</p>
     * <p>例2: notInSql("id", "select id from table where id &lt; 3")</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @param inValue   sql语句 ---&gt; 1,2,3,4,5,6 或者 select id from table where id &lt; 3
     * @return children
     */
    R notInSql(boolean condition, F column, String inValue);

    /**
     * 分组：GROUP BY 字段, ...
     * <p>例: groupBy("id")</p>
     *
     * @param condition 执行条件
     * @param column    单个字段
     * @return children
     */
    R groupBy(boolean condition, F column);

    default R groupBy(F column) {
        return groupBy(true, column);
    }

    /**
     * 分组：GROUP BY 字段, ...
     * <p>例: groupBy(Arrays.asList("id", "name"))</p>
     *
     * @param condition 执行条件
     * @param columns   字段数组
     * @return children
     */
    R groupBy(boolean condition, List<F> columns);

    default R groupBy(List<F> columns) {
        return groupBy(true, columns);
    }

    default R groupBy(F column, F... columns) {
        return groupBy(true, column, columns);
    }

    /**
     * 分组：GROUP BY 字段, ...
     */
    R groupBy(boolean condition, F column, F... columns);

    /**
     * 排序：ORDER BY 字段, ... ASC
     * <p>例: orderByAsc(true, "id")</p>
     *
     * @param condition 执行条件
     * @param column    单个字段
     * @return children
     */
    default R orderByAsc(boolean condition, F column) {
        return orderBy(condition, true, column);
    }

    default R orderByAsc(F column) {
        return orderByAsc(true, column);
    }

    /**
     * 排序：ORDER BY 字段, ... ASC
     * <p>例: orderByAsc(true, Arrays.asList("id", "name"))</p>
     *
     * @param condition 执行条件
     * @param columns   字段数组
     * @return children
     */
    default R orderByAsc(boolean condition, List<F> columns) {
        return orderBy(condition, true, columns);
    }

    default R orderByAsc(List<F> columns) {
        return orderByAsc(true, columns);
    }

    default R orderByAsc(F column, F... columns) {
        return orderByAsc(true, column, columns);
    }

    /**
     * 排序：ORDER BY 字段, ... ASC
     */
    default R orderByAsc(boolean condition, F column, F... columns) {
        return orderBy(condition, true, column, columns);
    }

    /**
     * 排序：ORDER BY 字段, ... DESC
     * <p>例: orderByDesc(true, "id")</p>
     *
     * @param condition 执行条件
     * @param column    字段
     * @return children
     */
    default R orderByDesc(boolean condition, F column) {
        return orderBy(condition, false, column);
    }

    default R orderByDesc(F column) {
        return orderByDesc(true, column);
    }

    /**
     * 排序：ORDER BY 字段, ... DESC
     * <p>例: orderByDesc(true, Arrays.asList("id", "name"))</p>
     *
     * @param condition 执行条件
     * @param columns   字段列表
     * @return children
     */
    default R orderByDesc(boolean condition, List<F> columns) {
        return orderBy(condition, false, columns);
    }

    default R orderByDesc(List<F> columns) {
        return orderByDesc(true, columns);
    }

    default R orderByDesc(F column, F... columns) {
        return orderByDesc(true, column, columns);
    }

    /**
     * 排序：ORDER BY 字段, ... DESC
     */
    default R orderByDesc(boolean condition, F column, F... columns) {
        return orderBy(condition, false, column, columns);
    }

    /**
     * 排序：ORDER BY 字段, ...
     * <p>例: orderBy(true, "id")</p>
     *
     * @param condition 执行条件
     * @param isAsc     是否是 ASC 排序
     * @param column    单个字段
     * @return children
     */
    R orderBy(boolean condition, boolean isAsc, F column);

    /**
     * 排序：ORDER BY 字段, ...
     * <p>例: orderBy(true, Arrays.asList("id", "name"))</p>
     *
     * @param condition 执行条件
     * @param isAsc     是否是 ASC 排序
     * @param columns   字段列表
     * @return children
     */
    R orderBy(boolean condition, boolean isAsc, List<F> columns);

    /**
     * 排序：ORDER BY 字段, ...
     */
    R orderBy(boolean condition, boolean isAsc, F column, F... columns);

    default R orderByExpAsc(String orderExp) {
        return orderByExp(orderExp, true);
    }

    default R orderByExpDesc(String orderExp) {
        return orderByExp(orderExp, false);
    }


    default R orderByExp(String orderExp, boolean isAsc) {
        return orderByExp(true, isAsc, orderExp);
    }

    /**
     * 使用函数进行 ORDER BY
     *
     * @param condition
     * @param orderExp
     * @return
     */
    R orderByExp(boolean condition, boolean isAsc, String orderExp);

    /**
     * ignore
     */
    default R having(String sqlHaving, Object... params) {
        return having(true, sqlHaving, params);
    }


    /**
     * HAVING ( sql语句 )
     * <p>例1: having("sum(age) &gt; 10")</p>
     * <p>例2: having("sum(age) &gt; {0}", 10)</p>
     *
     * @param condition 执行条件
     * @param sqlHaving sql 语句
     * @param params    参数数组
     * @return children
     */
    R having(boolean condition, String sqlHaving, Object... params);

    /**
     * ignore
     */
    default R func(Consumer<R> consumer) {
        return func(true, consumer);
    }

    /**
     * 消费函数
     *
     * @param consumer 消费函数
     * @return children
     * @since 3.3.1
     */
    R func(boolean condition, Consumer<R> consumer);
}
