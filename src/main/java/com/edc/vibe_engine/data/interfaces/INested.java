/*
 * Copyright (c) 2011-2022, baomi<PERSON>u (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.interfaces;

import java.io.Serializable;
import java.util.function.Consumer;

/**
 * 查询条件封装
 * <p>嵌套</p>
 * <li>泛型 Param 是具体需要运行函数的类(也是 wrapper 的子类)</li>
 *
 * <AUTHOR> miemie <PERSON>
 * @since 2017-05-26
 */
public interface INested<P, R> extends Serializable {


    /**
     * ignore
     */
    default R or() {
        return or(true);
    }

    /**
     * 拼接 OR
     *
     * @param condition 执行条件
     * @return children
     */
    R or(boolean condition);

    /**
     * ignore
     */
    default R and() {
        return and(true);
    }

    /**
     * 拼接 OR
     *
     * @param condition 执行条件
     * @return children
     */
    R and(boolean condition);

    /**
     * ignore
     */
    default R and(Consumer<P> consumer) {
        return and(true, consumer);
    }

    default R and(P other) {
        return and(true, other);
    }

    /**
     * AND 嵌套
     * <p>
     * 例: and(i -&gt; i.eq("name", "李白").ne("status", "活着"))
     * </p>
     *
     * @param condition 执行条件
     * @param consumer  消费函数
     * @return children
     */
    R and(boolean condition, Consumer<P> consumer);

    R and(boolean condition, P other);

    /**
     * ignore
     */
    default R or(Consumer<P> consumer) {
        return or(true, consumer);
    }

    default R or(P other) {
        return or(true, other);
    }

    /**
     * OR 嵌套
     * <p>
     * 例: or(i -&gt; i.eq("name", "李白").ne("status", "活着"))
     * </p>
     *
     * @param condition 执行条件
     * @param consumer  消费函数
     * @return children
     */
    R or(boolean condition, Consumer<P> consumer);

    R or(boolean condition, P other);

    /**
     * ignore
     */
    default R not(Consumer<P> consumer) {
        return not(true, consumer);
    }

    /**
     * not嵌套
     * <p>
     * 例: not(i -&gt; i.eq("name", "李白").ne("status", "活着"))
     * </p>
     *
     * @param condition 执行条件
     * @param consumer  消费函数
     * @return children
     */
    R not(boolean condition, Consumer<P> consumer);
}
