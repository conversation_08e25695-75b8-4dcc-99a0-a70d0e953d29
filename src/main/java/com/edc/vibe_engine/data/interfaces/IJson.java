package com.edc.vibe_engine.data.interfaces;

import java.util.Collection;

public interface IJson<R, F> {

    R jsonContains(boolean condition, F column, Object val, String path);

    default R jsonContains(boolean condition, F column, Object val) {
        return jsonContains(condition, column, val, null);
    }

    default R jsonContains(F column, Object val, String path) {
        return jsonContains(true, column, val, path);
    }

    default R jsonContains(F column, Object val) {
        return jsonContains(true, column, val);
    }

    R jsonArrayIn(boolean condition, F column, Collection<?> vals, String path);

    default R jsonArrayIn(boolean condition, F column, Collection<?> vals) {
        return jsonArrayIn(condition, column, vals, null);
    }

    default R jsonArrayIn(F column, Collection<?> vals, String path) {
        return jsonArrayIn(true, column, vals, path);
    }

    default R jsonArrayIn(F column, Collection<?> vals) {
        return jsonArrayIn(true, column, vals);
    }

    R jsonArrayNotIn(boolean condition, F column, Collection<?> vals, String path);

    default R jsonArrayNotIn(boolean condition, F column, Collection<?> vals) {
        return jsonArrayNotIn(true, column, vals, null);
    }

    default R jsonArrayNotIn(F column, Collection<?> vals, String path) {
        return jsonArrayNotIn(true, column, vals, path);
    }

    default R jsonArrayNotIn(F column, Collection<?> vals) {
        return jsonArrayNotIn(true, column, vals);
    }
}
