/*
 * Copyright (c) 2011-2022, baomi<PERSON>u (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.interfaces;

import java.io.Serializable;
import java.util.Collection;

/**
 * 查询条件封装
 * <p>比较值</p>
 *
 * <AUTHOR> miemie <PERSON>
 * @since 2017-05-26
 */
public interface ICompare<R, F> extends Serializable {

    /**
     * ignore
     */
    default R eq(F column, Object val) {
        return eq(true, column, val);
    }

    /**
     * 等于 =
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R eq(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R ne(F column, Object val) {
        return ne(true, column, val);
    }

    /**
     * 不等于 &lt;&gt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R ne(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R gt(F column, Object val) {
        return gt(true, column, val);
    }

    /**
     * 大于 &gt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R gt(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R ge(F column, Object val) {
        return ge(true, column, val);
    }

    /**
     * 大于等于 &gt;=
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R ge(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R lt(F column, Object val) {
        return lt(true, column, val);
    }

    /**
     * 小于 &lt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R lt(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R le(F column, Object val) {
        return le(true, column, val);
    }

    /**
     * 小于等于 &lt;=
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R le(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R between(F column, Object val1, Object val2) {
        return between(true, column, val1, val2);
    }

    /**
     * BETWEEN 值1 AND 值2
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val1      值1
     * @param val2      值2
     * @return Compare
     */
    R between(boolean condition, F column, Object val1, Object val2);

    /**
     * ignore
     */
    default R notBetween(F column, Object val1, Object val2) {
        return notBetween(true, column, val1, val2);
    }

    /**
     * NOT BETWEEN 值1 AND 值2
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val1      值1
     * @param val2      值2
     * @return Compare
     */
    R notBetween(boolean condition, F column, Object val1, Object val2);

    /**
     * ignore
     */
    default R like(F column, Object val) {
        return like(true, column, val);
    }

    /**
     * LIKE '%值%'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R like(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R notLike(F column, Object val) {
        return notLike(true, column, val);
    }

    /**
     * NOT LIKE '%值%'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R notLike(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R likeLeft(F column, Object val) {
        return likeLeft(true, column, val);
    }

    /**
     * LIKE '%值'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R likeLeft(boolean condition, F column, Object val);

    /**
     * ignore
     */
    default R likeRight(F column, Object val) {
        return likeRight(true, column, val);
    }

    /**
     * LIKE '值%'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R likeRight(boolean condition, F column, Object val);

    default R regexp(F column, Collection<String> values) {
        return regexp(true, column, values);
    }

    default R regexp(boolean condition, F column, Collection<String> values) {
        final String exps = String.join("|", values);
        return regexp(condition, column, exps);
    }

    default R regexp(F column, String val) {
        return regexp(true, column, val);
    }


    /**
     * 正则匹配 REGEXP '值'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R regexp(boolean condition, F column, String val);


    default R notRegexp(F column, Collection<String> values) {
        return notRegexp(true, column, values);
    }

    default R notRegexp(boolean condition, F column, Collection<String> values) {
        final String exps = String.join("|", values);
        return notRegexp(condition, column, exps);
    }

    default R notRegexp(F column, String val) {
        return notRegexp(true, column, val);
    }


    /**
     * 正则不匹配 NOT REGEXP '值'
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return Compare
     */
    R notRegexp(boolean condition, F column, String val);

    //    default R jsonContains(F column, String val) {
    //        return jsonContains(true, column, val);
    //    }
    //
    //    R jsonContains(boolean condition, F column, String val);
}
