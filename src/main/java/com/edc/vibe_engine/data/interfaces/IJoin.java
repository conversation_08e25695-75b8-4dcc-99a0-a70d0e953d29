/*
 * Copyright (c) 2011-2022, baomi<PERSON>u (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.interfaces;

import java.io.Serializable;

/**
 * 查询条件封装
 * <p>拼接</p>
 *
 * <AUTHOR> miemie <PERSON>
 * @since 2017-05-26
 */
public interface IJoin<R> extends Serializable {

    /**
     * ignore
     */
    default R apply(String applySql, Object... values) {
        return apply(true, applySql, values);
    }

    /**
     * 拼接 sql
     * <p>!! 会有 sql 注入风险 !!</p>
     * <p>例1: apply("id = 1")</p>
     * <p>例2: apply("date_format(dateColumn,'%Y-%m-%d') = '2008-08-08'")</p>
     * <p>例3: apply("date_format(dateColumn,'%Y-%m-%d') = {0}", LocalDate.now())</p>
     *
     * @param condition 执行条件
     * @param values    数据数组
     * @return children
     */
    R apply(boolean condition, String applySql, Object... values);
    //
    //    /**
    //     * ignore
    //     */
    //    default R last(String lastSql) {
    //        return last(true, true, lastSql);
    //    }
    //
    //    default R last(boolean condition, String lastSql) {
    //        return last(condition, true, lastSql);
    //    }
    //
    //    /**
    //     * 无视优化规则直接拼接到 sql 的最后(有sql注入的风险,请谨慎使用)
    //     * <p>例: last("limit 1")</p>
    //     * <p>注意只能调用一次,多次调用以最后一次为准</p>
    //     *
    //     * @param condition 执行条件
    //     * @param replaceExist 多次调用是，是否替换已存在的语句
    //     * @param lastSql   sql语句
    //     * @return children
    //     */
    //    R last(boolean condition, boolean replaceExist, String lastSql);

    /**
     * ignore
     */
    // default Join first(String firstSql) {
    //     return first(true, firstSql);
    // }

    /**
     * sql 起始句（会拼接在SQL语句的起始处）
     *
     * @param condition 执行条件
     * @param firstSql  起始语句
     * @return children
     * @since 3.3.1
     */
    // Join first(boolean condition, String firstSql);

    /**
     * ignore
     */
    default R exists(String existsSql, Object... values) {
        return exists(true, existsSql, values);
    }

    /**
     * 拼接 EXISTS ( sql语句 )
     * <p>!! sql 注入方法 !!</p>
     * <p>例: exists("select id from table where age = 1")</p>
     *
     * @param condition 执行条件
     * @param existsSql sql语句
     * @param values    数据数组
     * @return children
     */
    R exists(boolean condition, String existsSql, Object... values);

    /**
     * ignore
     */
    default R notExists(String existsSql, Object... values) {
        return notExists(true, existsSql, values);
    }

    /**
     * 拼接 NOT EXISTS ( sql语句 )
     * <p>!! sql 注入方法 !!</p>
     * <p>例: notExists("select id from table where age = 1")</p>
     *
     * @param condition 执行条件
     * @param existsSql sql语句
     * @param values    数据数组
     * @return children
     */
    R notExists(boolean condition, String existsSql, Object... values);
}
