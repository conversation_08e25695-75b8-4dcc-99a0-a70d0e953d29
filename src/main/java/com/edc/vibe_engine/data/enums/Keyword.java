/*
 * Copyright (c) 2011-2022, bao<PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.edc.vibe_engine.data.enums;


import com.edc.vibe_engine.common.constants.StringConstants;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;

/**
 * SQL 保留关键字枚举
 */
@AllArgsConstructor
public enum Keyword {

    AND,
    OR,
    NOT,
    IN,
    NOT_IN,
    LIKE,
    NOT_LIKE,
    EQ,
    NE,
    GT,
    GE,
    LT,
    LE,
    IS_NULL,
    IS_NOT_NULL,
    EXISTS,
    NOT_EXISTS,
    BETWEEN,
    NOT_BETWEEN,

    /* 特殊格式，不是实际的 sql 命令*/

    L_LIKE,

    R_LIKE,

    //    /**
    //     * 清单的快速查询条件
    //     */
    //    Q_FILTER,
    //
    //    /**
    //     * 模糊查询条件
    //     */
    //    Q_FUZZY,

    /**
     * 原始 sql
     */
    RAW,

    /**
     * 关联字段查询
     */
    RELATIVE,

    /**
     * 无数据权限
     */
    NO_AUTH,
    /**
     * 正则匹配
     */
    REGEXP,
    /**
     * 正则不匹配
     */
    NOT_REGEXP,


    /**
     * 数组字段，包含入参数组值的任意一项
     */
    IN_ANY,


    /**
     * 数据关联树形特殊操作符，包含树形子级 IN
     */
    SUB_IN,
    /**
     * 数据关联树形特殊操作符，包含树形子级 NOT_IN
     */
    SUB_NOT_IN,

    /**
     * 权限穿透，根据关联对象的权限数据过滤
     */
    PERM_IN,
    /**
     * 关联字段条件
     */
    REF_COL,

    /**
     * 过滤数据有效性
     */
    FILTER_EFFECT,

    /**
     * 公式计算值
     */
    EXP,
    ;

    public static boolean isSymbol(String symbol) {
        return symbol.startsWith(StringConstants.BI_UNDERLINE);
    }

    @JsonCreator
    public static Keyword from(String symbol) {
        if (isSymbol(symbol)) {
            return valueOf(symbol.substring(2));
        }
        return null;
    }

    /**
     * @return 转为系统字符
     */
    @JsonValue
    public String asSymbol() {
        return StringConstants.BI_UNDERLINE + this.name();
    }

    public boolean isAndOrOr() {
        return this == AND || this == OR;
    }

    public boolean isNested() {
        return this == AND || this == OR || this == NOT;
    }

    public boolean isMulti() {
        return this == IN || this == NOT_IN || this == IN_ANY
               || this == BETWEEN || this == NOT_BETWEEN
               || this == SUB_IN || this == SUB_NOT_IN
                /*|| this == DEPT_LABEL_IN*/;
    }
}
