package com.edc.vibe_engine.data.support;

import com.edc.vibe_engine.json.JSON;
import org.postgresql.util.PGobject;
import org.springframework.jdbc.support.JdbcUtils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.function.BiConsumer;

public class RowMapperUtils {


    public static void forEachColumn(ResultSet rs, BiConsumer<String, Object> kvConsumer) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        for (int i = 1; i <= columnCount; i++) {
            final String column = JdbcUtils.lookupColumnName(metaData, i);
            final Object value = JdbcUtils.getResultSetValue(rs, i);
            final String columnTypeName = metaData.getColumnTypeName(i);

            final Object result;
            if (isJsonColumnType(columnTypeName) && value != null) {
                if (value instanceof String) {
                    result = JSON.parse(((String) value));
                } else if (value instanceof PGobject obj) {
                    result = JSON.parse(obj.getValue());
                } else {
                    result = JSON.toGeneric(value);
                }
            } else {
                result = value;
            }
            kvConsumer.accept(column, result);
        }
    }

    public static boolean isJsonColumnType(String columnTypeName) {
        return columnTypeName.equalsIgnoreCase("JSON") || columnTypeName.equalsIgnoreCase("JSONB");
    }
}
