//package com.edc.vibe_engine.json.response;
//
//import com.exe.cloud.common.response.Response;
//import com.exe.cloud.json.utils.JsonPathUtils;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.jayway.jsonpath.DocumentContext;
//import com.jayway.jsonpath.TypeRef;
//import lombok.extern.slf4j.Slf4j;
//
//import java.lang.reflect.Type;
//
//@Slf4j
//public class JsonResponse {
//
//    public static final TypeReference<Response<Object>> RESPONSE_TYPE = new TypeReference<Response<Object>>() {
//    };
//
//    private final DocumentContext context;
//
//    public JsonResponse(String json) {
//        context = JsonPathUtils.parse(json);
//    }
//
//    public String getStatus() {
//        return extract("status");
//    }
//
//    public String getMsg() {
//        return extract("msg");
//    }
//
//    public boolean isSuccess() {
//        return extract("success");
//    }
//
//    /**
//     * Extract values given a JsonPath and deserialize into the given class.
//     */
//    public <T> T as(Class<T> clazz) {
//        return extract("$", clazz);
//    }
//
//    public <T> T as(TypeReference<T> typeRef) {
//        return extract("$", typeRef);
//    }
//
//    public <T> T extract(String path) {
//        try {
//            return context.read(path);
//        } catch (Exception ex) {
//            log.warn("Error extracting path '{}' from data", path);
//            throw ex;
//        }
//    }
//
//    /**
//     * Extract values given a JsonPath and deserialize into the given TypeRef.
//     * Use this for Lists of a specific type.
//     */
//    public <T> T extract(String path, Class<T> clazz) {
//        try {
//            return context.read(path, clazz);
//        } catch (Exception ex) {
//            log.warn(String.format("Error extracting path '%s' from data", path));
//            throw ex;
//        }
//    }
//
//    public <T> T extract(String path, TypeReference<T> typeRef) {
//        try {
//            return context.read(path, new TypeRef<T>() {
//                @Override
//                public Type getType() {
//                    return typeRef.getType();
//                }
//            });
//        } catch (Exception ex) {
//            log.warn(String.format("Error extracting path '%s' from data", path));
//            throw ex;
//        }
//    }
//
//    public <T> T extractData(Class<T> clazz) {
//        return extract("$.data", clazz);
//    }
//
//    public <T> T extractData(TypeReference<T> typeRef) {
//        return extract("$.data", typeRef);
//    }
//
//    public Response<Object> asResponse() {
//        return as(RESPONSE_TYPE);
//    }
//}
