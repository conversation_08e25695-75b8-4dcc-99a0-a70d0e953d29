//package com.edc.vibe_engine.json.utils;
//
//
//import com.exe.cloud.json.ObjectMapperBuilder;
//import com.jayway.jsonpath.Configuration;
//import com.jayway.jsonpath.DocumentContext;
//import com.jayway.jsonpath.JsonPath;
//import com.jayway.jsonpath.Option;
//import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
//import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
//import lombok.experimental.UtilityClass;
//
//@UtilityClass
//public class JsonPathUtils {
//    public static final Configuration jsonPathConfig = Configuration.builder()
//            .jsonProvider(new JacksonJsonProvider(ObjectMapperBuilder.getObjectMapper()))
//            .mappingProvider(new JacksonMappingProvider(ObjectMapperBuilder.getObjectMapper())).build()
//            .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);
//
//
//    public static DocumentContext parse(String json) {
//        return JsonPath.using(JsonPathUtils.jsonPathConfig).parse(json);
//    }
//
//    public static DocumentContext parse(Object json) {
//        return JsonPath.using(JsonPathUtils.jsonPathConfig).parse(json);
//    }
//
//
//}
