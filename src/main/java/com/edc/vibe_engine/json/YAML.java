package com.edc.vibe_engine.json;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator;

import java.util.Map;

public class YAML {


    private final static ObjectMapper YAML_OBJECT_MAPPER = new ObjectMapper(new YAMLFactory()
            .disable(YAMLGenerator.Feature.WRITE_DOC_START_MARKER)
            .enable(YAMLGenerator.Feature.MINIMIZE_QUOTES)
    );


    public static String toYaml(Object yamlObj) {
        return JSON.toJSON(YAML_OBJECT_MAPPER, yamlObj);
    }

    public static Map<String, Object> toMap(String yamlContent) {
        return JSON.toJavaObject(YAML_OBJECT_MAPPER, yamlContent, JSON.MAP_TYPE);
    }

    public static <T> T toJavaObject(String yamlContent, Class<T> exceptClass) {
        return JSON.toJavaObject(YAML_OBJECT_MAPPER, yamlContent, exceptClass);
    }

    public static <T> T toJavaObject(String yamlContent, TypeReference<T> genericType) {
        return JSON.toJavaObject(YAML_OBJECT_MAPPER, yamlContent, genericType);
    }
}
