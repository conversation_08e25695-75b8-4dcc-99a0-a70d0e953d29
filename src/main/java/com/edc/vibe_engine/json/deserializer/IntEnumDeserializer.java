package com.edc.vibe_engine.json.deserializer;

import com.edc.vibe_engine.common.interfaces.IntEnum;
import com.edc.vibe_engine.common.support.EnumMapperUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serial;

public class IntEnumDeserializer extends StdDeserializer<IntEnum> implements ContextualDeserializer {

    @Serial
    private static final long serialVersionUID = 5944121494662723089L;

    public static final IntEnumDeserializer INSTANCE = new IntEnumDeserializer();

    public IntEnumDeserializer() {
        this(null);
    }

    public IntEnumDeserializer(JavaType valueType) {
        super(valueType);
    }

    @Override
    public IntEnum deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String data = jsonParser.getText();
        if (StringUtils.isEmpty(data))
            return null;
        Class<?> enumClass = handledType();
        return (IntEnum) EnumMapperUtils.getEnumValue(enumClass, data);
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        //beanProperty is null when the type to deserialize is the top-level type or a generic type, not a type of a bean property
        JavaType type = ctxt.getContextualType() != null
                ? ctxt.getContextualType() : property.getMember().getType();
        return new IntEnumDeserializer(type);
    }
}
