package com.edc.vibe_engine.json.deserializer;

import com.edc.vibe_engine.json.JSON;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serial;
import java.util.List;

/**
 * 支持逗号分割字符串转换为列表
 */
public class CommaArrayDeserializer extends StdDeserializer<List<?>> implements ContextualDeserializer {

    @Serial
    private static final long serialVersionUID = 5944121494662723089L;

    public static final CommaArrayDeserializer INSTANCE = new CommaArrayDeserializer();

    public CommaArrayDeserializer() {
        this(null);
    }

    public CommaArrayDeserializer(JavaType valueType) {
        super(valueType);
    }

    @Override
    public List<?> deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        if (node.isNull()) {
            return null;
        }
        final JavaType valueType = getValueType(deserializationContext);
        if (node.isTextual()) {
            final String textValue = node.textValue();
            return JSON.convertObject(StringUtils.split(textValue, ","), valueType);
        }
        return JSON.getObjectMapper().treeToValue(node, valueType);
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        //beanProperty is null when the type to deserialize is the top-level type or a generic type, not a type of a bean property
        JavaType type = ctxt.getContextualType() != null
                ? ctxt.getContextualType() : property.getMember().getType();
        return new CommaArrayDeserializer(type);
    }
}
