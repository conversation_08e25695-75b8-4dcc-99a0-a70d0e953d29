package com.edc.vibe_engine.json.deserializer;

import com.edc.vibe_engine.json.JSON;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.io.Serial;


public class JsonStringToObjectDeserializer extends StdDeserializer<Object> {


    @Serial
    private static final long serialVersionUID = -6991462639575492681L;

    public static final JsonStringToObjectDeserializer INSTANCE = new JsonStringToObjectDeserializer();

    public JsonStringToObjectDeserializer() {
        this(null);
    }

    public JsonStringToObjectDeserializer(JavaType valueType) {
        super(valueType);
    }

    @Override
    public Object deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        if (node.isTextual()) {
            final String json = node.textValue();
            if (JSON.mayBeJson(json)) {
                return JSON.parse(json);
            }
        }
        return JSON.convertObject(node, Object.class);
    }
}
