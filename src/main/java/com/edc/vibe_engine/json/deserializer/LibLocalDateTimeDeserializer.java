package com.edc.vibe_engine.json.deserializer;

import com.edc.vibe_engine.common.support.DateUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.io.Serial;
import java.time.LocalDateTime;

public class LibLocalDateTimeDeserializer extends StdDeserializer<LocalDateTime> {
    @Serial
    private static final long serialVersionUID = 8434458443798107830L;

    public static final LibLocalDateTimeDeserializer INSTANCE = new LibLocalDateTimeDeserializer();

    public LibLocalDateTimeDeserializer() {
        super(LocalDateTime.class);
    }

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String date = p.getText();
        return DateUtils.parseDateTime(date);
    }
}
