package com.edc.vibe_engine.json.deserializer;

import com.edc.vibe_engine.common.support.DateUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;
import java.io.Serial;
import java.time.LocalDate;

public class LibLocalDateDeserializer extends StdDeserializer<LocalDate> {
    @Serial
    private static final long serialVersionUID = 8434458443798107830L;

    public static final LibLocalDateDeserializer INSTANCE = new LibLocalDateDeserializer();

    public LibLocalDateDeserializer() {
        super(LocalDate.class);
    }

    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String date = p.getText();
        return DateUtils.parseDate(date);
    }
}
