//package com.edc.vibe_engine.json.type;
//
//import com.exe.cloud.common.util.ClassUtils;
//import com.fasterxml.jackson.databind.DeserializationConfig;
//import com.fasterxml.jackson.databind.JavaType;
//import com.fasterxml.jackson.databind.module.SimpleAbstractTypeResolver;
//
//import java.util.Optional;
//
//public class ImplClassTypeResolver extends SimpleAbstractTypeResolver {
//
//    private static final long serialVersionUID = -4655142346841443179L;
//
//    @Override
//    public JavaType findTypeMapping(DeserializationConfig config, JavaType type) {
//        if (type.isContainerType()) {
//            return null;
//        }
//        Optional<Class<?>> dst = ClassUtils.findImplClass(type.getRawClass());
//        return dst.map(aClass -> config.getTypeFactory().constructSpecializedType(type, aClass)).orElse(null);
//    }
//}
