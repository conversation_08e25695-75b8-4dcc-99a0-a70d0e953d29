package com.edc.vibe_engine.json.serializer;

import com.edc.vibe_engine.common.support.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;
import java.time.LocalTime;

public class LibLocalTimeSerializer extends StdSerializer<LocalTime> {

    @Serial
    private static final long serialVersionUID = -4351285687235779173L;

    public static final LibLocalTimeSerializer INSTANCE = new LibLocalTimeSerializer();


    public LibLocalTimeSerializer() {
        super(LocalTime.class);
    }

    @Override
    public void serialize(LocalTime value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(DateUtils.formatTime(value));
    }
}
