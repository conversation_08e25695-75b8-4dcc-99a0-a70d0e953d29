package com.edc.vibe_engine.json.serializer;

import com.edc.vibe_engine.common.interfaces.IntEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;

public class IntEnumSerializer extends StdSerializer<IntEnum> {

    @Serial
    private static final long serialVersionUID = 3132396943860707145L;

    public static final IntEnumSerializer INSTANCE = new IntEnumSerializer();

    public IntEnumSerializer() {
        this(null);
    }

    protected IntEnumSerializer(Class<IntEnum> t) {
        super(t);
    }

    @Override
    public void serialize(IntEnum e, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (e == null) {
            jsonGenerator.writeNull();
        } else {
            jsonGenerator.writeNumber(e.value());
        }

    }
}
