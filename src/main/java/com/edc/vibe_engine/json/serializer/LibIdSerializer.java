package com.edc.vibe_engine.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;

public class LibIdSerializer extends StdSerializer<Long> {

    @Serial
    private static final long serialVersionUID = 3132396943860707145L;

    public static final LibIdSerializer INSTANCE = new LibIdSerializer();

    public static final long MAX_LONG_TO_STRING = (long) Math.pow(10, 15) * 8;

    public LibIdSerializer() {
        super(Long.class);
    }

    @Override
    public void serialize(Long l, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (l == null) {
            jsonGenerator.writeNull();
            return;
        }
        if (l > MAX_LONG_TO_STRING) {
            jsonGenerator.writeString(l.toString());
        } else {
            jsonGenerator.writeNumber(l);
        }
    }
}
