package com.edc.vibe_engine.json.serializer;

import com.edc.vibe_engine.common.support.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;
import java.time.LocalDate;

public class LibLocalDateSerializer extends StdSerializer<LocalDate> {

    @Serial
    private static final long serialVersionUID = -4351285687235779173L;

    public static final LibLocalDateSerializer INSTANCE = new LibLocalDateSerializer();

    public LibLocalDateSerializer() {
        super(LocalDate.class);
    }

    @Override
    public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(DateUtils.formatDate(value));
    }
}
