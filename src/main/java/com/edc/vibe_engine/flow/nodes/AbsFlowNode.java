package com.edc.vibe_engine.flow.nodes;

import com.edc.vibe_engine.flow.interfaces.IFlowContext;
import com.edc.vibe_engine.flow.interfaces.IFlowNode;

public abstract class AbsFlowNode<T> implements IFlowNode<T> {
    @Override
    public T process(IFlowContext context) throws Exception {
        T result = null;
        try {
            preProcess(context);
            result = doProcess(context);
            postProcess(context, result);
            return result;
        } catch (Exception e) {
            return onException(context, result, e);
        } finally {
            onFinally(context, result);
        }
    }

    protected void onFinally(IFlowContext context, T result) {

    }

    protected T onException(IFlowContext context, T result, Exception e) throws Exception {
        throw e;
    }


    protected void preProcess(IFlowContext context) {

    }

    protected abstract T doProcess(IFlowContext context);

    protected void postProcess(IFlowContext context, T result) {

    }
}
