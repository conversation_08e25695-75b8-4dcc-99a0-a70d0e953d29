package com.edc.vibe_engine.demo.entity;

import com.edc.vibe_engine.meta.annotation.Entity;
import com.edc.vibe_engine.meta.annotation.Field;
import com.edc.vibe_engine.meta.annotation.MetaSource;
import com.edc.vibe_engine.meta.annotation.Relation;
import com.edc.vibe_engine.meta.enums.EntityType;
import com.edc.vibe_engine.meta.enums.FieldFlag;
import com.edc.vibe_engine.meta.enums.RelationType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品实体示例 - 演示双引擎驱动的低代码平台功能
 */
@Data
@Entity(
        module = "demo",
        name = "product",
        displayName = "产品",
        type = EntityType.TABLE
)
@MetaSource(
        source = MetaSource.SourceType.ANNOTATION,
        priority = 100,
        overridable = true
)
public class Product {

    @Field(
            name = "name",
            displayName = "产品名称",
            size = 100,
            flags = {FieldFlag.REQUIRED},
            validations = {
                    @Field.Validation(
                            type = "required",
                            message = "产品名称不能为空"
                    ),
                    @Field.Validation(
                            type = "max",
                            params = "{\"max\": 100}",
                            message = "产品名称不能超过100个字符"
                    )
            },
            uiControl = @Field.UIControl(
                    type = "input",
                    placeholder = "请输入产品名称",
                    helpText = "产品的显示名称，用于前台展示",
                    group = "基本信息",
                    order = 1
            )
    )
    private String name;

    @Field(
            name = "code",
            displayName = "产品编码",
            size = 50,
            flags = {FieldFlag.REQUIRED, FieldFlag.UNIQUE},
            validations = {
                    @Field.Validation(
                            type = "required",
                            message = "产品编码不能为空"
                    ),
                    @Field.Validation(
                            type = "pattern",
                            params = "{\"pattern\": \"^[A-Z0-9]{6,20}$\"}",
                            message = "产品编码必须是6-20位大写字母和数字组合"
                    )
            },
            uiControl = @Field.UIControl(
                    type = "input",
                    placeholder = "请输入产品编码",
                    helpText = "产品的唯一标识码",
                    group = "基本信息",
                    order = 2
            )
    )
    private String code;

    @Field(
            name = "description",
            displayName = "产品描述",
            size = 1000,
            uiControl = @Field.UIControl(
                    type = "textarea",
                    props = "{\"rows\": 4}",
                    placeholder = "请输入产品描述",
                    group = "基本信息",
                    order = 3
            )
    )
    private String description;

    @Field(
            name = "price",
            displayName = "价格",
            digits = 2,
            flags = {FieldFlag.REQUIRED},
            validations = {
                    @Field.Validation(
                            type = "required",
                            message = "价格不能为空"
                    ),
                    @Field.Validation(
                            type = "min",
                            params = "{\"min\": 0}",
                            message = "价格不能小于0"
                    )
            },
            uiControl = @Field.UIControl(
                    type = "number",
                    props = "{\"precision\": 2, \"min\": 0}",
                    placeholder = "请输入价格",
                    group = "价格信息",
                    order = 1
            )
    )
    private BigDecimal price;

    @Field(
            name = "category_id",
            displayName = "产品分类",
            flags = {FieldFlag.REQUIRED},
            validations = {
                    @Field.Validation(
                            type = "required",
                            message = "请选择产品分类"
                    )
            },
            uiControl = @Field.UIControl(
                    type = "select",
                    props = "{\"allowClear\": true}",
                    placeholder = "请选择产品分类",
                    group = "分类信息",
                    order = 1
            )
    )
    private String categoryId;

    @Relation(
            name = "category",
            displayName = "产品分类",
            entity = "category",
            module = "demo",
            type = RelationType.REF_ONE,
            refField = "category_id",
            joinMapping = {
                    @Relation.JoinMapping(
                            field = "_id",
                            valExp = "self.category_id"
                    )
            }
    )
    private Object category;

    @Field(
            name = "status",
            displayName = "状态",
            flags = {FieldFlag.REQUIRED},
            options = {
                    @Field.Option(label = "草稿", value = "draft"),
                    @Field.Option(label = "上架", value = "active"),
                    @Field.Option(label = "下架", value = "inactive"),
                    @Field.Option(label = "停产", value = "discontinued")
            },
            validations = {
                    @Field.Validation(
                            type = "required",
                            message = "请选择产品状态"
                    )
            },
            uiControl = @Field.UIControl(
                    type = "select",
                    placeholder = "请选择产品状态",
                    group = "状态信息",
                    order = 1
            )
    )
    private String status;

    @Field(
            name = "is_featured",
            displayName = "是否推荐",
            uiControl = @Field.UIControl(
                    type = "switch",
                    helpText = "推荐产品将在首页展示",
                    group = "状态信息",
                    order = 2
            )
    )
    private Boolean isFeatured;

    @Field(
            name = "created_at",
            displayName = "创建时间",
            flags = {FieldFlag.READONLY},
            uiControl = @Field.UIControl(
                    type = "datetime",
                    readonly = true,
                    group = "系统信息",
                    order = 1
            )
    )
    private LocalDateTime createdAt;

    @Field(
            name = "updated_at",
            displayName = "更新时间",
            flags = {FieldFlag.READONLY},
            uiControl = @Field.UIControl(
                    type = "datetime",
                    readonly = true,
                    group = "系统信息",
                    order = 2
            )
    )
    private LocalDateTime updatedAt;
}
