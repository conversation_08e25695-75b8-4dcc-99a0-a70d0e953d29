// 测试实体元数据
export default {
    metadata: {
        name: 'test_entity',
        display_name: '测试实体',
        module: 'test',
        description: '这是一个用于测试的实体',
        fields: [
            {
                name: '_id',
                display_name: 'ID',
                type: 'VARCHAR',
                default_value: null,
                flags: ['PRIMARY_KEY'],
            },
            {
                name: 'name',
                display_name: '名称',
                type: 'VARCHAR',
                default_value: null,
                flags: ['KEY_INFO'],
            },
            {
                name: 'code',
                display_name: '编码',
                type: 'VARCHAR',
                default_value: null,
                flags: ['KEY_INFO'],
            },
            {
                name: 'status',
                display_name: '状态',
                type: 'VARCHAR',
                default_value: 'active',
                flags: [],
            },
            {
                name: 'parent_id',
                display_name: '父级ID',
                type: 'VARCHAR',
                default_value: null,
                flags: ['NULLABLE'],
            },
            {
                name: 'description',
                display_name: '描述',
                type: 'TEXT',
                default_value: null,
                flags: ['NULLABLE'],
            },
            {
                name: '_create_by',
                display_name: '创建人',
                type: 'VARCHAR',
                default_value: null,
                flags: ['NULLABLE'],
            },
            {
                name: '_create_at',
                display_name: '创建时间',
                type: 'DATE_TIME',
                default_value: null,
                flags: [],
            },
            {
                name: '_update_by',
                display_name: '修改人',
                type: 'VARCHAR',
                default_value: null,
                flags: ['NULLABLE'],
            },
            {
                name: '_update_at',
                display_name: '修改时间',
                type: 'DATE_TIME',
                default_value: null,
                flags: [],
            },
        ],
        relations: [
            {
                name: 'parent',
                display_name: '父级',
                field: 'parent_id',
                entity: 'test_entity',
                module: 'test',
                type: 'ONE',
            },
            {
                name: 'children',
                display_name: '子级',
                field: 'parent_id',
                entity: 'test_entity',
                module: 'test',
                type: 'MANY',
                reverse: true,
            },
            {
                name: '_creator',
                display_name: '创建人',
                field: '_create_by',
                entity: 'user',
                module: 'base',
                type: 'ONE',
            },
            {
                name: '_updater',
                display_name: '修改人',
                field: '_update_by',
                entity: 'user',
                module: 'base',
                type: 'ONE',
            },
        ],
        rules: [
            {
                fields: ['code'],
                point: '',
            },
        ],
        indexes: [
            {
                name: 'idx_test_entity_code',
                fields: ['code'],
            },
            {
                name: 'idx_test_entity_name',
                fields: ['name'],
            },
            {
                name: 'idx_test_entity_status',
                fields: ['status'],
            },
            {
                name: 'idx_test_entity_parent_id',
                fields: ['parent_id'],
            },
        ],
    },
}
