package com.edc.vibe_engine.data.sql;

import com.edc.vibe_engine.data.sql.interfaces.ISqlExecutable;
import com.edc.vibe_engine.data.sql.interfaces.ISqlPart;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.text.IsEqualIgnoringCase.equalToIgnoringCase;

public class BaseSqlTest {
    protected void logSqlWhere(String explain, ISqlPart wrapper, String targetSql) {
        String targetSqlStr = targetSql.replaceAll("\\s+", " ").replaceAll(", ", ",");
        String sqlSegment = wrapper.getPart().replaceAll("\\s+", " ").replaceAll(", ", ",");
        System.out.printf(" ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓   ->(%s)<-   ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓%n", explain);
        System.out.println(sqlSegment);
        assertThat(sqlSegment.trim(), equalToIgnoringCase(targetSqlStr));
    }

    protected void logSqlExecutable(String explain, ISqlExecutable executable, String targetSql) {
        String targetSqlStr = targetSql.replaceAll("\\s+", " ").replaceAll(", ", ",");
        String sqlSegment = executable.getSql().replaceAll("\\s+", " ").replaceAll(", ", ",");
        System.out.printf(" ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓   ->(%s)<-   ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓%n", explain);
        System.out.println(executable.getSql());
        assertThat(sqlSegment.trim(), equalToIgnoringCase(targetSqlStr));
    }
}
