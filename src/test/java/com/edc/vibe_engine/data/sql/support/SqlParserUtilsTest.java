package com.edc.vibe_engine.data.sql.support;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.statement.select.Select;
import org.junit.jupiter.api.Test;

class SqlParserUtilsTest {

    @Test
    void parse() throws JSQLParserException {
        String sql = """
                SELECT
                  fulltext,
                  1 - ("vector" <=> '[1.2, 0.8]') AS cosine_similarity
                FROM
                  p_cjh_vector
                ORDER BY
                  cosine_similarity DESC
                """;
        Select select = SqlParserUtils.parse(sql);
        System.out.println(select);
    }
}
