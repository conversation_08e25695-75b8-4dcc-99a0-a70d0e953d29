package com.edc.vibe_engine.data.sql;

import com.edc.vibe_engine.data.sql.block.InsertBlock;
import com.edc.vibe_engine.data.sql.block.UpdateBlock;
import com.edc.vibe_engine.data.sql.block.UpsertBlock;
import com.edc.vibe_engine.data.sql.condition.Condition;
import com.edc.vibe_engine.data.sql.enums.SqlDialect;
import com.edc.vibe_engine.data.sql.support.SqlDialectHelper;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class SqlExecutableTest {

    @BeforeAll
    public static void set() {
        SqlDialectHelper.setGlobal(SqlDialect.MYSQL);
    }

    @Test
    public void testCreateSelectWithAsterisk() {
        Condition condition = new Condition();

        SqlExecutable sqlExecutable = SqlExecutable.createSelect("test_table", condition);

        assertEquals("SELECT * FROM `test_table`", sqlExecutable.getSql());
        assertEquals(Collections.emptyList(), sqlExecutable.getParams());
    }

    @Test
    public void testCreateSelectWithFields() {
        Condition condition = new Condition();

        SqlExecutable sqlExecutable = SqlExecutable.createSelect("test_table", Arrays.asList("field1", "field2"), condition);

        assertEquals("SELECT `field1`,`field2` FROM `test_table`", sqlExecutable.getSql());
        assertEquals(Collections.emptyList(), sqlExecutable.getParams());
    }

    @Test
    public void testCreateInsertWithBlock() {
        InsertBlock insertBlock = new InsertBlock();
        insertBlock.set("field1", "value1")
                .set("field2", "value2");

        SqlExecutable sqlExecutable = SqlExecutable.createInsert("test_table", insertBlock);

        assertEquals("INSERT INTO `test_table` (`field1`,`field2`) VALUES (?,?)", sqlExecutable.getSql());
        assertEquals(List.of("value1", "value2"), sqlExecutable.getParams());
    }

    @Test
    public void testCreateUpdateWithBlock() {
        UpdateBlock updateBlock = new UpdateBlock();
        updateBlock.set("field1", "value1")
                .set("field2", "value2");

        Condition condition = new Condition();

        SqlExecutable sqlExecutable = SqlExecutable.createUpdate("test_table", condition, updateBlock);

        assertEquals("UPDATE `test_table` SET `field1` = ?,`field2` = ?", sqlExecutable.getSql());
        assertEquals(List.of("value1", "value2"), sqlExecutable.getParams());
    }

    @Test
    public void testCreateUpsertWithBlock() {
        UpsertBlock upsertBlock = new UpsertBlock();
        upsertBlock.set("field1", "value1")
                .set("field2", "value2");

        SqlExecutable sqlExecutable = SqlExecutable.createUpsert("test_table", upsertBlock);

        assertEquals("INSERT INTO `test_table` (`field1`,`field2`) values (?,?) ON DUPLICATE KEY UPDATE `field1`=VALUES(`field1`),`field2`=VALUES(`field2`)", sqlExecutable.getSql());
        assertEquals(List.of("value1", "value2"), sqlExecutable.getParams());
    }

    @Test
    public void testCreateDelete() {
        Condition condition = new Condition();

        SqlExecutable sqlExecutable = SqlExecutable.createDelete("test_table", condition);

        assertEquals("DELETE FROM `test_table`", sqlExecutable.getSql());
        assertEquals(Collections.emptyList(), sqlExecutable.getParams());
    }
}

