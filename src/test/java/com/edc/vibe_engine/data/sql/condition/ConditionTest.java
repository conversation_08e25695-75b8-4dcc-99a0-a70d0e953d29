package com.edc.vibe_engine.data.sql.condition;

import com.edc.vibe_engine.data.sql.BaseSqlTest;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Collections;

class ConditionTest extends BaseSqlTest {

    @Test
    void testCacheSqlSegment() {
        Condition ew = new Condition()
                .eq("xxx", 123)
                .and(i -> i.eq("andx", 65444).le("ande", 66666))
                .ne("xxx", 222);
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ?)");
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ?)");
        ew.gt("x22", 333);
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ? AND `x22` > ?)");
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ? AND `x22` > ?)");
        ew.orderByAsc("column");
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ? AND `x22` > ?) ORDER BY `column` ASC");
        logSqlWhere("xx", ew, "WHERE (`xxx` = ? AND (`andx` = ? AND `ande` <= ?) AND `xxx` <> ? AND `x22` > ?) ORDER BY `column` ASC");
    }

    @Test
    void testQueryWrapper() {
        logSqlWhere("去除第一个 or,以及自动拼接 and,以及手动拼接 or,以及去除最后的多个or", new Condition().or()
                        .ge("age", 3).or().ge("age", 3).ge("age", 3).or().or().or().or(),
                "WHERE (`age` >= ? OR `age` >= ? AND `age` >= ?)");

        logSqlWhere("多个 or 相连接,去除多余的 or", new Condition()
                        .ge("age", 3).or().or().or().ge("age", 3).or().or().ge("age", 3),
                "WHERE (`age` >= ? OR `age` >= ? OR `age` >= ?)");

        logSqlWhere("多个 and 相连接,去除多余的 and", new Condition()
                        .ge("age", 3).and().and().and().ge("age", 3).and().and().ge("age", 3),
                "WHERE (`age` >= ? AND `age` >= ? AND `age` >= ?)");

        //        logSqlWhere("嵌套,正常嵌套", new Condition()
        //                        .nested(i -> i.eq("id", 1)).eq("id", 1),
        //                "((`id` = ?) AND `id` = ?)");

        logSqlWhere("嵌套,第一个套外的 and 自动消除", new Condition()
                        .and(i -> i.eq("id", 1)).eq("id", 1),
                "WHERE ((`id` = ?) AND `id` = ?)");

        logSqlWhere("嵌套,多层嵌套", new Condition()
                        .and(i -> i.eq("id", 1).and(j -> j.eq("id", 2))),
                "WHERE (`id` = ? AND (`id` = ?))");

        logSqlWhere("嵌套,第一个套外的 or 自动消除", new Condition()
                        .or(i -> i.eq("id", 1)).eq("id", 1),
                "WHERE ((`id` = ?) AND `id` = ?)");

        logSqlWhere("嵌套,套内外自动拼接 and", new Condition()
                        .eq("id", 11).and(i -> i.eq("id", 1)).eq("id", 1),
                "WHERE (`id` = ? AND (`id` = ?) AND `id` = ?)");

        logSqlWhere("嵌套,套内外手动拼接 or,去除套内第一个 or", new Condition()
                        .eq("id", 11).or(i -> i.or().eq("id", 1)).or().eq("id", 1),
                "WHERE (`id` = ? OR (`id` = ?) OR `id` = ?)");

        logSqlWhere("多个 order by 和 group by 拼接,自动优化顺序,last方法拼接在最后", new Condition()
                        .eq("id", 11)
                        //                        .last("limit 1")
                        .orderByAsc("id", "name", "sex").orderByDesc("age", "txl")
                        .groupBy("id", "name", "sex").groupBy("id", "name"),
                "WHERE (`id` = ?) GROUP BY `id`,`name`,`sex`,`id`,`name` ORDER BY `id` ASC,`name` ASC,`sex` ASC,`age` DESC,`txl` DESC");

        logSqlWhere("只存在 order by", new Condition()
                        .orderByAsc("id", "name", "sex").orderByDesc("age", "txl"),
                "ORDER BY `id` ASC,`name` ASC,`sex` ASC,`age` DESC,`txl` DESC");

        logSqlWhere("只存在 group by", new Condition()
                        .groupBy("id", "name", "sex").groupBy("id", "name"),
                "GROUP BY `id`,`name`,`sex`,`id`,`name`");
    }

    @Test
    void testJoin() {
        Condition queryWrapper = new Condition()
                //                .last("limit 1")
                .or()
                .apply("date_format(column,'%Y-%m-%d') = '2008-08-08'")
                .apply("date_format(column,'%Y-%m-%d') = ?", LocalDate.now())
                .or().exists("select id from table where age = 1")
                .or().notExists("select id from table where age = 1");
        logSqlWhere("测试 Join 下的方法", queryWrapper, "WHERE (date_format(column,'%Y-%m-%d') = '2008-08-08' AND date_format(column,'%Y-%m-%d') = ? OR EXISTS (select id from table where age = 1) OR NOT EXISTS (select id from table where age = 1))");
    }

    // @Test
    // void testNested() {
    //     Condition queryWrapper = new Condition()
    //             .and(i -> i.eq("id", 1).nested(j -> j.ne("id", 2)))
    //             .or(i -> i.eq("id", 1).and(j -> j.ne("id", 2)))
    //             .nested(i -> i.eq("id", 1).or(j -> j.ne("id", 2)))
    //             .not(i -> i.eq("id", 1).or(j -> j.ne("id", 2)));
    //     logSqlWhere("测试 Nested 下的方法", queryWrapper, "((id = ? AND (id <> ?)) OR (id = ? AND (id <> ?)) AND (id = ? OR (id <> ?)) AND NOT (id = ? OR (id <> ?)))");
    // }

    // @Test
    // void testPluralLambda() {
    //     TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), Entity.class);
    //     Condition queryWrapper = new QueryWrapper<>();
    //     queryWrapper.lambda().eq(Entity::getName, "sss");
    //     queryWrapper.lambda().eq(Entity::getName, "sss2");
    //     logSqlWhere("测试 PluralLambda", queryWrapper, "(username = ? AND username = ?)");
    //     logParams(queryWrapper);
    // }

    @Test
    void testInEmptyColl() {
        Condition queryWrapper = new Condition().in("xxx", Collections.emptyList());
        logSqlWhere("测试 empty 的 coll", queryWrapper, "WHERE (`xxx` IN ())");
    }

    @Test
    void testExistsValue() {
        Condition wrapper = new Condition().eq("a", "b")
                .exists("select 1 from xxx where id = ? and name = ?", 1, "Bob");
        logSqlWhere("testExistsValue", wrapper, "WHERE (`a` = ? AND EXISTS (select 1 from xxx where id = ? and name = ?))");
        wrapper = new Condition().eq("a", "b")
                .notExists("select 1 from xxx where id = ? and name = ?", 1, "Bob");
        logSqlWhere("testNotExistsValue", wrapper, "WHERE (`a` = ? AND NOT EXISTS (select 1 from xxx where id = ? and name = ?))");
    }

}
