package com.edc.vibe_engine.data.schema;

import com.edc.vibe_engine.data.connection.ConnectionSupplier;
import com.edc.vibe_engine.data.schema.enums.DBFieldType;
import com.edc.vibe_engine.data.schema.types.DBField;
import com.edc.vibe_engine.data.schema.types.DBIndex;
import com.edc.vibe_engine.data.schema.types.DBTable;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.testcontainers.containers.PostgreSQLContainer;

class PgSqlSchemaManagerTest {

    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(
            "postgres:17-alpine"
    );


    @BeforeAll
    static void beforeAll() {
        postgres.start();
    }

    @AfterAll
    static void afterAll() {
        postgres.stop();
    }

    private PgSqlSchemaManager pgSqlSchemaManager;

    @BeforeEach
    void setUp() {
        //        JdbcConfig jdbcConfig = new JdbcConfig(SqlDialect.PGSQL, postgres.getHost(), postgres.getMappedPort(POSTGRESQL_PORT))
        //                .auth(postgres.getUsername(), postgres.getPassword())
        //                .schema(postgres.getDatabaseName(), "");

        pgSqlSchemaManager = new PgSqlSchemaManager(new ConnectionSupplier(postgres.getJdbcUrl(), postgres.getUsername(), postgres.getPassword()));
    }

    @Test
    void test_table_without_primaryKey() {
        final String tableName = "test_table_no_pk";
        final DBTable v0 = DBTable.builder()
                .name(tableName)
                .field(DBField.builder().code("001").name("id").type(DBFieldType.BIGINT).allowNull(false).comment("主键").build())
                .field(DBField.builder().code("002").name("code").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("code").build())
                .field(DBField.builder().code("003").name("name").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("name").build())
                .build();

        pgSqlSchemaManager.upgrade(v0, null, System.out::println);
        pgSqlSchemaManager.tableList().forEach(System.out::println);
    }

    @Test
    public void test_pg_index() {
        final DBTable test = DBTable.builder()
                .name("test1")
                .primaryKey("id")
                .field(DBField.builder().name("id").type(DBFieldType.BIGINT).allowNull(false).autoIncrement(true).build())
                .field(DBField.builder().name("code").type(DBFieldType.VARCHAR).comment("code").allowNull(false).build())
                .field(DBField.builder().name("name").type(DBFieldType.VARCHAR).comment("name").size(100).allowNull(false).build())
                .index(DBIndex.builder().name("idx_code").field("code").build()).build();

        pgSqlSchemaManager.drop("test1", System.out::println);
        pgSqlSchemaManager.upgrade(test, null, System.out::println);
    }

    @Test
    public void test_Rename_Table() {

        final String tableName = "test_rename_table";
        pgSqlSchemaManager.drop(tableName, System.out::println);

        final DBTable v0 = DBTable.builder()
                .name(tableName)
                .primaryKey("id")
                .field(DBField.builder().code("001").name("id").type(DBFieldType.BIGINT).allowNull(false).comment("主键").build())
                .field(DBField.builder().code("002").name("code").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("code").build())
                .field(DBField.builder().code("003").name("name").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("name").build())
                .build();

        final DBTable v1 = DBTable.builder()
                .name(tableName)
                .primaryKey("id")
                .field(DBField.builder().code("001").name("id").type(DBFieldType.BIGINT).allowNull(false).comment("主键").build())
                .field(DBField.builder().code("004").name("code").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("code").build())
                .field(DBField.builder().code("003").name("name2").type(DBFieldType.VARCHAR).allowNull(false).size(20).comment("name").build())
                .field(DBField.builder().code("005").name("desc").type(DBFieldType.TEXT).allowNull(false).comment("text").build())
                .build();
        pgSqlSchemaManager.upgrade(v0, null, System.out::println);
        pgSqlSchemaManager.upgrade(v1, v0, System.out::println);
    }

}
