package com.edc.vibe_engine.validation;

import com.edc.vibe_engine.common.exception.ValidationException;
import com.edc.vibe_engine.engine.rule.RuleValidator;
import com.edc.vibe_engine.engine.rule.ValidationRuleProcess;
import com.edc.vibe_engine.meta.MetaManager;
import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.enums.DataType;
import com.edc.vibe_engine.meta.validation.CustomValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRule;
import com.edc.vibe_engine.meta.validation.ValidationRuleType;
import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.script.JsExpressionEvaluator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class ValidationRuleTest {


    private RuleValidator ruleValidator;

    private DefEntity.DefEntityBuilder entityBuilder;
    private DataRecord record;

    @BeforeEach
    void setUp() {
        JsExpressionEvaluator expressionEvaluator = new JsExpressionEvaluator();
        ValidationRuleProcess validationRuleProcess = new ValidationRuleProcess(expressionEvaluator);
        ruleValidator = new RuleValidator(List.of(validationRuleProcess));

        // 创建测试实体
        entityBuilder = DefEntity.builder()
                .name("test_entity")
                .module("test")
                .displayName("测试实体");

        // 名称字段（必填）
        DefField nameField = DefField.builder()
                .name("name")
                .displayName("名称")
                .dataType(DataType.VARCHAR)
                .build();

        ValidationRule requiredRule = ValidationRule.builder()
                .type(ValidationRuleType.REQUIRED)
                .message("名称不能为空")
                .build();

        nameField.setValidationRules(List.of(requiredRule));
        entityBuilder.field(nameField);

        // 年龄字段（范围验证）
        DefField ageField = DefField.builder()
                .name("age")
                .displayName("年龄")
                .dataType(DataType.INT)
                .build();

        ValidationRule minRule = ValidationRule.builder()
                .type(ValidationRuleType.MIN)
                .message("年龄不能小于18岁")
                .build();
        minRule.setParamsMap(Map.of("min", 18));

        ValidationRule maxRule = ValidationRule.builder()
                .type(ValidationRuleType.MAX)
                .message("年龄不能大于120岁")
                .build();
        maxRule.setParamsMap(Map.of("max", 120));

        ageField.setValidationRules(List.of(minRule, maxRule));
        entityBuilder.field(ageField);

        // 电子邮件字段（格式验证）
        DefField emailField = DefField.builder()
                .name("email")
                .displayName("电子邮件")
                .dataType(DataType.VARCHAR)
                .build();

        ValidationRule emailRule = ValidationRule.builder()
                .type(ValidationRuleType.EMAIL)
                .message("电子邮件格式不正确")
                .build();

        emailField.setValidationRules(List.of(emailRule));
        entityBuilder.field(emailField);


        // 创建测试记录
        Map<String, Object> data = new HashMap<>();
        data.put("name", "Test");
        data.put("age", 25);
        data.put("email", "<EMAIL>");
        record = new DataRecord(data);

    }

    @Test
    void testRequiredValidation() {
        DefEntity entity = MetaManager.registerEntity(entityBuilder);
        // 测试必填验证通过
        record.set("name", "Test");
        assertDoesNotThrow(() -> ruleValidator.validate(entity, record));

        // 测试必填验证失败
        record.set("name", null);
        ValidationException exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("名称不能为空", exception.getErrors().get(0).getMessage());
    }

    @Test
    void testRangeValidation() {
        DefEntity entity = MetaManager.registerEntity(entityBuilder);
        // 测试范围验证通过
        record.set("age", 25);
        assertDoesNotThrow(() -> ruleValidator.validate(entity, record));

        // 测试最小值验证失败
        record.set("age", 15);
        ValidationException exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("年龄不能小于18岁", exception.getErrors().get(0).getMessage());

        // 测试最大值验证失败
        record.set("age", 130);
        exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("年龄不能大于120岁", exception.getErrors().get(0).getMessage());
    }

    @Test
    void testEmailValidation() {
        DefEntity entity = MetaManager.registerEntity(entityBuilder);
        // 测试电子邮件验证通过
        record.set("email", "<EMAIL>");
        assertDoesNotThrow(() -> ruleValidator.validate(entity, record));

        // 测试电子邮件验证失败
        record.set("email", "invalid-email");
        ValidationException exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("电子邮件格式不正确", exception.getErrors().get(0).getMessage());
    }

    @Test
    void testCustomValidation() {

        // 添加自定义验证规则
        DefField passwordField = DefField.builder()
                .name("password")
                .displayName("密码")
                .dataType(DataType.VARCHAR)
                .build();

        CustomValidationRule customRule = new CustomValidationRule(
                "value && value.length >= 8 && /[A-Z]/.test(value) && /[0-9]/.test(value)");
        customRule.setMessage("密码必须至少8个字符，包含大写字母和数字");

        passwordField.setValidationRules(List.of(customRule));

        entityBuilder.field(passwordField);
        DefEntity entity = MetaManager.registerEntity(entityBuilder);

        // 测试自定义验证通过
        record.set("password", "Password123");
        assertDoesNotThrow(() -> ruleValidator.validate(entity, record));

        // 测试自定义验证失败
        record.set("password", "weak");
        ValidationException exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("密码必须至少8个字符，包含大写字母和数字", exception.getErrors().get(0).getMessage());
    }

    @Test
    void testConditionalValidation() {

        // 添加条件验证规则
        DefField phoneField = DefField.builder()
                .name("phone")
                .displayName("电话号码")
                .dataType(DataType.VARCHAR)
                .build();

        ValidationRule phoneRule = ValidationRule.builder()
                .type(ValidationRuleType.PATTERN)
                .message("电话号码格式不正确")
                .condition("record.contact_type === 'phone'")
                .dependsOn(List.of("contact_type"))
                .build();
        phoneRule.setParamsMap(Map.of("pattern", "^\\d{11}$"));

        phoneField.setValidationRules(List.of(phoneRule));

        entityBuilder.field(phoneField);

        DefEntity entity = MetaManager.registerEntity(entityBuilder);

        // 测试条件为true时验证生效
        record.set("contact_type", "phone");
        record.set("phone", "12345"); // 无效的电话号码
        ValidationException exception = assertThrows(ValidationException.class,
                () -> ruleValidator.validate(entity, record));
        assertEquals("电话号码格式不正确", exception.getErrors().get(0).getMessage());

        // 测试条件为false时验证不生效
        record.set("contact_type", "email");
        record.set("phone", "12345"); // 无效的电话号码，但不会验证
        assertDoesNotThrow(() -> ruleValidator.validate(entity, record));
    }
}
