package com.edc.vibe_engine.meta;

import com.edc.vibe_engine.meta.config.MetaConfiguration;
import com.edc.vibe_engine.meta.manager.DualEngineMetaManager;
import com.edc.vibe_engine.meta.model.MetaDefinition;
import com.edc.vibe_engine.meta.provider.AnnotationMetaProvider;
import com.edc.vibe_engine.meta.provider.ConfigMetaProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

/**
 * 双引擎元数据管理器测试
 */
@ExtendWith(MockitoExtension.class)
class DualEngineMetaManagerTest {

    @Mock
    private MetaConfiguration metaConfiguration;

    @Mock
    private AnnotationMetaProvider annotationMetaProvider;

    @Mock
    private ConfigMetaProvider configMetaProvider;

    private DualEngineMetaManager dualEngineMetaManager;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();

        // 配置默认值
        when(metaConfiguration.isCacheEnabled()).thenReturn(true);
        when(metaConfiguration.getMergeStrategy()).thenReturn(MetaConfiguration.MergeStrategy.PRIORITY_BASED);

        // 创建提供者列表
        List<com.edc.vibe_engine.meta.interfaces.IMetaProvider> providers = List.of(
                annotationMetaProvider,
                configMetaProvider
        );

        dualEngineMetaManager = new DualEngineMetaManager(metaConfiguration, providers);
    }

    @Test
    void testGetMetaDefinition_FromAnnotation() {
        // 准备测试数据
        MetaDefinition annotationDef = MetaDefinition.builder()
                .name("product")
                .displayName("产品")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION)
                .priority(100)
                .build();

        when(annotationMetaProvider.getMetaDefinition("product")).thenReturn(annotationDef);
        when(configMetaProvider.getMetaDefinition("product")).thenReturn(null);

        // 执行测试
        MetaDefinition result = dualEngineMetaManager.getMetaDefinition("product");

        // 验证结果
        assertNotNull(result);
        assertEquals("product", result.getName());
        assertEquals("产品", result.getDisplayName());
        assertEquals("demo", result.getModule());
        assertEquals(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION, result.getSource());
    }

    @Test
    void testGetMetaDefinition_FromConfig() {
        // 准备测试数据
        MetaDefinition configDef = MetaDefinition.builder()
                .name("category")
                .displayName("产品分类")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.CONFIG)
                .priority(50)
                .build();

        when(annotationMetaProvider.getMetaDefinition("category")).thenReturn(null);
        when(configMetaProvider.getMetaDefinition("category")).thenReturn(configDef);

        // 执行测试
        MetaDefinition result = dualEngineMetaManager.getMetaDefinition("category");

        // 验证结果
        assertNotNull(result);
        assertEquals("category", result.getName());
        assertEquals("产品分类", result.getDisplayName());
        assertEquals("demo", result.getModule());
        assertEquals(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.CONFIG, result.getSource());
    }

    @Test
    void testGetMetaDefinition_MergeFromBothSources() {
        // 准备测试数据 - 注解定义
        MetaDefinition annotationDef = MetaDefinition.builder()
                .name("product")
                .displayName("产品")
                .module("demo")
                .description("注解定义的产品")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION)
                .priority(100)
                .build();

        // 准备测试数据 - 配置定义
        MetaDefinition configDef = MetaDefinition.builder()
                .name("product")
                .displayName("配置产品")
                .module("demo")
                .description("配置文件定义的产品")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.CONFIG)
                .priority(50)
                .build();

        when(annotationMetaProvider.getMetaDefinition("product")).thenReturn(annotationDef);
        when(configMetaProvider.getMetaDefinition("product")).thenReturn(configDef);

        // 执行测试
        MetaDefinition result = dualEngineMetaManager.getMetaDefinition("product");

        // 验证结果 - 应该优先使用注解定义（优先级更高）
        assertNotNull(result);
        assertEquals("product", result.getName());
        assertEquals("产品", result.getDisplayName()); // 注解的displayName
        assertEquals("demo", result.getModule());
        assertEquals(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION, result.getSource());
    }

    @Test
    void testGetMetaDefinition_NotFound() {
        when(annotationMetaProvider.getMetaDefinition("nonexistent")).thenReturn(null);
        when(configMetaProvider.getMetaDefinition("nonexistent")).thenReturn(null);

        MetaDefinition result = dualEngineMetaManager.getMetaDefinition("nonexistent");

        assertNull(result);
    }

    @Test
    void testGetAllMetaDefinitions() {
        // 准备测试数据
        MetaDefinition annotationDef = MetaDefinition.builder()
                .name("product")
                .displayName("产品")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION)
                .priority(100)
                .build();

        MetaDefinition configDef = MetaDefinition.builder()
                .name("category")
                .displayName("产品分类")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.CONFIG)
                .priority(50)
                .build();

        when(annotationMetaProvider.getAllMetaDefinitions()).thenReturn(List.of(annotationDef));
        when(configMetaProvider.getAllMetaDefinitions()).thenReturn(List.of(configDef));

        // 执行测试
        List<MetaDefinition> results = dualEngineMetaManager.getAllMetaDefinitions();

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());

        // 验证包含两个实体
        assertTrue(results.stream().anyMatch(def -> "product".equals(def.getName())));
        assertTrue(results.stream().anyMatch(def -> "category".equals(def.getName())));
    }

    @Test
    void testGetMetaDefinitionsByModule() {
        // 准备测试数据
        MetaDefinition demoDef1 = MetaDefinition.builder()
                .name("product")
                .displayName("产品")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION)
                .build();

        MetaDefinition demoDef2 = MetaDefinition.builder()
                .name("category")
                .displayName("产品分类")
                .module("demo")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.CONFIG)
                .build();

        MetaDefinition otherDef = MetaDefinition.builder()
                .name("user")
                .displayName("用户")
                .module("system")
                .source(com.edc.vibe_engine.meta.annotation.MetaSource.SourceType.ANNOTATION)
                .build();

        when(annotationMetaProvider.getAllMetaDefinitions()).thenReturn(List.of(demoDef1, otherDef));
        when(configMetaProvider.getAllMetaDefinitions()).thenReturn(List.of(demoDef2));

        // 执行测试
        List<MetaDefinition> results = dualEngineMetaManager.getMetaDefinitionsByModule("demo");

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        assertTrue(results.stream().allMatch(def -> "demo".equals(def.getModule())));
    }

    @Test
    void testClearCache() {
        // 这个测试主要验证方法不会抛出异常
        assertDoesNotThrow(() -> dualEngineMetaManager.clearCache());
        assertDoesNotThrow(() -> dualEngineMetaManager.clearCache("product"));
    }
}
