package com.edc.vibe_engine.meta;

import com.edc.vibe_engine.meta.def.DefEntity;
import com.edc.vibe_engine.meta.def.DefField;
import com.edc.vibe_engine.meta.def.DefRelation;
import com.edc.vibe_engine.meta.register.JsMetaRegister;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * JS元数据注册器测试
 */
public class JsMetaRegisterTest {

    private JsMetaRegister jsMetaRegister;

    @BeforeEach
    public void setUp() {
        jsMetaRegister = new JsMetaRegister();
        // 清空MetaManager中的数据
        MetaManager.METADATA_MAP.clear();
    }

    @Test
    public void testRegister() {
        // 执行注册
        jsMetaRegister.register();

        // 验证结果
        List<DefEntity> entities = MetaManager.getAll();
        System.out.println("注册的实体数量: " + entities.size());

        // 打印所有注册的实体
        for (DefEntity entity : entities) {
            System.out.println("实体: " + entity.getModule() + "." + entity.getName());
        }

        assertFalse(entities.isEmpty(), "应该至少注册了一个实体");

        // 检查是否包含测试实体
        boolean hasTestEntity = entities.stream()
                .anyMatch(e -> "test_entity".equals(e.getName()) && "test".equals(e.getModule()));

        if (!hasTestEntity) {
            System.out.println("未找到test_entity实体，可能的原因：");
            System.out.println("1. JS文件未被正确加载");
            System.out.println("2. JS文件格式不正确");
            System.out.println("3. 元数据解析失败");
        }

        assertTrue(hasTestEntity, "应该包含test_entity实体");

        // 检查实体字段
        DefEntity testEntity = MetaManager.get("test_entity");
        if (testEntity != null) {
            System.out.println("test_entity字段数量: " + testEntity.getFields().size());

            // 打印所有字段
            for (DefField field : testEntity.getFields()) {
                System.out.println("字段: " + field.getName() + " (" + field.getDataType() + ")");
            }

            assertNotNull(testEntity.getFields(), "字段列表不应为空");
            assertTrue(testEntity.getFields().stream()
                    .anyMatch(f -> "code".equals(f.getName())), "应该包含code字段");
            assertTrue(testEntity.getFields().stream()
                    .anyMatch(f -> "name".equals(f.getName())), "应该包含name字段");
            assertTrue(testEntity.getFields().stream()
                    .anyMatch(f -> "status".equals(f.getName())), "应该包含status字段");

            // 检查关系
            System.out.println("test_entity关系数量: " +
                               (testEntity.getRelations() != null ? testEntity.getRelations().size() : 0));

            // 打印所有关系
            if (testEntity.getRelations() != null) {
                for (DefRelation relation : testEntity.getRelations()) {
                    System.out.println("关系: " + relation.getName() + " -> " +
                                       relation.getModule() + "." + relation.getEntity());
                }
            }

            assertNotNull(testEntity.getRelations(), "关系列表不应为空");
            assertTrue(testEntity.getRelations().stream()
                    .anyMatch(r -> "parent".equals(r.getName())), "应该包含parent关系");
            assertTrue(testEntity.getRelations().stream()
                    .anyMatch(r -> "children".equals(r.getName())), "应该包含children关系");
        } else {
            System.out.println("未找到test_entity实体");
        }
    }

    @Test
    public void testResourceLoading() throws IOException {
        // 测试资源加载
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        // 测试测试资源目录
        Resource[] testResources = resolver.getResources("classpath:metadata/**/*.js");
        System.out.println("测试资源目录中的JS文件数量: " + testResources.length);

        // 打印找到的测试资源
        for (Resource resource : testResources) {
            System.out.println("测试资源: " + resource.getFilename() + " - " + resource.getURL());
        }

        // 测试主资源目录
        Resource[] mainResources = resolver.getResources("classpath*:metadata/**/*.js");
        System.out.println("所有资源目录中的JS文件数量: " + mainResources.length);

        // 打印找到的主资源
        for (Resource resource : mainResources) {
            System.out.println("所有资源: " + resource.getFilename() + " - " + resource.getURL());
        }

        // 测试根目录
        Resource[] rootResources = resolver.getResources("classpath*:**/*.js");
        System.out.println("所有JS文件数量: " + rootResources.length);

        // 打印部分根资源
        for (int i = 0; i < Math.min(10, rootResources.length); i++) {
            System.out.println("JS文件: " + rootResources[i].getFilename() + " - " + rootResources[i].getURL());
        }

        assertTrue(testResources.length > 0, "应该能找到测试JS元数据文件");
    }
}
