package com.edc.vibe_engine.script.context;

import com.edc.vibe_engine.script.JS;
import org.graalvm.polyglot.Value;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * JsContext 类的单元测试
 */
@DisplayName("JsContext 测试")
class JSTest {

    @Nested
    @DisplayName("基本类型转换测试")
    class BasicTypeConversionTests {

        @Test
        @DisplayName("应正确转换 null 值")
        void shouldConvertNullValue() {
            JS.withContext(ctx -> {
                Value nullValue = ctx.eval("js", "null");
                assertNull(JS.toJava(nullValue));
            });
        }

        @Test
        @DisplayName("应正确转换布尔值")
        void shouldConvertBooleanValue() {
            JS.withContext(ctx -> {
                Value trueValue = ctx.eval("js", "true");
                Value falseValue = ctx.eval("js", "false");

                assertEquals(Boolean.TRUE, JS.toJava(trueValue));
                assertEquals(Boolean.FALSE, JS.toJava(falseValue));
            });
        }

        @Test
        @DisplayName("应正确转换数字值")
        void shouldConvertNumberValue() {
            JS.withContext(ctx -> {
                Value intValue = ctx.eval("js", "42");
                Value doubleValue = ctx.eval("js", "3.14");

                assertEquals(42, ((Number) JS.toJava(intValue)).intValue());
                assertEquals(3.14, ((Number) JS.toJava(doubleValue)).doubleValue(), 0.001);
            });
        }

        @Test
        @DisplayName("应正确转换字符串值")
        void shouldConvertStringValue() {
            JS.withContext(ctx -> {
                Value stringValue = ctx.eval("js", "'Hello, World!'");

                assertEquals("Hello, World!", JS.toJava(stringValue));
            });
        }
    }

    @Nested
    @DisplayName("数组和列表转换测试")
    class ArrayAndListConversionTests {

        @Test
        @DisplayName("应将普通数组转换为 Java 数组")
        void shouldConvertArrayToJavaArray() {
            JS.withContext(ctx -> {
                Value arrayValue = ctx.eval("js", "[1, 2, 3, 4, 5]");

                Object result = JS.toJava(arrayValue);
                assertTrue(result instanceof List);

                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) result;
                assertEquals(5, list.size());
                assertEquals(1, ((Number) list.get(0)).intValue());
                assertEquals(5, ((Number) list.get(4)).intValue());
            });
        }

        @Test
        @DisplayName("应正确转换混合类型数组")
        void shouldConvertMixedTypeArray() {
            JS.withContext(ctx -> {
                Value mixedArray = ctx.eval("js", "[1, 'text', true, null]");

                Object result = JS.toJava(mixedArray);
                assertTrue(result instanceof List);

                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) result;
                assertEquals(4, list.size());
                assertEquals(1, ((Number) list.get(0)).intValue());
                assertEquals("text", list.get(1));
                assertEquals(true, list.get(2));
                assertNull(list.get(3));
            });
        }
    }

    @Nested
    @DisplayName("对象转换测试")
    class ObjectConversionTests {

        @Test
        @DisplayName("应将 JavaScript 对象转换为 Java Map")
        void shouldConvertObjectToMap() {
            JS.withContext(ctx -> {
                Value objectValue = ctx.eval("js", "({name: 'Test', value: 42})");

                Object result = JS.toJava(objectValue);
                assertTrue(result instanceof Map);

                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) result;
                assertEquals(2, map.size());
                assertEquals("Test", map.get("name"));
                assertEquals(42, ((Number) map.get("value")).intValue());
            });
        }

        @Test
        @DisplayName("应正确转换嵌套对象")
        void shouldConvertNestedObject() {
            JS.withContext(ctx -> {
                Value nestedObject = ctx.eval("js", "({user: {name: 'Test', age: 30}, active: true})");

                Object result = JS.toJava(nestedObject);
                assertTrue(result instanceof Map);

                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) result;
                assertEquals(2, map.size());
                assertTrue(map.get("user") instanceof Map);
                assertEquals(true, map.get("active"));

                @SuppressWarnings("unchecked")
                Map<String, Object> userMap = (Map<String, Object>) map.get("user");
                assertEquals("Test", userMap.get("name"));
                assertEquals(30, ((Number) userMap.get("age")).intValue());
            });
        }
    }

    @Nested
    @DisplayName("函数转换测试")
    class FunctionConversionTests {

        @Test
        @DisplayName("应将 JavaScript 函数转换为 JsFunction")
        void shouldConvertFunctionToJsFunction() {
            JS.withContext(ctx -> {
                Value functionValue = ctx.eval("js", "(function add(a, b) { return a + b; })");

                Object result = JS.toJava(functionValue);
                assertTrue(result instanceof JS.JsFunction);

                JS.JsFunction function = (JS.JsFunction) result;
                assertNotNull(function.source());
                assertTrue(function.source().contains("add"), "函数源码应包含函数名");
            });
        }

        @Test
        @DisplayName("应正确执行转换后的函数")
        void shouldExecuteConvertedFunction() {
            JS.withContext(ctx -> {
                Value functionValue = ctx.eval("js", "(function multiply(a, b) { return a * b; })");

                JS.JsFunction function = (JS.JsFunction) JS.toJava(functionValue);

                // 在同一个上下文中执行函数
                Object result = function.execute(ctx, 5, 3);
                assertEquals(15, ((Number) result).intValue());
            });
        }

        @Test
        @DisplayName("应正确处理对象中的函数")
        void shouldHandleFunctionsInObjects() {
            JS.withContext(ctx -> {
                Value objectWithFunction = ctx.eval("js",
                        "({name: 'Calculator', calculate: function(x) { return x * 2; }})");

                Object result = JS.toJava(objectWithFunction);
                assertTrue(result instanceof Map);

                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) result;
                assertEquals(2, map.size());
                assertEquals("Calculator", map.get("name"));
                assertTrue(map.get("calculate") instanceof JS.JsFunction);

                // 在同一个上下文中执行函数
                JS.JsFunction calculateFunction = (JS.JsFunction) map.get("calculate");
                Object calculationResult = calculateFunction.execute(ctx, 10);
                assertEquals(20, ((Number) calculationResult).intValue());
            });
        }
    }

    @Nested
    @DisplayName("错误处理测试")
    class ErrorHandlingTests {

        @Test
        @DisplayName("应正确处理非法函数值")
        void shouldHandleInvalidFunctionValue() {
            JS.withContext(ctx -> {
                Value nonFunctionValue = ctx.eval("js", "42");

                assertThrows(IllegalArgumentException.class, () -> {
                    JS.createFunctionWrapper(nonFunctionValue);
                });
            });
        }

        @Test
        @DisplayName("应正确处理无法转换的值")
        void shouldHandleUnconvertibleValue() {
            // 这个测试可能需要模拟一个无法转换的值
            // 由于 GraalVM 的限制，可能难以创建这样的场景
            // 此处仅作为示例
        }
    }

    @Nested
    @DisplayName("复杂场景测试")
    class ComplexScenarioTests {

        @Test
        @DisplayName("应正确处理复杂的嵌套结构")
        void shouldHandleComplexNestedStructure() {
            JS.withContext(ctx -> {
                String complexJs = """
                        ({
                            users: [
                                { id: 1, name: 'Alice', active: true },
                                { id: 2, name: 'Bob', active: false }
                            ],
                            settings: {
                                theme: 'dark',
                                notifications: true
                            },
                            getUser: function(id) {
                                return this.users.find(user => user.id === id);
                            }
                        })
                        """;

                Value complexValue = ctx.eval("js", complexJs);
                final Value getUser = complexValue.invokeMember("getUser", 1);
                assertEquals("Alice", getUser.getMember("name").asString());
                Object result = JS.toJava(complexValue);

                assertTrue(result instanceof Map);

                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) result;
                assertEquals(3, map.size());

                assertTrue(map.get("users") instanceof List);
                assertTrue(map.get("settings") instanceof Map);
                assertTrue(map.get("getUser") instanceof JS.JsFunction);

                @SuppressWarnings("unchecked")
                List<Object> users = (List<Object>) map.get("users");
                assertEquals(2, users.size());

                @SuppressWarnings("unchecked")
                Map<String, Object> user1 = (Map<String, Object>) users.get(0);
                assertEquals(1, ((Number) user1.get("id")).intValue());
                assertEquals("Alice", user1.get("name"));

                //                JsContext.JsFunction getUserFunction = (JsContext.JsFunction) map.get("getUser");
                //                Object user = getUserFunction.execute(ctx, 1);
                //
                //                assertNotNull(user);
                //                assertTrue(user instanceof Map);
                //
                //                @SuppressWarnings("unchecked")
                //                Map<String, Object> foundUser = (Map<String, Object>) user;
                //                assertEquals("Alice", foundUser.get("name"));
            });
        }
    }
}
