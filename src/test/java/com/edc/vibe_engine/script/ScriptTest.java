package com.edc.vibe_engine.script;

import com.edc.vibe_engine.record.DataRecord;
import com.edc.vibe_engine.record.column.ColumnCollection;
import com.edc.vibe_engine.script.proxy.ProxyRecord;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Source;
import org.graalvm.polyglot.Value;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class ScriptTest {


    static String JS_MODULE = """
            /**
             * 实体业务逻辑定义
             */
            export default {
              /**
               * 元数据
               */
              metadata: {
                // 实体名称
                name: "entity_name",
                // 显示名称
                displayName: "实体显示名称",
                // 描述
                description: "实体描述"
              },
              /**
               * 验证规则
               */
              validation: {
                /**
                 * 字段验证
                 * @param {string} field - 字段名
                 * @param {any} value - 字段值
                 * @param {object} record - 完整记录
                 * @returns {object} 验证结果
                 */
                validateField(field, value, record) {
                  // 返回验证结果
                  return { valid: true, message: "" };
                },
                /**
                 * 表单验证
                 * @param {object} record - 完整记录
                 * @returns {object} 验证结果
                 */
                validateForm(record) {
                  const errors = [];
                  // 执行验证逻辑
                  return { valid: errors.length === 0, errors };
                }
              },
              /**
               * 计算规则
               */
              calculation: {
                /**
                 * 计算字段值
                 * @param {string} field - 字段名
                 * @param {object} record - 完整记录
                 * @returns {any} 计算结果
                 */
                calculateField(field, record) {
                  // 根据字段名执行不同的计算逻辑
                  switch (field) {
                    case "field1":
                      // 计算field1的值
                      return someCalculation(record);
                    case "field2":
                      // 计算field2的值
                      return anotherCalculation(record);
                    default:
                      return null;
                  }
                }
              },
              /**
               * 业务规则
               */
              rules: {
                /**
                 * 前置规则（保存前执行）
                 * @param {object} record - 记录对象
                 * @returns {object} 处理后的记录
                 */
                beforeSave(record) {
                  // 修改record或返回新record
                  return record;
                },
                /**
                 * 后置规则（保存后执行）
                 * @param {object} record - 记录对象
                 */
                afterSave(record) {
                  // 执行后续操作
                }
              },
              /**
               * 自定义函数
               */
              functions: {
                /**
                 * 自定义业务函数
                 * @param {object} params - 参数
                 * @returns {any} 结果
                 */
                customFunction(params) {
                  // 返回结果
                  return result;
                }
              }
            };
            """;

    static String JS_CODE = "(function myFun(param){console.log('Hello ' + param + ' from JS');})";
    static String JS_CODE2 = """
            ({
            testFn:(param) => {
                console.log('Hello ' + param + ' from JS')
            },
            testFn2:() => {
                console.log('Hello ' + record.t1 + ' from JS')
                record['t3'] = "t2"
            }
            })
            """;
    static String JS_CODE3 = """
            (function test_export() {
                function testFn() {
                       console.log('Hello ' + record.t1 + ' from JS')
                 record['t3'] = "t2"
                }
             Polyglot.export("testFn", testFn);
            })
            """;

    @Test
    public void test_module() throws IOException {
        // 读取JS文件内容
        // 创建执行上下文

        String port = "4242";
        //        String path = java.util.UUID.randomUUID().toString();
        String path = "test-inspect";

        try (Context context = Context.newBuilder("js")
                .allowAllAccess(true)
                //                .option("inspect", port)
                //                .option("inspect.Path", path)
                .option("js.esm-eval-returns-exports", "true")
                .build()) {
            String hostAdress = "localhost";
            String debugUrl = String.format(
                    "chrome-devtools://devtools/bundled/js_app.html?ws=%s:%s/%s",
                    hostAdress, port, path);
            System.out.println("debug:" + debugUrl);

            // 执行脚本
            //            Value bindings = context.getBindings("js");
            //            context.eval("js", "var exports = {};");

            final Source js = Source.newBuilder("js", JS_MODULE, "test.mjs")
                    .mimeType("application/javascript+module")
                    .build();

            Value exports = context.eval(js);


            final Value metadata = exports.getMember("default").getMember("metadata");
            System.out.println(metadata);
        }

    }

    @Test
    public void test() {
        //        String who = "World";
        //        System.out.println("Hello " + who + " from Java");
        //        List<ScriptEngineFactory> engines = new ScriptEngineManager().getEngineFactories();
        //        for (ScriptEngineFactory f : engines) {
        //            System.out.println(f.getLanguageName() + " " + f.getEngineName() + " " + f.getNames());
        //        }
        //        System.out.println("=== Graal.js via javax.script.ScriptEngine ===");
        //        ScriptEngine graaljsEngine = new ScriptEngineManager().getEngineByName("graal.js");
        //        if (graaljsEngine == null) {
        //            System.out.println("*** Graal.js not found ***");
        //        } else {
        //            //             benchScriptEngineIntl(graaljsEngine);
        //        }

        final DataRecord record = new DataRecord(new ColumnCollection());
        record.set("t1", 1);
        record.set("t2", 2);

        try (Context context = Context.create()) {
            context.getBindings("js").putMember("record", new ProxyRecord(record));


            context.eval("js", JS_CODE3);

            //
            //            System.out.println(value.canExecute());
            //            final Value testFn1 = value.getMember("testFn");

            //            testFn1.canExecute()

            //            final Object o = copyToJavaLand(testFn1);

            //            Value mkfn = context.eval("js", testFn1.toString());
            //            mkfn.execute("World2");
            //            System.out.println(mkfn.canExecute());

            //            final Value testFn = value.invokeMember("testFn2", "World");


            //            testFn.execute("World");

            context.eval("js", """
                    (function x() {
                    var testFn = Polyglot.import("testFn");
                    testFn()
                    })
                    """);

            System.out.println(record.get("t3"));
        }

        //        try (Context context = Context.create()) {
        //            context.getBindings("js").putMember("record", new ProxyRecord(record));
        //
        //            context.eval("js", """
        //                    (function x() {
        //                    var test = Polyglot.import("test");
        //                    test.testFn2()
        //                    })
        //                    """);
        //
        //            System.out.println(record.get("t3"));
        //        }


    }

    Object copyToJavaLand(Value value) {
        if (value.isBoolean()) {
            return value.asBoolean();
        } else if (value.isString()) {
            return value.asString();
        } else if (value.isNumber()) {
            return value.as(Number.class);
        } else if (value.isHostObject()) {
            return value.asHostObject();
        } else if (value.isProxyObject()) {
            return value.asProxyObject();
        } else if (value.hasArrayElements()) {
            Object[] array = new Object[(int) value.getArraySize()];
            for (int i = 0; i < array.length; i++) {
                array[i] = copyToJavaLand(value.getArrayElement(i));
            }
            return array;
        }
        throw new IllegalArgumentException("Cannot copy value " + value + ".");
    }
}
