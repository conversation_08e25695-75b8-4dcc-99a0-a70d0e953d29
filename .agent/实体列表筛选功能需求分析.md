# 实体列表页面筛选功能需求分析

## 功能概述

实现实体列表页面的筛选功能，支持基本查询和高级查询，通过元数据驱动的方式定义字段的查询行为。

## 详细需求

### 1. 元数据定义扩展

- **EntityField.flags**：标记字段为关键信息字段，支持模糊查询，可以有多个关键信息字段
- **EntityField.condition**：定义字段的查询行为
    - 是否支持查询
    - 查询时使用的操作符
    - 是否支持高级查询

### 2. 查询区域UI设计

- 所有关键信息字段共用一个输入框，实现模糊查询
- 其他支持查询的字段平铺在查询区域中
- 支持添加自定义高级查询条件

### 3. 查询条件处理

- 所有查询条件合并到Condition对象中
- 支持基本查询和高级查询的条件组合

### 4. 技术实现要点

- 扩展EntityField类型定义，增加flags和condition属性
- 实现查询表单组件，支持基本查询和高级查询
- 实现查询条件的构建和转换逻辑
- 集成到EntityList组件中

## 数据结构设计

### EntityField扩展

需要扩展EntityField类型，增加以下属性：

- **flags**: 包含isKeyInfo等标记，用于标识字段特性
- **condition**: 定义查询行为，包括是否可查询、使用的操作符、是否支持高级查询等

### 查询条件结构

基于提供的Condition类，实现查询条件的构建和转换，支持：

- 基本查询条件
- 高级查询条件
- 条件组合（AND/OR/NOT）
- 不同比较操作符（EQ/NE/GT/LT/LIKE等）

## 组件设计

### 1. EntityListFilter组件

- 负责渲染查询表单
- 处理基本查询和高级查询
- 生成Condition对象

### 2. KeywordSearch组件

- 处理关键信息字段的模糊查询
- 支持多个关键字段的组合查询

### 3. AdvancedFilter组件

- 支持添加自定义高级查询条件
- 提供灵活的条件组合方式

## 实现路径

1. 扩展EntityField类型定义
2. 实现EntityListFilter组件
3. 实现KeywordSearch组件
4. 实现AdvancedFilter组件
5. 集成到EntityList组件中
6. 实现查询条件的构建和转换逻辑

## 交互流程

1. 用户在查询区域输入查询条件
2. 系统根据字段元数据构建查询表单
3. 用户提交查询请求
4. 系统构建Condition对象
5. 将Condition对象传递给GraphQL查询
6. 展示查询结果

## 技术挑战与解决方案

1. **复杂查询条件的构建**：利用Condition类的嵌套结构支持复杂条件
2. **查询UI的灵活性**：基于元数据动态生成查询表单
3. **查询性能优化**：合理设计查询条件，避免不必要的查询

## 后续扩展可能

1. 支持查询条件的保存和复用
2. 支持更复杂的条件组合方式
3. 支持更多类型的查询控件
