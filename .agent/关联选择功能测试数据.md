# 关联选择功能测试数据

## 1. 测试实体元数据

### 1.1 员工实体元数据

```typescript
import { EntityMetadata } from '@/features/metadata/types'
import { RelationConditionType } from '@/features/metadata/types/relation'

/**
 * 员工实体元数据
 */
export const employeeMetadata: EntityMetadata = {
  name: 'employee',
  display_name: '员工',
  module: 'hr',
  description: '员工信息',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'employee_id_field',
    },
    {
      name: 'code',
      display_name: '工号',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_code_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE'],
        defaultOperator: '__EQ',
      },
    },
    {
      name: 'name',
      display_name: '姓名',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_name_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE', '__L_LIKE', '__R_LIKE'],
        defaultOperator: '__LIKE',
      },
    },
    {
      name: 'department_id',
      display_name: '部门',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'employee_department_field',
    },
    {
      name: 'position_id',
      display_name: '职位',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'employee_position_field',
    },
    {
      name: 'manager_id',
      display_name: '上级主管',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'employee_manager_field',
    },
    {
      name: 'status',
      display_name: '状态',
      type: 'VARCHAR',
      default_value: 'active',
      flags: [],
      _id: 'employee_status_field',
      options: {
        values: [
          { label: '在职', value: 'active' },
          { label: '离职', value: 'inactive' },
          { label: '休假', value: 'leave' },
        ],
      },
    },
  ],
  _id: 'employee_metadata',
  relations: [
    // 部门关联 - 普通用户视图
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      alias: 'department_info',
      condition: {
        type: 'role' as RelationConditionType,
        roles: ['user'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
    },
    // 部门关联 - 管理员视图
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      alias: 'department_detail',
      condition: {
        type: 'role' as RelationConditionType,
        roles: ['admin'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'manager_name', 'employee_count'],
        searchable: true,
        previewable: true,
      },
    },
    // 职位关联
    {
      field: 'position_id',
      entity: 'position',
      module: 'hr',
      type: 'one',
      alias: 'position_id_relation',
      condition: {
        type: 'always' as RelationConditionType,
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'level'],
        searchable: true,
        filter: {
          condition: 'department_id ? { where: [{ department_id: { __EQ: department_id } }] } : {}',
          dependsOn: ['department_id'],
        },
      },
    },
    // 上级主管关联 - 创建模式
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      alias: 'manager',
      condition: {
        type: 'mode' as RelationConditionType,
        modes: ['create'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_name'],
        searchable: true,
        filter: {
          condition: 'department_id ? { where: [{ department_id: { __EQ: department_id }, status: { __EQ: "active" } }] } : { where: [{ status: { __EQ: "active" } }] }',
          dependsOn: ['department_id'],
        },
      },
    },
    // 上级主管关联 - 编辑模式
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      alias: 'manager_detail',
      condition: {
        type: 'mode' as RelationConditionType,
        modes: ['edit'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_name', 'department_name'],
        searchable: true,
        previewable: true,
        filter: {
          condition: '{ where: [{ _id: { __NE: _id }, status: { __EQ: "active" } }] }',
          dependsOn: ['_id'],
        },
      },
    },
  ],
}

/**
 * 部门实体元数据
 */
export const departmentMetadata: EntityMetadata = {
  name: 'department',
  display_name: '部门',
  module: 'hr',
  description: '部门信息',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'department_id_field',
    },
    {
      name: 'code',
      display_name: '部门编码',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_code_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE'],
        defaultOperator: '__EQ',
      },
    },
    {
      name: 'name',
      display_name: '部门名称',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_name_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE'],
        defaultOperator: '__LIKE',
      },
    },
    {
      name: 'parent_id',
      display_name: '上级部门',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_parent_field',
    },
    {
      name: 'manager_id',
      display_name: '部门经理',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_manager_field',
    },
    {
      name: 'manager_name',
      display_name: '经理姓名',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE', 'VIRTUAL'],
      _id: 'department_manager_name_field',
    },
    {
      name: 'employee_count',
      display_name: '员工数量',
      type: 'INTEGER',
      default_value: '0',
      flags: ['VIRTUAL'],
      _id: 'department_employee_count_field',
    },
  ],
  _id: 'department_metadata',
  relations: [
    // 上级部门关联
    {
      field: 'parent_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      alias: 'parent_department',
      condition: {
        type: 'always' as RelationConditionType,
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
        filter: {
          condition: '_id ? { where: [{ _id: { __NE: _id } }] } : {}',
          dependsOn: ['_id'],
        },
      },
    },
    // 部门经理关联
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      alias: 'manager',
      condition: {
        type: 'always' as RelationConditionType,
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_name'],
        searchable: true,
        filter: {
          condition: '{ where: [{ status: { __EQ: "active" } }] }',
          dependsOn: [],
        },
      },
    },
  ],
}
```

## 2. 测试数据

### 2.1 员工数据

```typescript
/**
 * 员工测试数据
 */
export const mockEmployeeData = [
  {
    _id: '1',
    code: 'EMP001',
    name: '张三',
    department_id: '1',
    position_id: '1',
    manager_id: null,
    status: 'active',
    department_info: {
      _id: '1',
      code: 'DEP001',
      name: '研发部',
    },
    position_id_relation: {
      _id: '1',
      code: 'POS001',
      name: '技术总监',
      level: 'P8',
    },
  },
  {
    _id: '2',
    code: 'EMP002',
    name: '李四',
    department_id: '1',
    position_id: '2',
    manager_id: '1',
    status: 'active',
    department_info: {
      _id: '1',
      code: 'DEP001',
      name: '研发部',
    },
    position_id_relation: {
      _id: '2',
      code: 'POS002',
      name: '高级工程师',
      level: 'P6',
    },
    manager: {
      _id: '1',
      code: 'EMP001',
      name: '张三',
      position_name: '技术总监',
    },
  },
  // 更多测试数据...
]

/**
 * 部门测试数据
 */
export const mockDepartmentData = [
  {
    _id: '1',
    code: 'DEP001',
    name: '研发部',
    parent_id: null,
    manager_id: '1',
    manager_name: '张三',
    employee_count: 10,
    manager: {
      _id: '1',
      code: 'EMP001',
      name: '张三',
      position_name: '技术总监',
    },
  },
  {
    _id: '2',
    code: 'DEP002',
    name: '产品部',
    parent_id: null,
    manager_id: '3',
    manager_name: '王五',
    employee_count: 5,
    manager: {
      _id: '3',
      code: 'EMP003',
      name: '王五',
      position_name: '产品总监',
    },
  },
  // 更多测试数据...
]
```
