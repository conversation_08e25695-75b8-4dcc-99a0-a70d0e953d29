# 实体表单关联选择功能系统分析

## 1. 系统架构

实体表单的关联选择功能将基于现有的表单系统进行扩展，主要涉及以下几个部分：

```
EntityForm
├── EntityField
│   ├── RelationSelect (新增)
│   │   ├── RelationSelectTrigger
│   │   ├── RelationSelectModal
│   │   └── RelationTag
│   └── 其他控件...
├── FieldContainer
└── 其他组件...
```

### 1.1 核心组件

1. **RelationSelect**：关联选择的主控件，负责整体逻辑和状态管理
2. **RelationSelectTrigger**：触发选择操作的组件，可以是下拉框或按钮
3. **RelationSelectModal**：弹窗选择界面，包含搜索、筛选和分页功能
4. **RelationTag**：已选关联项的标签展示组件

### 1.2 数据流

```
用户操作 → RelationSelect → 关联数据查询 → 数据展示 → 选择确认 → 表单数据更新
```

## 2. 元数据扩展

### 2.1 关联配置

需要扩展`EntityField`的元数据结构，增加关联字段的配置：

```typescript
// 在src/features/metadata/types/index.ts中扩展

/**
 * 关联配置
 */
export interface RelationConfig {
  entity: string;              // 关联实体名称
  module?: string;             // 关联实体所属模块，默认为meta
  valueField?: string;         // 值字段，默认为_id
  labelField?: string;         // 标签字段，默认为name
  displayFields?: string[];    // 显示字段列表
  multiple?: boolean;          // 是否多选
  cascade?: {                  // 级联配置
    parent?: string;           // 父级字段
    children?: string;         // 子级字段
  };
  filter?: {                   // 过滤条件
    condition?: string;        // 条件表达式
    dependsOn?: string[];      // 依赖字段
  };
  searchable?: boolean;        // 是否可搜索
  createable?: boolean;        // 是否可创建
  previewable?: boolean;       // 是否可预览
}

/**
 * 字段控制配置
 */
export interface FieldControl {
  // 现有属性...
  relation?: RelationConfig;   // 关联配置
}
```

### 2.2 控件类型映射

在控件类型映射中添加关联选择控件：

```typescript
// 在src/lib/metadata/controls.ts中扩展

/**
 * 确定字段的控件类型
 */
export function determineControlType(field: EntityField): string {
  // 优先使用前端指定的控件类型
  if (field.control?.type) {
    return field.control.type;
  }
  
  // 检查是否有关联配置，有则使用关联选择控件
  if (field.control?.relation) {
    return field.control.relation.multiple ? 'relationMultiSelect' : 'relationSelect';
  }
  
  // 其次检查是否有选项配置，有则使用选择类控件
  if (field.options) {
    return field.options.multiple ? 'multiSelect' : 'select';
  }
  
  // 最后使用默认映射
  return getFieldControlType(field.type);
}
```

## 3. 组件设计

### 3.1 RelationSelect 组件

```typescript
// src/lib/controls/relation-select.tsx

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Dialog } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Search } from 'lucide-react';
import { ControlProps } from '@/lib/controls';
import { fetchEntityList } from '@/features/entity/api/entityApi';
import { fetchEntityMetadata } from '@/features/metadata/api/metadataApi';
import { EntityMetadata } from '@/features/metadata/types';

export interface RelationSelectProps extends ControlProps {
  relation: {
    entity: string;
    module?: string;
    valueField?: string;
    labelField?: string;
    displayFields?: string[];
    multiple?: boolean;
    // 其他关联配置...
  };
}

export const RelationSelect: React.FC<RelationSelectProps> = ({
  value,
  onChange,
  disabled,
  readOnly,
  placeholder = '请选择',
  relation,
  ...props
}) => {
  // 状态管理
  const [isOpen, setIsOpen] = useState(false);
  const [metadata, setMetadata] = useState<EntityMetadata | null>(null);
  const [items, setItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');
  
  // 配置项
  const module = relation.module || 'meta';
  const valueField = relation.valueField || '_id';
  const labelField = relation.labelField || 'name';
  const isMultiple = relation.multiple || false;
  
  // 加载关联实体元数据
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        const meta = await fetchEntityMetadata(relation.entity, module);
        setMetadata(meta);
      } catch (error) {
        console.error('Failed to load entity metadata:', error);
      }
    };
    
    loadMetadata();
  }, [relation.entity, module]);
  
  // 加载关联实体数据
  const loadItems = async (page = 1, pageSize = 10, condition = {}) => {
    if (!metadata) return;
    
    setLoading(true);
    try {
      const response = await fetchEntityList(
        relation.entity,
        page,
        pageSize,
        condition,
        module,
        metadata
      );
      setItems(response.items);
    } catch (error) {
      console.error('Failed to load entity items:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 处理选择变更
  const handleSelectionChange = (selected: any[]) => {
    setSelectedItems(selected);
    
    if (isMultiple) {
      onChange(selected.map(item => item[valueField]));
    } else {
      onChange(selected.length > 0 ? selected[0][valueField] : null);
    }
  };
  
  // 渲染触发器
  const renderTrigger = () => {
    // 实现触发器UI
  };
  
  // 渲染选择弹窗
  const renderModal = () => {
    // 实现选择弹窗UI
  };
  
  // 渲染已选标签
  const renderTags = () => {
    // 实现已选标签UI
  };
  
  return (
    <div className="relation-select">
      {/* 实现组件UI */}
    </div>
  );
};
```

### 3.2 控件注册

```typescript
// 在src/lib/controls/index.tsx中注册关联选择控件

import { RelationSelect } from './relation-select';
import { RelationMultiSelect } from './relation-multi-select';

// 注册关联选择控件
controlRegistry.set('relationSelect', RelationSelect);
controlRegistry.set('relationMultiSelect', RelationMultiSelect);
```

## 4. 数据处理

### 4.1 关联数据查询

```typescript
/**
 * 查询关联实体数据
 * @param entity 实体名称
 * @param module 模块名称
 * @param condition 查询条件
 * @param page 页码
 * @param pageSize 每页条数
 * @returns 分页数据
 */
export const fetchRelationData = async (
  entity: string,
  module: string = 'meta',
  condition: Record<string, unknown> = {},
  page: number = 1,
  pageSize: number = 10
): Promise<PageResponse<any>> => {
  try {
    // 先获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module);
    
    // 使用元数据查询实体列表
    return await fetchEntityList(
      entity,
      page,
      pageSize,
      condition,
      module,
      metadata
    );
  } catch (error) {
    console.error(`Failed to fetch relation data for ${entity}:`, error);
    throw error;
  }
};
```

### 4.2 关联数据缓存

```typescript
/**
 * 关联数据缓存
 */
class RelationDataCache {
  private cache: Map<string, {
    data: any[];
    timestamp: number;
    total: number;
  }> = new Map();
  
  private readonly TTL = 5 * 60 * 1000; // 5分钟缓存过期
  
  /**
   * 生成缓存键
   */
  private generateKey(entity: string, module: string, condition: Record<string, unknown>, page: number, pageSize: number): string {
    return `${module}:${entity}:${JSON.stringify(condition)}:${page}:${pageSize}`;
  }
  
  /**
   * 获取缓存数据
   */
  get(entity: string, module: string, condition: Record<string, unknown>, page: number, pageSize: number): any | null {
    const key = this.generateKey(entity, module, condition, page, pageSize);
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }
    
    return cached;
  }
  
  /**
   * 设置缓存数据
   */
  set(entity: string, module: string, condition: Record<string, unknown>, page: number, pageSize: number, data: any[], total: number): void {
    const key = this.generateKey(entity, module, condition, page, pageSize);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      total
    });
  }
  
  /**
   * 清除实体相关的所有缓存
   */
  clearEntity(entity: string, module: string): void {
    const prefix = `${module}:${entity}:`;
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * 清除所有缓存
   */
  clearAll(): void {
    this.cache.clear();
  }
}

// 导出单例
export const relationDataCache = new RelationDataCache();
```

## 5. 实现路径

### 5.1 开发步骤

1. **元数据扩展**
    - 扩展EntityField类型，添加关联配置
    - 更新控件类型映射逻辑

2. **基础组件开发**
    - 实现RelationSelect组件
    - 实现RelationMultiSelect组件
    - 实现RelationSelectModal组件

3. **数据处理**
    - 实现关联数据查询功能
    - 实现关联数据缓存机制

4. **高级功能**
    - 实现级联选择功能
    - 实现关联创建功能
    - 实现关联预览功能

5. **集成测试**
    - 单元测试各组件功能
    - 集成测试表单中的关联选择
    - 性能测试大数据量场景

### 5.2 优先级排序

1. **P0**：基础单选关联功能
2. **P1**：多选关联功能
3. **P2**：级联选择功能
4. **P3**：关联创建和预览功能

## 6. 测试计划

### 6.1 单元测试

- 测试RelationSelect组件的基本功能
- 测试RelationMultiSelect组件的多选功能
- 测试关联数据查询和缓存机制

### 6.2 集成测试

- 测试在EntityForm中使用关联选择控件
- 测试关联选择与表单其他字段的交互
- 测试关联选择的数据提交和回显

### 6.3 性能测试

- 测试大数据量下的加载性能
- 测试多个关联字段同时存在的场景
- 测试缓存机制的效果
