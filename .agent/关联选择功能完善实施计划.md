# 关联选择功能完善实施计划

## 1. 关联数据查询实现

### 1.1 完善 `src/lib/relation/api.ts` 文件

```typescript
/**
 * 查询关联实体数据
 * @param entity 实体名称
 * @param module 模块名称
 * @param condition 查询条件
 * @param page 页码
 * @param pageSize 每页条数
 * @param context 上下文
 * @returns 分页数据
 */
export const fetchRelationData = async <T>(
  entity: string,
  module: string = 'meta',
  condition: Record<string, unknown> = {},
  page: number = 1,
  pageSize: number = 10,
  context: Record<string, any> = {}
): Promise<PageResponse<T>> => {
  try {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 使用元数据查询实体列表
    return await fetchEntityList(
      entity,
      page,
      pageSize,
      condition,
      module,
      metadata,
      context
    )
  } catch (error) {
    console.error(`Failed to fetch relation data for ${entity}:`, error)
    throw error
  }
}

/**
 * 查询关联实体详情
 * @param entity 实体名称
 * @param id 实体ID
 * @param module 模块名称
 * @param context 上下文
 * @returns 实体详情
 */
export const fetchRelationDetail = async <T>(
  entity: string,
  id: string,
  module: string = 'meta',
  context: Record<string, any> = {}
): Promise<T> => {
  try {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 查询实体详情
    return await fetchEntityDetail(
      entity,
      id,
      module,
      metadata,
      context
    )
  } catch (error) {
    console.error(`Failed to fetch relation detail for ${entity}:`, error)
    throw error
  }
}

/**
 * 批量查询关联实体详情
 * @param entity 实体名称
 * @param ids 实体ID数组
 * @param module 模块名称
 * @param context 上下文
 * @returns 实体详情数组
 */
export const fetchRelationBatch = async <T>(
  entity: string,
  ids: string[],
  module: string = 'meta',
  context: Record<string, any> = {}
): Promise<T[]> => {
  if (ids.length === 0) {
    return []
  }
  
  try {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 查询条件
    const condition = {
      where: [
        {
          _id: {
            __IN: ids
          }
        }
      ]
    }
    
    // 查询实体列表
    const response = await fetchEntityList<T>(
      entity,
      1,
      ids.length,
      condition,
      module,
      metadata,
      context
    )
    
    return response.items
  } catch (error) {
    console.error(`Failed to fetch relation batch for ${entity}:`, error)
    throw error
  }
}
```

### 1.2 创建 `src/lib/relation/cache.ts` 文件

```typescript
/**
 * 关联数据缓存
 */
class RelationDataCache {
  private cache: Map<string, {
    data: any[]
    timestamp: number
    hasMore: boolean
    total: number
  }> = new Map()
  
  private readonly TTL = 5 * 60 * 1000 // 5分钟缓存过期
  
  /**
   * 生成缓存键
   */
  private generateKey(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number
  ): string {
    return `${module}.${entity}:${page}:${pageSize}:${JSON.stringify(condition)}`
  }
  
  /**
   * 获取缓存数据
   */
  get<T>(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number
  ): { data: T[], hasMore: boolean, total: number } | null {
    const key = this.generateKey(entity, module, condition, page, pageSize)
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }
    
    return {
      data: cached.data as T[],
      hasMore: cached.hasMore,
      total: cached.total
    }
  }
  
  /**
   * 设置缓存数据
   */
  set<T>(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number,
    data: T[],
    hasMore: boolean,
    total: number
  ): void {
    const key = this.generateKey(entity, module, condition, page, pageSize)
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hasMore,
      total
    })
  }
  
  /**
   * 清除缓存
   */
  clear(entity?: string, module?: string): void {
    if (!entity && !module) {
      this.cache.clear()
      return
    }
    
    const prefix = module ? `${module}.${entity || ''}` : entity
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix!)) {
        this.cache.delete(key)
      }
    }
  }
}

// 导出单例
export const relationDataCache = new RelationDataCache()
```

## 2. 条件过滤实现

### 2.1 创建 `src/lib/relation/filter.ts` 文件

```typescript
import { RelationControl } from '@/features/metadata/types/relation'

/**
 * 构建关联过滤条件
 * @param filter 过滤配置
 * @param formValues 表单值
 * @returns 查询条件
 */
export const buildRelationFilter = (
  filter: RelationControl['filter'],
  formValues: Record<string, any>
): Record<string, unknown> => {
  if (!filter) return {}
  
  const { condition, dependsOn } = filter
  
  // 如果没有条件表达式，返回空条件
  if (!condition) return {}
  
  try {
    // 构建条件对象
    const conditionObj: Record<string, unknown> = {}
    
    // 获取依赖字段的值
    const dependencyValues: Record<string, any> = {}
    if (dependsOn && dependsOn.length > 0) {
      dependsOn.forEach(field => {
        dependencyValues[field] = formValues[field]
      })
    }
    
    // 使用 Function 构造函数创建一个函数，传入依赖字段值
    const keys = Object.keys(dependencyValues)
    const values = Object.values(dependencyValues)
    const fn = new Function(...keys, `return ${condition}`)
    const result = fn(...values)
    
    // 如果结果是对象，直接返回
    if (typeof result === 'object' && result !== null) {
      return result
    }
    
    // 否则构建简单条件
    return { where: [{ condition: result }] }
  } catch (error) {
    console.error('Error building relation filter:', error)
    return {}
  }
}
```

### 2.2 创建 `src/lib/relation/hooks/useRelationDependency.ts` 文件

```typescript
import { useState, useEffect } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { RelationControl } from '@/features/metadata/types/relation'
import { buildRelationFilter } from '../filter'

/**
 * 关联依赖 Hook
 * @param field 字段名
 * @param control 关联控制配置
 * @returns 过滤条件和依赖状态
 */
export const useRelationDependency = (
  field: string,
  control?: RelationControl
): {
  filter: Record<string, unknown>
  isDependencyReady: boolean
  dependencyValues: Record<string, any>
} => {
  const { control: formControl } = useFormContext()
  const [filter, setFilter] = useState<Record<string, unknown>>({})
  const [isDependencyReady, setIsDependencyReady] = useState(false)
  const [dependencyValues, setDependencyValues] = useState<Record<string, any>>({})
  
  // 获取依赖字段列表
  const dependsOn = control?.filter?.dependsOn || []
  
  // 监听依赖字段变化
  const watchedValues = useWatch({
    control: formControl,
    name: dependsOn,
  })
  
  // 当依赖字段变化时，更新过滤条件
  useEffect(() => {
    if (!control?.filter) {
      setIsDependencyReady(true)
      setFilter({})
      return
    }
    
    // 构建依赖值对象
    const values: Record<string, any> = {}
    let allReady = true
    
    dependsOn.forEach((dep, index) => {
      const value = Array.isArray(watchedValues) ? watchedValues[index] : watchedValues
      values[dep] = value
      
      // 检查依赖是否就绪
      if (value === undefined || value === null) {
        allReady = false
      }
    })
    
    setDependencyValues(values)
    setIsDependencyReady(allReady)
    
    // 构建过滤条件
    if (allReady) {
      const newFilter = buildRelationFilter(control.filter, values)
      setFilter(newFilter)
    }
  }, [watchedValues, control?.filter, dependsOn])
  
  return { filter, isDependencyReady, dependencyValues }
}
```

## 3. 关联选择组件优化

### 3.1 修改 `src/lib/relation/components/relation-select.tsx` 文件

主要修改点：

1. 使用真实数据查询替换模拟数据
2. 实现条件过滤功能
3. 优化加载状态和错误处理
4. 实现分页加载和虚拟滚动

### 3.2 修改 `src/lib/relation/components/relation-multi-select.tsx` 文件

主要修改点：

1. 使用真实数据查询替换模拟数据
2. 实现条件过滤功能
3. 优化加载状态和错误处理
4. 实现分页加载和虚拟滚动

## 4. 测试计划

### 4.1 单元测试

1. 测试 `buildRelationFilter` 函数
2. 测试 `useRelationDependency` Hook
3. 测试关联数据查询函数

### 4.2 集成测试

1. 测试 `RelationSelect` 组件
2. 测试 `RelationMultiSelect` 组件
3. 测试条件过滤功能

### 4.3 端到端测试

1. 测试完整的关联选择流程
2. 测试大数据量的性能表现
