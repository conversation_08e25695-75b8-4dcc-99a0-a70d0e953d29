# 关联选择功能实现代码 - 条件过滤

## 1. 条件过滤实现 (src/lib/relation/filter.ts)

```typescript
import { RelationControl } from '@/features/metadata/types/relation'

/**
 * 构建关联过滤条件
 * @param filter 过滤配置
 * @param formValues 表单值
 * @returns 查询条件
 */
export const buildRelationFilter = (
  filter: RelationControl['filter'],
  formValues: Record<string, any>
): Record<string, unknown> => {
  if (!filter) return {}
  
  const { condition, dependsOn } = filter
  
  // 如果没有条件表达式，返回空条件
  if (!condition) return {}
  
  try {
    // 获取依赖字段的值
    const dependencyValues: Record<string, any> = {}
    if (dependsOn && dependsOn.length > 0) {
      dependsOn.forEach(field => {
        dependencyValues[field] = formValues[field]
      })
    }
    
    // 使用 Function 构造函数创建一个函数，传入依赖字段值
    const keys = Object.keys(dependencyValues)
    const values = Object.values(dependencyValues)
    const fn = new Function(...keys, `return ${condition}`)
    const result = fn(...values)
    
    // 如果结果是对象，直接返回
    if (typeof result === 'object' && result !== null) {
      return result
    }
    
    // 否则构建简单条件
    return { where: [{ condition: result }] }
  } catch (error) {
    console.error('Error building relation filter:', error)
    return {}
  }
}

/**
 * 检查依赖字段是否就绪
 * @param dependsOn 依赖字段列表
 * @param formValues 表单值
 * @returns 是否就绪
 */
export const isDependencyReady = (
  dependsOn: string[] | undefined,
  formValues: Record<string, any>
): boolean => {
  if (!dependsOn || dependsOn.length === 0) return true
  
  return dependsOn.every(field => {
    const value = formValues[field]
    return value !== undefined && value !== null
  })
}

/**
 * 获取依赖字段值
 * @param dependsOn 依赖字段列表
 * @param formValues 表单值
 * @returns 依赖字段值
 */
export const getDependencyValues = (
  dependsOn: string[] | undefined,
  formValues: Record<string, any>
): Record<string, any> => {
  if (!dependsOn || dependsOn.length === 0) return {}
  
  const values: Record<string, any> = {}
  dependsOn.forEach(field => {
    values[field] = formValues[field]
  })
  
  return values
}

/**
 * 检查依赖字段是否变化
 * @param prevValues 上一次的值
 * @param nextValues 当前的值
 * @returns 是否变化
 */
export const hasDependencyChanged = (
  prevValues: Record<string, any>,
  nextValues: Record<string, any>
): boolean => {
  const prevKeys = Object.keys(prevValues)
  const nextKeys = Object.keys(nextValues)
  
  // 如果键的数量不同，则认为有变化
  if (prevKeys.length !== nextKeys.length) return true
  
  // 检查每个键的值是否相同
  return prevKeys.some(key => {
    const prevValue = prevValues[key]
    const nextValue = nextValues[key]
    
    // 处理数组类型
    if (Array.isArray(prevValue) && Array.isArray(nextValue)) {
      if (prevValue.length !== nextValue.length) return true
      return prevValue.some((val, index) => val !== nextValue[index])
    }
    
    // 处理对象类型
    if (
      typeof prevValue === 'object' && prevValue !== null &&
      typeof nextValue === 'object' && nextValue !== null
    ) {
      return JSON.stringify(prevValue) !== JSON.stringify(nextValue)
    }
    
    // 处理基本类型
    return prevValue !== nextValue
  })
}
```

## 2. 依赖监听 Hook 实现 (src/lib/relation/hooks/useRelationDependency.ts)

```typescript
import { useState, useEffect } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { RelationControl } from '@/features/metadata/types/relation'
import { buildRelationFilter, isDependencyReady, getDependencyValues, hasDependencyChanged } from '../filter'

/**
 * 关联依赖 Hook
 * @param field 字段名
 * @param control 关联控制配置
 * @returns 过滤条件和依赖状态
 */
export const useRelationDependency = (
  field: string,
  control?: RelationControl
): {
  filter: Record<string, unknown>
  isDependencyReady: boolean
  dependencyValues: Record<string, any>
  hasDependencyChanged: boolean
} => {
  const { control: formControl, getValues } = useFormContext()
  const [filter, setFilter] = useState<Record<string, unknown>>({})
  const [isDependencyReadyState, setIsDependencyReady] = useState(false)
  const [dependencyValues, setDependencyValues] = useState<Record<string, any>>({})
  const [prevDependencyValues, setPrevDependencyValues] = useState<Record<string, any>>({})
  const [hasDependencyChangedState, setHasDependencyChanged] = useState(false)
  
  // 获取依赖字段列表
  const dependsOn = control?.filter?.dependsOn || []
  
  // 监听依赖字段变化
  const watchedValues = useWatch({
    control: formControl,
    name: dependsOn,
  })
  
  // 当依赖字段变化时，更新过滤条件
  useEffect(() => {
    if (!control?.filter) {
      setIsDependencyReady(true)
      setFilter({})
      setHasDependencyChanged(false)
      return
    }
    
    // 获取表单值
    const formValues = getValues()
    
    // 构建依赖值对象
    const values = getDependencyValues(dependsOn, formValues)
    
    // 检查依赖是否就绪
    const ready = isDependencyReady(dependsOn, formValues)
    
    // 检查依赖是否变化
    const changed = hasDependencyChanged(prevDependencyValues, values)
    
    setDependencyValues(values)
    setIsDependencyReady(ready)
    setHasDependencyChanged(changed)
    
    // 如果依赖就绪，构建过滤条件
    if (ready) {
      const newFilter = buildRelationFilter(control.filter, formValues)
      setFilter(newFilter)
      
      // 更新上一次的依赖值
      if (changed) {
        setPrevDependencyValues(values)
      }
    }
  }, [watchedValues, control?.filter, dependsOn, getValues])
  
  return {
    filter,
    isDependencyReady: isDependencyReadyState,
    dependencyValues,
    hasDependencyChanged: hasDependencyChangedState,
  }
}

/**
 * 关联依赖值 Hook
 * 简化版的依赖监听，只返回依赖字段的值
 * @param dependsOn 依赖字段列表
 * @returns 依赖字段值
 */
export const useRelationDependencyValues = (
  dependsOn: string[] = []
): Record<string, any> => {
  const { control } = useFormContext()
  const [values, setValues] = useState<Record<string, any>>({})
  
  // 监听依赖字段变化
  const watchedValues = useWatch({
    control,
    name: dependsOn,
  })
  
  // 当依赖字段变化时，更新值
  useEffect(() => {
    const newValues: Record<string, any> = {}
    
    dependsOn.forEach((field, index) => {
      const value = Array.isArray(watchedValues) ? watchedValues[index] : watchedValues
      newValues[field] = value
    })
    
    setValues(newValues)
  }, [watchedValues, dependsOn])
  
  return values
}
```

## 3. 条件过滤测试 (src/lib/relation/__tests__/filter.test.ts)

```typescript
import { describe, it, expect } from 'vitest'
import { buildRelationFilter, isDependencyReady, getDependencyValues, hasDependencyChanged } from '../filter'

describe('关联过滤条件', () => {
  it('应该构建简单条件', () => {
    const filter = {
      condition: '{ where: [{ status: { __EQ: "active" } }] }',
      dependsOn: [],
    }
    
    const result = buildRelationFilter(filter, {})
    
    expect(result).toEqual({
      where: [{ status: { __EQ: 'active' } }],
    })
  })
  
  it('应该构建依赖条件', () => {
    const filter = {
      condition: 'department_id ? { where: [{ department_id: { __EQ: department_id } }] } : {}',
      dependsOn: ['department_id'],
    }
    
    const result = buildRelationFilter(filter, { department_id: '1' })
    
    expect(result).toEqual({
      where: [{ department_id: { __EQ: '1' } }],
    })
  })
  
  it('应该处理多个依赖', () => {
    const filter = {
      condition: 'department_id && status ? { where: [{ department_id: { __EQ: department_id }, status: { __EQ: status } }] } : {}',
      dependsOn: ['department_id', 'status'],
    }
    
    const result = buildRelationFilter(filter, { department_id: '1', status: 'active' })
    
    expect(result).toEqual({
      where: [{ department_id: { __EQ: '1' }, status: { __EQ: 'active' } }],
    })
  })
  
  it('应该检查依赖是否就绪', () => {
    expect(isDependencyReady(['department_id'], { department_id: '1' })).toBe(true)
    expect(isDependencyReady(['department_id'], { department_id: null })).toBe(false)
    expect(isDependencyReady(['department_id'], {})).toBe(false)
    expect(isDependencyReady([], {})).toBe(true)
    expect(isDependencyReady(undefined, {})).toBe(true)
  })
  
  it('应该获取依赖字段值', () => {
    expect(getDependencyValues(['department_id', 'status'], { department_id: '1', status: 'active', name: 'test' }))
      .toEqual({ department_id: '1', status: 'active' })
    expect(getDependencyValues([], { department_id: '1' })).toEqual({})
    expect(getDependencyValues(undefined, { department_id: '1' })).toEqual({})
  })
  
  it('应该检查依赖是否变化', () => {
    expect(hasDependencyChanged({ department_id: '1' }, { department_id: '2' })).toBe(true)
    expect(hasDependencyChanged({ department_id: '1' }, { department_id: '1' })).toBe(false)
    expect(hasDependencyChanged({ department_id: '1', status: 'active' }, { department_id: '1', status: 'inactive' })).toBe(true)
    expect(hasDependencyChanged({ department_id: '1' }, { status: 'active' })).toBe(true)
    expect(hasDependencyChanged({}, {})).toBe(false)
  })
})
```
