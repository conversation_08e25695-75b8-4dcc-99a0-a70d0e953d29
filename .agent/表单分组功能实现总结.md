# 表单分组功能实现总结

## 1. 功能概述

实现了表单控件的分组功能，支持通过 `control.layout.group` 属性将表单字段分组展示，提高表单的可读性和用户体验。分组功能使用
Card 组件将同一组的字段包装在一起，每个分组有独立的标题和内容区域。

## 2. 实现内容

### 2.1 新增组件

1. **FieldGroup 组件**：
    - 路径：`src/features/entity/components/FieldGroup.tsx`
    - 功能：将同一组的字段包装在一个卡片中，包含标题和内容区域
    - 实现：使用 Card 组件包装字段，保持原有的网格布局

### 2.2 新增工具函数

1. **groupFields 函数**：
    - 路径：`src/features/entity/utils/groupUtils.ts`
    - 功能：将字段按照 `control.layout.group` 属性分组，未指定分组的字段归入默认分组
    - 实现：遍历字段列表，按照分组进行分类，并对每个分组内的字段按照 `order` 属性排序

2. **getGroupOrder 函数**：
    - 路径：`src/features/entity/utils/groupUtils.ts`
    - 功能：获取分组顺序，将默认分组放在最前面，其他分组按字母顺序排序
    - 实现：使用数组过滤和排序方法实现

### 2.3 修改现有组件

1. **EntityForm 组件**：
    - 路径：`src/features/entity/components/EntityForm.tsx`
    - 修改内容：使用分组逻辑渲染字段，替换原有的字段渲染逻辑
    - 实现：将字段按照分组进行分类，然后按照分组顺序渲染各个分组

### 2.4 示例和测试

1. **示例页面**：
    - 路径：`src/pages/examples/group-form.tsx`
    - 功能：展示表单分组功能的示例，包含产品表单和客户表单两个示例
    - 实现：使用 Tabs 组件切换不同的表单示例

2. **示例组件**：
    - 路径：`src/examples/group-form-example.tsx`
    - 功能：实现分组表单示例，包含产品表单和客户表单两个示例
    - 实现：使用测试元数据创建表单，展示分组效果

3. **测试数据**：
    - 路径：`.agent/mock-group-form-metadata.ts`
    - 功能：提供测试用的实体元数据，包含多个分组的字段
    - 实现：创建产品实体和客户实体两个测试元数据

4. **测试脚本**：
    - 路径：`src/features/entity/utils/groupUtils.test.ts`
    - 功能：测试分组工具函数的功能
    - 实现：使用 vitest 编写单元测试，测试分组和排序功能

5. **路由配置**：
    - 路径：`src/routes/_authenticated/examples/group-form/route.tsx`
    - 功能：配置表单分组示例的路由
    - 实现：使用 TanStack Router 创建路由

## 3. 功能特点

1. **灵活的分组配置**：
    - 通过 `control.layout.group` 属性指定字段所属的分组
    - 未指定分组的字段自动归入默认分组（"基本信息"）
    - 支持自定义默认分组名称

2. **分组内字段排序**：
    - 通过 `control.layout.order` 属性指定字段在分组内的顺序
    - 未指定顺序的字段默认排在后面

3. **分组顺序控制**：
    - 默认分组始终显示在最前面
    - 其他分组按字母顺序排序
    - 支持自定义分组顺序

4. **与现有功能兼容**：
    - 保持与字段联动机制的兼容
    - 保持与字段可见性控制的兼容
    - 保持与字段跨度（span）设置的兼容

## 4. 使用示例

### 4.1 字段元数据配置

```typescript
// 基本信息组
{
  name: 'name',
  display_name: '名称',
  type: 'VARCHAR',
  default_value: null,
  flags: [],
  _id: 'name_id',
  control: {
    layout: {
      group: '基本信息',
      order: 1
    }
  }
}

// 详细信息组
{
  name: 'description',
  display_name: '描述',
  type: 'TEXT',
  default_value: null,
  flags: ['NULLABLE'],
  _id: 'description_id',
  control: {
    layout: {
      group: '详细信息',
      order: 1,
      span: 2
    }
  }
}
```

### 4.2 分组与联动结合

```typescript
// 企业信息组（条件显示）
{
  name: 'company_name',
  display_name: '公司名称',
  type: 'VARCHAR',
  default_value: '',
  flags: ['NULLABLE'],
  _id: 'company_name_id',
  control: {
    layout: {
      group: '企业信息',
      order: 1
    },
    linkage: [
      {
        sources: [
          { type: 'field', name: 'customer_type' }
        ],
        effect: 'setVisibility',
        expression: "customer_type === 'business'"
      }
    ]
  }
}
```

## 5. 后续优化方向

1. **分组折叠功能**：
    - 支持折叠/展开分组，减少长表单的视觉复杂度
    - 记住用户的折叠状态，提高用户体验

2. **分组顺序自定义**：
    - 支持通过配置指定分组的显示顺序
    - 实现分组的拖拽排序功能

3. **分组样式自定义**：
    - 支持自定义分组的样式，如背景色、边框等
    - 支持为不同分组设置不同的图标或标识

4. **响应式优化**：
    - 在小屏幕设备上优化分组的显示方式
    - 考虑在移动设备上使用手风琴（Accordion）组件替代卡片

5. **分组级联动**：
    - 支持整个分组的条件显示
    - 支持分组之间的联动关系
