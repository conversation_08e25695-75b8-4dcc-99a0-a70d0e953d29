# 表单分组折叠功能实现总结

## 1. 功能概述

在原有表单分组功能的基础上，实现了分组的折叠/展开功能，提高表单的可用性和用户体验。主要功能包括：

1. 分组标题和折叠按钮：每个分组的标题区域可点击，用于折叠/展开分组内容
2. 折叠状态指示：通过箭头图标直观地指示分组的折叠状态
3. 默认折叠状态：可以设置分组的默认折叠状态，默认只展开第一个分组
4. 全局折叠控制：提供"展开全部"和"折叠全部"按钮，一键控制所有分组的折叠状态

## 2. 实现内容

### 2.1 修改 FieldGroup 组件

修改 `FieldGroup` 组件，使用 Shadcn UI 的 `Collapsible` 组件实现折叠功能：

```tsx
export function FieldGroup({ 
  title, 
  fields, 
  readOnly = false, 
  defaultOpen = true 
}: FieldGroupProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <Card className="mb-6">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="pb-3 cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
          <div className="flex items-center justify-between">
            <CardTitle>{title}</CardTitle>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {isOpen ? '收起' : '展开'} {title}
                </span>
              </Button>
            </CollapsibleTrigger>
          </div>
        </CardHeader>
        <CollapsibleContent>
          <CardContent>
            {/* 字段内容 */}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
```

### 2.2 修改 EntityForm 组件

修改 `EntityForm` 组件，添加分组折叠状态管理和全局折叠控制：

```tsx
// 管理分组折叠状态
const [groupOpenState, setGroupOpenState] = useState<Record<string, boolean>>(() => {
  // 默认情况下，只有第一个分组是展开的，其他分组是折叠的
  const initialState: Record<string, boolean> = {};
  groupOrder.forEach((groupName, index) => {
    initialState[groupName] = index === 0; // 只有第一个分组默认展开
  });
  return initialState;
});

// 切换所有分组的折叠状态
const toggleAllGroups = (isOpen: boolean) => {
  const newState: Record<string, boolean> = {};
  groupOrder.forEach(groupName => {
    newState[groupName] = isOpen;
  });
  setGroupOpenState(newState);
};

// 展开所有分组
const expandAllGroups = () => toggleAllGroups(true);

// 折叠所有分组
const collapseAllGroups = () => toggleAllGroups(false);
```

添加全局折叠控制按钮：

```tsx
{/* 分组折叠控制按钮 */}
{groupOrder.length > 1 && (
  <div className="flex justify-end space-x-2 mb-4">
    <button
      type="button"
      className="text-sm text-muted-foreground hover:text-foreground"
      onClick={expandAllGroups}
    >
      展开全部
    </button>
    <span className="text-muted-foreground">|</span>
    <button
      type="button"
      className="text-sm text-muted-foreground hover:text-foreground"
      onClick={collapseAllGroups}
    >
      折叠全部
    </button>
  </div>
)}
```

### 2.3 更新示例组件

更新 `GroupFormExample` 和 `CustomerGroupFormExample` 组件，添加分组折叠功能，展示如何使用折叠功能。

## 3. 功能特点

1. **直观的用户界面**：
    - 使用箭头图标直观地指示分组的折叠状态
    - 分组标题区域可点击，提高用户体验
    - 折叠/展开动画平滑，提供良好的视觉反馈

2. **灵活的折叠控制**：
    - 支持单个分组的折叠/展开
    - 支持全局折叠/展开控制
    - 支持设置默认折叠状态

3. **优化的表单体验**：
    - 减少长表单的视觉复杂度
    - 帮助用户聚焦于当前操作的表单部分
    - 提高大型表单的可用性

4. **与现有功能兼容**：
    - 保持与字段联动机制的兼容
    - 保持与字段可见性控制的兼容
    - 保持与字段跨度（span）设置的兼容

## 4. 使用示例

### 4.1 基本使用

```tsx
<FieldGroup
  title="基本信息"
  fields={fields}
  readOnly={false}
  defaultOpen={true}
/>
```

### 4.2 与分组状态管理结合

```tsx
// 管理分组折叠状态
const [groupOpenState, setGroupOpenState] = useState<Record<string, boolean>>({
  '基本信息': true,
  '详细信息': false,
  '价格库存': false
});

// 渲染分组
{groupOrder.map(groupName => (
  <FieldGroup
    key={groupName}
    title={groupName}
    fields={fieldGroups[groupName]}
    readOnly={false}
    defaultOpen={groupOpenState[groupName]}
  />
))}
```

## 5. 后续优化方向

1. **记住用户的折叠状态**：
    - 使用 localStorage 或其他状态持久化方案记住用户的折叠偏好
    - 在用户再次访问表单时恢复之前的折叠状态

2. **分组级联动**：
    - 支持整个分组的条件显示
    - 支持分组之间的联动关系

3. **分组样式自定义**：
    - 支持自定义分组的样式，如背景色、边框等
    - 支持为不同分组设置不同的图标或标识

4. **键盘导航支持**：
    - 支持使用键盘导航和操作分组
    - 提高表单的无障碍性
