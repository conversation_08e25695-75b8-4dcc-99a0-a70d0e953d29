# 低代码平台API设计

## 1. API设计原则

### 1.1 设计理念

- **一致性**：所有API遵循统一的设计规范和命名约定
- **简洁性**：API接口简洁明了，易于理解和使用
- **可扩展性**：支持未来功能扩展，不破坏现有接口
- **安全性**：内置安全机制，防止未授权访问和攻击
- **性能优化**：优化API响应速度和资源消耗

### 1.2 技术选择

- **GraphQL**：作为主要API技术，提供灵活的数据查询和操作能力
- **RESTful API**：作为补充，提供简单直观的资源操作接口
- **WebSocket**：用于实时数据更新和通知

## 2. GraphQL API

### 2.1 Schema设计

GraphQL Schema是API的核心，定义了可查询的数据类型和操作。

#### 2.1.1 基础类型

```graphql
# 基础标量类型
scalar ID
scalar String
scalar Int
scalar Float
scalar Boolean
scalar DateTime
scalar JSON

# 分页输入
input PageInput {
  page: Int!
  pageSize: Int!
}

# 分页结果
type PageInfo {
  total: Int!
  page: Int!
  pageSize: Int!
  hasNext: Boolean!
}
```

#### 2.1.2 元数据类型

```graphql
# 实体定义
type DefEntity {
  _id: ID!
  name: String!
  module: String!
  displayName: String!
  description: String
  icon: String
  color: String
  group: String
  isSystem: Boolean!
  fields: [DefField!]!
  relations: [DefRelation!]!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 字段定义
type DefField {
  _id: ID!
  entityId: ID!
  name: String!
  displayName: String!
  type: String!
  defaultValue: String
  isRequired: Boolean!
  isUnique: Boolean!
  minLength: Int
  maxLength: Int
  minValue: Float
  maxValue: Float
  pattern: String
  format: String
  helpText: String
  placeholder: String
  isSystem: Boolean!
  options: [DefOption!]
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 实体关系
type DefRelation {
  _id: ID!
  name: String!
  sourceEntityId: ID!
  targetEntityId: ID!
  relationType: String!
  sourceFieldId: ID!
  targetFieldId: ID!
  junctionTable: String
  cascadeDelete: Boolean!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 字段选项
type DefOption {
  _id: ID!
  fieldId: ID!
  label: String!
  value: String!
  order: Int!
  color: String
  icon: String
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}
```

#### 2.1.3 表单类型

```graphql
# 表单定义
type FormDef {
  _id: ID!
  name: String!
  displayName: String!
  description: String
  entityId: ID!
  version: Int!
  status: String!
  layoutType: String!
  submitButtonText: String
  cancelButtonText: String
  successMessage: String
  fields: [FormField!]!
  layouts: [FormLayout!]!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 表单字段
type FormField {
  _id: ID!
  formId: ID!
  fieldId: ID
  name: String!
  displayName: String!
  controlType: String!
  sectionId: ID
  tabId: ID
  order: Int!
  width: String
  isVisible: Boolean!
  isEditable: Boolean!
  isRequired: Boolean!
  defaultValue: String
  helpText: String
  placeholder: String
  validationRules: JSON
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 表单布局
type FormLayout {
  _id: ID!
  formId: ID!
  type: String!
  parentId: ID
  name: String!
  displayName: String!
  order: Int!
  width: String
  height: String
  cssClass: String
  cssStyle: String
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}
```

#### 2.1.4 页面类型

```graphql
# 页面定义
type PageDef {
  _id: ID!
  name: String!
  displayName: String!
  description: String
  type: String!
  version: Int!
  status: String!
  routePath: String!
  icon: String
  isHome: Boolean!
  components: [PageComponent!]!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 页面组件
type PageComponent {
  _id: ID!
  pageId: ID!
  parentId: ID
  componentType: String!
  name: String!
  displayName: String!
  order: Int!
  x: Int
  y: Int
  width: String
  height: String
  dataSource: JSON
  properties: JSON
  events: JSON
  cssClass: String
  cssStyle: String
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}
```

#### 2.1.5 工作流类型

```graphql
# 工作流定义
type FlowDef {
  _id: ID!
  name: String!
  displayName: String!
  description: String
  entityId: ID!
  version: Int!
  status: String!
  startNodeId: ID!
  nodes: [FlowNode!]!
  transitions: [FlowTransition!]!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 工作流节点
type FlowNode {
  _id: ID!
  flowId: ID!
  name: String!
  displayName: String!
  nodeType: String!
  formId: ID
  assigneeType: String
  assignee: String
  timeout: Int
  timeoutAction: String
  x: Int!
  y: Int!
  properties: JSON
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}

# 工作流转换
type FlowTransition {
  _id: ID!
  flowId: ID!
  sourceNodeId: ID!
  targetNodeId: ID!
  name: String!
  condition: String
  order: Int!
  _createBy: String
  _createAt: DateTime!
  _updateBy: String
  _updateAt: DateTime!
}
```

### 2.2 查询操作

```graphql
type Query {
  # 元数据查询
  entity(id: ID!): DefEntity
  entities(module: String, page: PageInput): [DefEntity!]!
  entitiesCount(module: String): Int!
  
  # 表单查询
  form(id: ID!): FormDef
  forms(entityId: ID, page: PageInput): [FormDef!]!
  formsCount(entityId: ID): Int!
  
  # 页面查询
  page(id: ID!): PageDef
  pages(type: String, page: PageInput): [PageDef!]!
  pagesCount(type: String): Int!
  
  # 工作流查询
  flow(id: ID!): FlowDef
  flows(entityId: ID, page: PageInput): [FlowDef!]!
  flowsCount(entityId: ID): Int!
  
  # 数据查询
  record(entityName: String!, id: ID!): JSON
  records(entityName: String!, condition: JSON, page: PageInput): [JSON!]!
  recordsCount(entityName: String!, condition: JSON): Int!
}
```

### 2.3 变更操作

```graphql
type Mutation {
  # 元数据操作
  createEntity(input: EntityInput!): DefEntity!
  updateEntity(id: ID!, input: EntityInput!): DefEntity!
  deleteEntity(id: ID!): Boolean!
  
  # 表单操作
  createForm(input: FormInput!): FormDef!
  updateForm(id: ID!, input: FormInput!): FormDef!
  deleteForm(id: ID!): Boolean!
  publishForm(id: ID!): FormDef!
  
  # 页面操作
  createPage(input: PageInput!): PageDef!
  updatePage(id: ID!, input: PageInput!): PageDef!
  deletePage(id: ID!): Boolean!
  publishPage(id: ID!): PageDef!
  
  # 工作流操作
  createFlow(input: FlowInput!): FlowDef!
  updateFlow(id: ID!, input: FlowInput!): FlowDef!
  deleteFlow(id: ID!): Boolean!
  publishFlow(id: ID!): FlowDef!
  
  # 数据操作
  createRecord(entityName: String!, data: JSON!): JSON!
  updateRecord(entityName: String!, id: ID!, data: JSON!): JSON!
  deleteRecord(entityName: String!, id: ID!): Boolean!
}
```

### 2.4 订阅操作

```graphql
type Subscription {
  # 元数据变更通知
  entityChanged(id: ID): DefEntity!
  
  # 表单变更通知
  formChanged(id: ID): FormDef!
  
  # 页面变更通知
  pageChanged(id: ID): PageDef!
  
  # 工作流变更通知
  flowChanged(id: ID): FlowDef!
  
  # 数据变更通知
  recordChanged(entityName: String!, id: ID): JSON!
}
```

## 3. RESTful API

除了GraphQL API，平台还提供RESTful API作为补充，主要用于简单的CRUD操作和文件上传下载等场景。

### 3.1 API端点

#### 3.1.1 元数据API

- `GET /api/meta/entities` - 获取实体列表
- `GET /api/meta/entities/{id}` - 获取实体详情
- `POST /api/meta/entities` - 创建实体
- `PUT /api/meta/entities/{id}` - 更新实体
- `DELETE /api/meta/entities/{id}` - 删除实体

#### 3.1.2 表单API

- `GET /api/forms` - 获取表单列表
- `GET /api/forms/{id}` - 获取表单详情
- `POST /api/forms` - 创建表单
- `PUT /api/forms/{id}` - 更新表单
- `DELETE /api/forms/{id}` - 删除表单
- `POST /api/forms/{id}/publish` - 发布表单

#### 3.1.3 页面API

- `GET /api/pages` - 获取页面列表
- `GET /api/pages/{id}` - 获取页面详情
- `POST /api/pages` - 创建页面
- `PUT /api/pages/{id}` - 更新页面
- `DELETE /api/pages/{id}` - 删除页面
- `POST /api/pages/{id}/publish` - 发布页面

#### 3.1.4 工作流API

- `GET /api/flows` - 获取工作流列表
- `GET /api/flows/{id}` - 获取工作流详情
- `POST /api/flows` - 创建工作流
- `PUT /api/flows/{id}` - 更新工作流
- `DELETE /api/flows/{id}` - 删除工作流
- `POST /api/flows/{id}/publish` - 发布工作流

#### 3.1.5 数据API

- `GET /api/data/{entityName}` - 获取实体数据列表
- `GET /api/data/{entityName}/{id}` - 获取实体数据详情
- `POST /api/data/{entityName}` - 创建实体数据
- `PUT /api/data/{entityName}/{id}` - 更新实体数据
- `DELETE /api/data/{entityName}/{id}` - 删除实体数据

#### 3.1.6 文件API

- `GET /api/files` - 获取文件列表
- `GET /api/files/{id}` - 下载文件
- `POST /api/files` - 上传文件
- `DELETE /api/files/{id}` - 删除文件

### 3.2 请求参数

#### 3.2.1 查询参数

- `page` - 页码，默认为1
- `pageSize` - 每页记录数，默认为10
- `sort` - 排序字段，格式为`field,asc|desc`
- `filter` - 过滤条件，JSON格式

#### 3.2.2 请求体

请求体采用JSON格式，字段与GraphQL输入类型一致。

### 3.3 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  },
  "page": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "hasNext": true
  }
}
```

## 4. WebSocket API

WebSocket API用于实时数据更新和通知，主要包括以下功能：

### 4.1 连接建立

- `ws://host:port/ws?token={token}` - 建立WebSocket连接

### 4.2 消息类型

#### 4.2.1 订阅消息

```json
{
  "type": "subscribe",
  "channel": "entity_changed",
  "params": {
    "id": "entity_id"
  }
}
```

#### 4.2.2 取消订阅消息

```json
{
  "type": "unsubscribe",
  "channel": "entity_changed",
  "params": {
    "id": "entity_id"
  }
}
```

#### 4.2.3 通知消息

```json
{
  "type": "notification",
  "channel": "entity_changed",
  "data": {
    // 实体数据
  },
  "timestamp": 1621234567890
}
```

## 5. API安全

### 5.1 认证方式

- **JWT认证**：使用JWT进行API认证
- **API密钥**：使用API密钥进行服务间认证
- **OAuth2.0**：支持OAuth2.0认证流程

### 5.2 授权控制

- **基于角色的访问控制**：根据用户角色控制API访问权限
- **基于资源的访问控制**：根据资源所有权控制API访问权限
- **基于操作的访问控制**：根据操作类型控制API访问权限

### 5.3 安全措施

- **HTTPS**：所有API通过HTTPS传输
- **CSRF防护**：使用CSRF令牌防止跨站请求伪造
- **速率限制**：限制API调用频率，防止滥用
- **输入验证**：严格验证所有输入参数，防止注入攻击

## 6. API版本控制

### 6.1 版本策略

- **URL版本**：在URL中包含版本号，如`/api/v1/entities`
- **请求头版本**：在请求头中指定版本，如`Accept: application/vnd.api+json;version=1.0`

### 6.2 兼容性保证

- **向后兼容**：新版本API保持对旧版本客户端的兼容性
- **废弃通知**：API废弃前提供充分的通知和过渡期
- **变更文档**：详细记录API变更，帮助客户端适配

## 7. API文档

### 7.1 文档工具

- **Swagger/OpenAPI**：使用OpenAPI规范生成RESTful API文档
- **GraphQL Playground**：提供GraphQL API交互式文档和测试工具

### 7.2 文档内容

- **API概述**：API的总体说明和使用指南
- **端点详情**：每个端点的详细说明，包括参数、响应和示例
- **错误码**：所有可能的错误码及其含义
- **认证授权**：API认证和授权的详细说明
- **最佳实践**：API使用的最佳实践和建议
