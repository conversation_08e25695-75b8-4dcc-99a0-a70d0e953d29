# 表单字段控件联动规则使用文档

## 1. 概述

> **重要说明：** 我们现在统一使用 `control.linkage` 方式实现字段联动，不再支持 `control.visibility` 方式。如果需要控制字段的可见性，请使用
`linkage` 中的 `setVisibility` 效果。

表单字段控件联动规则是一种强大的功能，允许开发者定义字段之间的动态交互关系，提高表单的灵活性和用户体验。通过联动规则，可以实现以下功能：

- 根据某个字段的值显示或隐藏其他字段
- 根据某个字段的值设置其他字段的值
- 根据某个字段的值更新其他字段的选项列表
- 根据某个字段的值设置其他字段的必填状态
- 根据某个字段的值设置其他字段的禁用状态
- 根据某个字段的值设置其他字段的验证规则
- 根据某个字段的值设置其他字段的控件属性

## 2. 联动规则数据结构

联动规则定义在字段的 `control.linkage` 属性中，是一个数组，每个元素代表一条联动规则：

```typescript
interface LinkageRule {
  id?: string               // 规则唯一标识
  priority?: number         // 规则优先级，数字越小优先级越高
  target: string            // 目标字段
  effect: LinkageEffectType // 联动效果类型
  condition?: string        // 触发条件
  dependsOn?: string[]      // 显式依赖的字段（可选，如果不提供会自动从条件表达式中提取）
  mapping?: Record<string, any> // 值映射
  value?: any               // 静态值
  expression?: string       // 动态表达式
  props?: Record<string, any>   // 控件属性
}

// 联动效果类型
type LinkageEffectType =
  | 'setValue'      // 设置值
  | 'setOptions'    // 设置选项
  | 'setVisibility' // 设置可见性
  | 'setRequired'   // 设置是否必填
  | 'setDisabled'   // 设置是否禁用
  | 'setValidation' // 设置验证规则
  | 'setProps'      // 设置控件属性
  | 'custom'        // 自定义效果
```

## 3. 联动效果类型说明

### 3.1 setValue - 设置值

设置目标字段的值。可以使用静态值、映射或表达式。

```typescript
// 示例1：使用静态值
{
  target: 'targetField',
  effect: 'setValue',
  value: 'defaultValue'
}

// 示例2：使用映射
{
  target: 'targetField',
  effect: 'setValue',
  mapping: {
    'option1': 'value1',
    'option2': 'value2'
  }
}

// 示例3：使用表达式
{
  target: 'totalPrice',
  effect: 'setValue',
  expression: 'quantity * unitPrice'
}
```

### 3.2 setOptions - 设置选项

设置目标字段的选项列表。通常与下拉选择框等控件一起使用。

```typescript
// 示例：根据省份设置城市选项
{
  target: 'city',
  effect: 'setOptions',
  mapping: {
    '北京': [{ label: '北京市', value: '北京市' }],
    '上海': [{ label: '上海市', value: '上海市' }],
    '广东': [
      { label: '广州市', value: '广州市' },
      { label: '深圳市', value: '深圳市' }
    ]
  }
}
```

### 3.3 setVisibility - 设置可见性

控制目标字段的显示或隐藏。

```typescript
// 示例：根据支付方式显示或隐藏信用卡号字段
{
  target: 'creditCardNumber',
  effect: 'setVisibility',
  condition: "paymentMethod === 'creditCard'"
}
```

### 3.4 setRequired - 设置必填状态

动态设置目标字段是否必填。

```typescript
// 示例：当选择有附件时，附件描述为必填
{
  target: 'attachmentDescription',
  effect: 'setRequired',
  condition: 'hasAttachment === true',
  value: true
}
```

### 3.5 setDisabled - 设置禁用状态

动态设置目标字段是否禁用。

```typescript
// 示例：当选择默认值时，禁用自定义值字段
{
  target: 'customValue',
  effect: 'setDisabled',
  condition: 'isDefault === true',
  value: true
}
```

### 3.6 setProps - 设置控件属性

动态设置目标字段控件的属性。

```typescript
// 示例：根据字段类型设置不同的控件属性
{
  target: 'fieldValue',
  effect: 'setProps',
  condition: "fieldType === 'number'",
  props: {
    min: 0,
    max: 100,
    step: 1
  }
}
```

### 3.7 setValidation - 设置验证规则

动态设置目标字段的验证规则。

```typescript
// 示例：根据字段类型设置不同的验证规则
{
  target: 'fieldValue',
  effect: 'setValidation',
  condition: "fieldType === 'email'",
  value: {
    pattern: {
      value: /^[^@]+@[^@]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的电子邮件地址'
    }
  }
}
```

### 3.8 custom - 自定义效果

执行自定义函数。

```typescript
// 示例：执行自定义函数
{
  target: 'customField',
  effect: 'custom',
  value: (target, formValues) => {
    // 自定义逻辑
    console.log(`Custom effect on ${target}`, formValues);
  }
}
```

## 4. 条件表达式

条件表达式是一个JavaScript表达式字符串，用于决定是否应用联动规则。表达式中可以引用表单中的其他字段值。

```typescript
// 示例：简单条件
"paymentMethod === 'creditCard'"

// 示例：复合条件
"age > 18 && country === 'China'"

// 示例：使用辅助函数
"startsWith(email, 'admin')"
```

### 4.1 可用的辅助函数

表达式引擎内置了一些辅助函数，可以在条件表达式中使用：

- 字符串操作
    - `startsWith(str, search)`: 检查字符串是否以指定内容开头
    - `endsWith(str, search)`: 检查字符串是否以指定内容结尾
    - `contains(str, search)`: 检查字符串是否包含指定内容

- 数组操作
    - `includes(arr, item)`: 检查数组是否包含指定元素
    - `some(arr, predicate)`: 检查数组是否有元素满足条件
    - `every(arr, predicate)`: 检查数组是否所有元素都满足条件

- 日期操作
    - `dateCompare(date1, date2)`: 比较两个日期的大小

- 数字操作
    - `round(num, decimals)`: 四舍五入到指定小数位

## 5. 使用示例

### 5.1 地址级联选择

```typescript
// 省份字段
{
  name: 'province',
  display_name: '省份',
  type: 'VARCHAR',
  control: {
    type: 'select',
    linkage: [
      {
        target: 'city',
        effect: 'setOptions',
        mapping: {
          '北京': [{ label: '北京市', value: '北京市' }],
          '上海': [{ label: '上海市', value: '上海市' }],
          '广东': [
            { label: '广州市', value: '广州市' },
            { label: '深圳市', value: '深圳市' }
          ]
        }
      },
      {
        target: 'city',
        effect: 'setValue',
        value: '' // 清空城市选择
      }
    ]
  }
}

// 城市字段
{
  name: 'city',
  display_name: '城市',
  type: 'VARCHAR',
  control: {
    type: 'select'
  }
}
```

### 5.2 条件显示字段

```typescript
// 支付方式字段
{
  name: 'paymentMethod',
  display_name: '支付方式',
  type: 'VARCHAR',
  control: {
    type: 'select',
    props: {
      options: [
        { label: '信用卡', value: 'creditCard' },
        { label: '银行转账', value: 'bankTransfer' },
        { label: '其他', value: 'other' }
      ]
    },
    linkage: [
      {
        target: 'creditCardNumber',
        effect: 'setVisibility',
        condition: "paymentMethod === 'creditCard'"
      },
      {
        target: 'bankAccount',
        effect: 'setVisibility',
        condition: "paymentMethod === 'bankTransfer'"
      },
      {
        target: 'otherPaymentDetails',
        effect: 'setVisibility',
        condition: "paymentMethod === 'other'"
      }
    ]
  }
}
```

### 5.3 动态计算字段

```typescript
// 数量字段
{
  name: 'quantity',
  display_name: '数量',
  type: 'INT',
  control: {
    type: 'number',
    linkage: [
      {
        target: 'totalPrice',
        effect: 'setValue',
        expression: 'quantity * unitPrice',
        dependsOn: ['unitPrice']
      }
    ]
  }
}

// 单价字段
{
  name: 'unitPrice',
  display_name: '单价',
  type: 'FLOAT',
  control: {
    type: 'number',
    linkage: [
      {
        target: 'totalPrice',
        effect: 'setValue',
        expression: 'quantity * unitPrice',
        dependsOn: ['quantity']
      }
    ]
  }
}

// 总价字段
{
  name: 'totalPrice',
  display_name: '总价',
  type: 'FLOAT',
  control: {
    type: 'number',
    props: {
      readOnly: true
    }
  }
}
```

## 6. 调试联动规则

系统提供了联动调试工具，可以帮助开发者调试联动规则：

```typescript
// 启用联动调试
import { LinkageDebugger } from '@/lib/linkage';
LinkageDebugger.enable();

// 获取执行日志
const logs = LinkageDebugger.getExecutionLog();

// 获取特定字段的日志
const fieldLogs = LinkageDebugger.getFieldLog('fieldName');

// 分析联动链
const analysis = LinkageDebugger.analyzeLinkageChain(rules);
```

## 7. 最佳实践

1. **统一使用 linkage 方式**：始终使用 `control.linkage` 方式实现字段联动，包括可见性控制。不要使用已弃用的
   `control.visibility` 方式。

2. **避免循环依赖**：确保联动规则不会形成循环依赖，否则可能导致无限循环。当字段需要引用自身时，请显式指定空的 `dependsOn`
   数组。

3. **优先使用条件表达式**：尽量使用条件表达式而不是显式依赖字段，系统会自动提取依赖。

4. **合理设置优先级**：当多个规则作用于同一个目标字段时，使用优先级控制执行顺序。

5. **使用表达式计算值**：对于需要计算的值，使用表达式而不是静态值。

6. **分组相关联动规则**：将相关的联动规则放在一起，便于维护。

7. **使用调试工具**：在开发过程中使用调试工具检查联动规则的执行情况。

8. **测试边界情况**：测试各种输入组合，确保联动规则在各种情况下都能正常工作。

## 8. 注意事项

1. 联动规则的执行顺序是按照优先级排序的，优先级相同时按照定义顺序执行。

2. 联动规则的条件表达式中可以引用表单中的任何字段值。

3. 联动规则的表达式中不应该有副作用，应该是纯函数。

4. 联动规则的依赖字段会自动从条件表达式中提取，但也可以显式指定。

5. 联动规则的目标字段可以是当前字段自身，实现自我联动。

6. 联动规则的映射值可以是任何类型，包括对象、数组等。

7. 联动规则的表达式可以使用内置的辅助函数，也可以注册自定义辅助函数。
