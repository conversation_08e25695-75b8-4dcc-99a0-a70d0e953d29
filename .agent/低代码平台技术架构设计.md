# 低代码平台技术架构设计

## 1. 总体架构

### 1.1 架构概述

低代码平台采用分层架构设计，主要包括以下几层：

```
+---------------------------+
|        前端应用层         |
+---------------------------+
|        API 网关层         |
+---------------------------+
|        业务服务层         |
+---------------------------+
|        核心引擎层         |
+---------------------------+
|        数据存储层         |
+---------------------------+
```

- **前端应用层**：包括设计器（表单、页面、流程）和运行时应用
- **API 网关层**：提供统一的 API 入口，处理认证、授权、路由等
- **业务服务层**：实现具体业务功能，如用户管理、权限管理等
- **核心引擎层**：提供低代码平台的核心能力，如元数据管理、表单引擎、工作流引擎等
- **数据存储层**：负责数据持久化，包括关系型数据库、缓存等

### 1.2 系统组件

```
+----------------------------------------------------------+
|                      设计时组件                           |
|  +----------------+  +----------------+  +-------------+  |
|  |  表单设计器     |  |  页面设计器     |  | 流程设计器   |  |
|  +----------------+  +----------------+  +-------------+  |
+----------------------------------------------------------+
                           |
                           v
+----------------------------------------------------------+
|                      核心引擎组件                         |
|  +----------------+  +----------------+  +-------------+  |
|  |  元数据引擎     |  |  表单引擎      |  | 工作流引擎   |  |
|  +----------------+  +----------------+  +-------------+  |
|  +----------------+  +----------------+  +-------------+  |
|  |  规则引擎       |  |  脚本引擎      |  | 权限引擎    |  |
|  +----------------+  +----------------+  +-------------+  |
+----------------------------------------------------------+
                           |
                           v
+----------------------------------------------------------+
|                      运行时组件                           |
|  +----------------+  +----------------+  +-------------+  |
|  |  表单运行时     |  |  页面运行时     |  | 流程运行时  |  |
|  +----------------+  +----------------+  +-------------+  |
+----------------------------------------------------------+
```

## 2. 核心模块设计

### 2.1 元数据管理模块

#### 2.1.1 模块职责

- 管理所有低代码平台的元数据定义
- 提供元数据的CRUD操作
- 支持元数据版本控制和迁移
- 实现元数据的导入导出

#### 2.1.2 核心数据结构

```
+---------------+       +---------------+       +---------------+
|   DefEntity   |------>|   DefField    |------>|   DefOption   |
+---------------+       +---------------+       +---------------+
        |                      |
        v                      v
+---------------+       +---------------+
|   DefRule     |       | FieldValidation|
+---------------+       +---------------+
```

#### 2.1.3 关键接口

- `MetaManager`：元数据管理的核心类，提供元数据的注册和查询功能
- `IMetaRegister`：元数据注册接口，用于注册实体定义
- `MetaExtRegistry`：元数据扩展注册，用于扩展元数据的GraphQL能力

### 2.2 表单引擎模块

#### 2.2.1 模块职责

- 管理表单定义和表单控件
- 处理表单数据的验证和转换
- 支持表单数据的提交和保存
- 实现表单的版本控制和发布

#### 2.2.2 核心数据结构

```
+---------------+       +---------------+       +---------------+
|   FormDef     |------>|   FormField   |------>| FormValidator |
+---------------+       +---------------+       +---------------+
        |                      |
        v                      v
+---------------+       +---------------+
|   FormLayout  |       |  FormAction   |
+---------------+       +---------------+
```

#### 2.2.3 关键接口

- `FormEngine`：表单引擎核心类，负责表单的加载和处理
- `IFormRenderer`：表单渲染接口，用于生成表单的前端描述
- `IFormValidator`：表单验证接口，用于验证表单数据

### 2.3 页面引擎模块

#### 2.3.1 模块职责

- 管理页面布局和组件
- 处理页面的渲染和交互
- 支持页面的版本控制和发布
- 实现页面与数据的绑定

#### 2.3.2 核心数据结构

```
+---------------+       +---------------+       +---------------+
|   PageDef     |------>| PageComponent |------>| ComponentProp |
+---------------+       +---------------+       +---------------+
        |                      |
        v                      v
+---------------+       +---------------+
|   PageLayout  |       |  PageAction   |
+---------------+       +---------------+
```

#### 2.3.3 关键接口

- `PageEngine`：页面引擎核心类，负责页面的加载和处理
- `IPageRenderer`：页面渲染接口，用于生成页面的前端描述
- `IComponentRegistry`：组件注册接口，用于注册自定义组件

### 2.4 工作流引擎模块

#### 2.4.1 模块职责

- 管理工作流定义和节点
- 处理工作流的执行和状态管理
- 支持工作流的版本控制和发布
- 实现工作流与表单、页面的集成

#### 2.4.2 核心数据结构

```
+---------------+       +---------------+       +---------------+
|   FlowDef     |------>|   FlowNode    |------>|  NodeAction   |
+---------------+       +---------------+       +---------------+
        |                      |
        v                      v
+---------------+       +---------------+
| FlowTransition|       |  FlowVariable |
+---------------+       +---------------+
```

#### 2.4.3 关键接口

- `FlowEngine`：工作流引擎核心类，负责工作流的加载和执行
- `IFlowNode`：工作流节点接口，用于定义节点的行为
- `IFlowContext`：工作流上下文接口，用于在节点间传递数据

### 2.5 规则引擎模块

#### 2.5.1 模块职责

- 管理业务规则定义
- 处理规则的执行和评估
- 支持规则的版本控制和发布
- 实现规则与表单、页面、工作流的集成

#### 2.5.2 核心数据结构

```
+---------------+       +---------------+       +---------------+
|   RuleDef     |------>| RuleCondition |------>|  RuleAction   |
+---------------+       +---------------+       +---------------+
        |                      |
        v                      v
+---------------+       +---------------+
|  RuleVariable |       |  RuleFunction |
+---------------+       +---------------+
```

#### 2.5.3 关键接口

- `RuleEngine`：规则引擎核心类，负责规则的加载和执行
- `IRuleEvaluator`：规则评估接口，用于评估规则条件
- `IRuleExecutor`：规则执行接口，用于执行规则动作

## 3. 数据模型设计

### 3.1 核心数据表

- **元数据表**：存储实体、字段、关系等元数据
- **表单表**：存储表单定义、布局、控件等
- **页面表**：存储页面定义、布局、组件等
- **工作流表**：存储工作流定义、节点、转换等
- **规则表**：存储规则定义、条件、动作等
- **用户表**：存储用户信息、角色、权限等
- **应用表**：存储应用定义、模块、菜单等

### 3.2 数据关系

- 一个应用包含多个模块
- 一个模块包含多个实体
- 一个实体包含多个字段和规则
- 一个表单关联一个实体
- 一个页面包含多个组件
- 一个工作流包含多个节点和转换
- 一个规则包含多个条件和动作

## 4. API 设计

### 4.1 GraphQL API

- **查询**：获取元数据、表单、页面、工作流等
- **变更**：创建、更新、删除元数据、表单、页面、工作流等
- **订阅**：监听元数据、表单、页面、工作流等的变化

### 4.2 RESTful API

- **元数据 API**：管理元数据的CRUD操作
- **表单 API**：管理表单的CRUD操作
- **页面 API**：管理页面的CRUD操作
- **工作流 API**：管理工作流的CRUD操作
- **规则 API**：管理规则的CRUD操作
- **用户 API**：管理用户的CRUD操作
- **应用 API**：管理应用的CRUD操作

## 5. 扩展机制

### 5.1 插件系统

- **插件接口**：定义插件的生命周期和扩展点
- **插件加载器**：负责插件的加载和卸载
- **插件注册表**：管理已安装的插件

### 5.2 脚本引擎

- **脚本执行器**：执行JavaScript、Python等脚本
- **脚本上下文**：提供脚本执行的上下文环境
- **脚本库**：提供常用的脚本函数和工具

### 5.3 事件总线

- **事件发布者**：发布事件到事件总线
- **事件订阅者**：从事件总线订阅事件
- **事件处理器**：处理特定类型的事件

## 6. 部署架构

### 6.1 单体部署

适用于小型应用场景，所有组件部署在一个服务中。

### 6.2 微服务部署

适用于大型应用场景，各组件独立部署为微服务。

```
+----------------+  +----------------+  +----------------+
|  元数据服务     |  |  表单服务      |  |  页面服务      |
+----------------+  +----------------+  +----------------+
+----------------+  +----------------+  +----------------+
|  工作流服务     |  |  规则服务      |  |  用户服务      |
+----------------+  +----------------+  +----------------+
```

### 6.3 多租户部署

支持多租户隔离，可以采用以下策略：

- **Schema隔离**：每个租户使用独立的数据库Schema
- **表前缀隔离**：每个租户使用带有租户前缀的表
- **行级隔离**：所有租户共享表，通过租户ID区分数据
