# 多级关联数据查询实施计划

## 1. 实现目标

实现多级关联数据查询功能，使系统能够在一次查询中获取多级关联实体的数据，例如员工关联部门，部门关联公司，减少前端的请求次数，提高应用性能。

## 2. 实现步骤

### 2.1 类型定义扩展

#### 2.1.1 扩展 EntityRelation 接口

修改 `src/features/metadata/types/relation.ts` 文件，扩展 `EntityRelation` 接口：

```typescript
/**
 * 实体关联信息
 */
export interface EntityRelation {
  field: string;                         // 关联字段名
  entity: string;                        // 关联实体名
  module?: string;                       // 关联实体所属模块
  type: 'one' | 'many';                  // 关联类型：一对一或一对多
  foreignField?: string;                 // 外键字段，默认为_id
  alias?: string;                        // 关联数据别名，默认为 ${field}_relation
  condition?: RelationCondition;         // 关联条件
  control?: RelationControl;             // 关联控制配置
  nestedRelations?: boolean;             // 是否允许嵌套查询关联数据，默认为false
  maxDepth?: number;                     // 最大嵌套深度，默认为1
}
```

### 2.2 查询生成器扩展

#### 2.2.1 修改 QueryGenerator 类

修改 `src/lib/graphql/query-generator.ts` 文件，实现递归生成关联查询的功能：

```typescript
/**
 * GraphQL查询生成器
 * 根据实体元数据生成GraphQL查询
 */
export class QueryGenerator {
  /**
   * 生成实体详情查询
   * @param entityMetadata 实体元数据
   * @param context 上下文
   * @param includeRelations 是否包含关联数据
   * @param maxDepth 最大嵌套深度
   * @returns GraphQL查询字符串
   */
  static generateDetailQuery(
    entityMetadata: EntityMetadata,
    context: Record<string, any> = {},
    includeRelations: boolean = true,
    maxDepth: number = 1
  ): string {
    const { name, fields } = entityMetadata
    const fieldNames = this.generateFieldsSelection(fields)

    // 如果不包含关联数据，直接返回基本查询
    if (!includeRelations || maxDepth <= 0) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail($_id: $_id) {
            ${fieldNames}
          }
        }
      `
    }

    // 如果没有关联信息，直接返回基本查询
    if (!entityMetadata.relations || entityMetadata.relations.length === 0) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail($_id: $_id) {
            ${fieldNames}
          }
        }
      `
    }

    // 获取所有满足条件的关联配置
    const matchingRelations = getAllMatchingRelations(entityMetadata.relations, context)

    // 如果没有满足条件的关联配置，直接返回基本查询
    if (matchingRelations.length === 0) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail($_id: $_id) {
            ${fieldNames}
          }
        }
      `
    }

    // 关联字段查询
    const relationQueries = this.generateRelationQueries(matchingRelations, context, maxDepth)

    return `
      query Get${name}Detail($_id: ID!) {
        ${name}_detail($_id: $_id) {
          ${fieldNames}${relationQueries}
        }
      }
    `
  }

  /**
   * 生成关联查询字符串
   * @param relations 关联配置数组
   * @param context 上下文
   * @param maxDepth 最大嵌套深度
   * @param visitedEntities 已访问的实体，用于检测循环引用
   * @returns 关联查询字符串
   */
  private static generateRelationQueries(
    relations: EntityRelation[],
    context: Record<string, any> = {},
    maxDepth: number = 1,
    visitedEntities: Set<string> = new Set()
  ): string {
    if (maxDepth <= 0) return ''

    return relations.map(relation => {
      const { field, entity, module, alias, control, nestedRelations, maxDepth: relationMaxDepth } = relation
      const relationAlias = alias || `${field}_relation`
      const displayFields = control?.displayFields?.join('\n        ') || '_id'

      // 如果不允许嵌套查询或已达到最大深度，只返回基本字段
      if (!nestedRelations || maxDepth <= 1) {
        return `
      ${relationAlias} {
        ${displayFields}
      }`
      }

      // 检测循环引用
      const entityKey = `${module || 'meta'}.${entity}`
      if (visitedEntities.has(entityKey)) {
        return `
      ${relationAlias} {
        ${displayFields}
      }`
      }

      // 记录已访问的实体
      const newVisitedEntities = new Set(visitedEntities)
      newVisitedEntities.add(entityKey)

      // 获取关联实体的元数据
      try {
        // 这里需要异步获取元数据，但由于查询生成是同步的，
        // 我们需要在调用前预先加载所有需要的元数据
        const relationMetadata = getEntityMetadataSync(entity, module || 'meta')
        
        if (!relationMetadata || !relationMetadata.relations || relationMetadata.relations.length === 0) {
          return `
      ${relationAlias} {
        ${displayFields}
      }`
        }

        // 获取关联实体中满足条件的关联配置
        const nestedRelations = getAllMatchingRelations(relationMetadata.relations, context)
        
        if (nestedRelations.length === 0) {
          return `
      ${relationAlias} {
        ${displayFields}
      }`
        }

        // 递归生成嵌套关联查询
        const nestedDepth = Math.min(maxDepth - 1, relationMaxDepth || maxDepth - 1)
        const nestedQueries = this.generateRelationQueries(
          nestedRelations,
          context,
          nestedDepth,
          newVisitedEntities
        )

        return `
      ${relationAlias} {
        ${displayFields}${nestedQueries}
      }`
      } catch (error) {
        console.error(`Failed to get metadata for ${entity}:`, error)
        return `
      ${relationAlias} {
        ${displayFields}
      }`
      }
    }).join('')
  }

  // 其他方法类似修改...
}
```

#### 2.2.2 实现同步获取元数据的函数

创建 `src/features/metadata/api/metadataCache.ts` 文件，实现同步获取元数据的功能：

```typescript
/**
 * 元数据缓存
 */
import { EntityMetadata } from '../types'

// 元数据缓存
const metadataCache = new Map<string, EntityMetadata>()

/**
 * 设置元数据缓存
 * @param entity 实体名称
 * @param module 模块名称
 * @param metadata 元数据
 */
export const setEntityMetadataCache = (
  entity: string,
  module: string,
  metadata: EntityMetadata
): void => {
  const key = `${module}.${entity}`
  metadataCache.set(key, metadata)
}

/**
 * 同步获取元数据
 * @param entity 实体名称
 * @param module 模块名称
 * @returns 元数据或undefined
 */
export const getEntityMetadataSync = (
  entity: string,
  module: string
): EntityMetadata | undefined => {
  const key = `${module}.${entity}`
  return metadataCache.get(key)
}

/**
 * 清除元数据缓存
 * @param entity 实体名称
 * @param module 模块名称
 */
export const clearEntityMetadataCache = (
  entity?: string,
  module?: string
): void => {
  if (entity && module) {
    const key = `${module}.${entity}`
    metadataCache.delete(key)
  } else {
    metadataCache.clear()
  }
}
```

### 2.3 数据处理器扩展

修改 `src/lib/relation/processor.ts` 文件，实现递归处理嵌套关联数据的功能：

```typescript
/**
 * 处理实体数据，合并关联数据
 * @param data 原始数据
 * @param metadata 实体元数据
 * @param context 上下文
 * @param maxDepth 最大处理深度
 * @param visitedEntities 已访问的实体，用于检测循环引用
 * @returns 处理后的数据
 */
export const processEntityData = (
  data: any,
  metadata: EntityMetadata,
  context: Record<string, any> = {},
  maxDepth: number = 1,
  visitedEntities: Set<string> = new Set()
): any => {
  if (!data) return data
  
  // 如果没有关联信息或已达到最大深度，直接返回原始数据
  if (!metadata.relations || metadata.relations.length === 0 || maxDepth <= 0) return data
  
  // 处理关联数据
  const result = { ...data }
  
  // 获取所有字段
  const fields = metadata.fields.map(field => field.name)
  
  // 处理每个字段的关联数据
  fields.forEach(field => {
    // 获取满足条件的关联配置
    const relation = getMatchingRelation(metadata.relations, field, context)
    
    if (relation) {
      const { entity, module, alias, nestedRelations, maxDepth: relationMaxDepth } = relation
      const relationAlias = alias || `${field}_relation`
      const relationData = data[relationAlias]
      
      if (relationData) {
        // 将关联数据合并到原始数据中
        result[`${field}_data`] = relationData
        
        // 如果允许嵌套查询且未达到最大深度，处理嵌套关联数据
        if (nestedRelations && maxDepth > 1 && relationData) {
          // 检测循环引用
          const entityKey = `${module || 'meta'}.${entity}`
          if (!visitedEntities.has(entityKey)) {
            // 记录已访问的实体
            const newVisitedEntities = new Set(visitedEntities)
            newVisitedEntities.add(entityKey)
            
            try {
              // 获取关联实体的元数据
              const relationMetadata = getEntityMetadataSync(entity, module || 'meta')
              
              if (relationMetadata) {
                // 递归处理嵌套关联数据
                const nestedDepth = Math.min(maxDepth - 1, relationMaxDepth || maxDepth - 1)
                
                if (Array.isArray(relationData)) {
                  // 处理一对多关联
                  result[`${field}_data`] = relationData.map(item => 
                    processEntityData(item, relationMetadata, context, nestedDepth, newVisitedEntities)
                  )
                } else {
                  // 处理一对一关联
                  result[`${field}_data`] = processEntityData(
                    relationData,
                    relationMetadata,
                    context,
                    nestedDepth,
                    newVisitedEntities
                  )
                }
              }
            } catch (error) {
              console.error(`Failed to process nested relation data for ${entity}:`, error)
            }
          }
        }
      }
    }
  })
  
  return result
}
```

### 2.4 元数据预加载

修改 `src/features/metadata/api/metadataApi.ts` 文件，实现元数据预加载功能：

```typescript
/**
 * 预加载关联实体元数据
 * @param metadata 主实体元数据
 * @param context 上下文
 * @param maxDepth 最大加载深度
 * @param visitedEntities 已访问的实体，用于检测循环引用
 */
export const preloadRelationMetadata = async (
  metadata: EntityMetadata,
  context: Record<string, any> = {},
  maxDepth: number = 2,
  visitedEntities: Set<string> = new Set()
): Promise<void> => {
  if (!metadata.relations || metadata.relations.length === 0 || maxDepth <= 0) return
  
  // 获取所有满足条件的关联配置
  const matchingRelations = getAllMatchingRelations(metadata.relations, context)
  
  // 预加载每个关联实体的元数据
  for (const relation of matchingRelations) {
    const { entity, module, nestedRelations, maxDepth: relationMaxDepth } = relation
    
    // 检测循环引用
    const entityKey = `${module || 'meta'}.${entity}`
    if (visitedEntities.has(entityKey)) continue
    
    // 记录已访问的实体
    const newVisitedEntities = new Set(visitedEntities)
    newVisitedEntities.add(entityKey)
    
    try {
      // 获取关联实体的元数据
      const relationMetadata = await fetchEntityMetadata(entity, module || 'meta')
      
      // 缓存元数据
      setEntityMetadataCache(entity, module || 'meta', relationMetadata)
      
      // 如果允许嵌套查询且未达到最大深度，递归预加载嵌套关联实体的元数据
      if (nestedRelations && maxDepth > 1) {
        const nestedDepth = Math.min(maxDepth - 1, relationMaxDepth || maxDepth - 1)
        await preloadRelationMetadata(relationMetadata, context, nestedDepth, newVisitedEntities)
      }
    } catch (error) {
      console.error(`Failed to preload metadata for ${entity}:`, error)
    }
  }
}
```

## 3. 测试计划

### 3.1 创建测试数据

创建包含多级关联的测试实体元数据：

```typescript
/**
 * 测试关联实体元数据 - 员工
 */
export const employeeMetadata: EntityMetadata = {
  // 基本信息...
  relations: [
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      alias: 'department_info',
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 2, // 最大嵌套深度
      // 其他配置...
    },
    // 其他关联...
  ]
}

/**
 * 测试关联实体元数据 - 部门
 */
export const departmentMetadata: EntityMetadata = {
  // 基本信息...
  relations: [
    {
      field: 'parent_id',
      entity: 'department', // 自关联
      module: 'hr',
      type: 'one',
      alias: 'parent_department',
      nestedRelations: true,
      maxDepth: 3, // 允许查询更深的部门层级
      // 其他配置...
    },
    {
      field: 'company_id',
      entity: 'company',
      module: 'hr',
      type: 'one',
      alias: 'company_info',
      nestedRelations: true,
      // 其他配置...
    },
    // 其他关联...
  ]
}
```

### 3.2 编写测试用例

编写多级关联查询的测试用例，测试循环引用和深度控制。
