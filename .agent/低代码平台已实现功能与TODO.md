# 低代码平台已实现功能与TODO

## 已实现功能分析

通过对当前项目代码的分析，`vibe-engine` 已经实现了以下核心功能：

### 1. 元数据管理

- [x] **基础元数据模型**：已实现 `DefEntity` 和 `DefField` 等基础元数据模型
- [x] **元数据注册机制**：通过 `MetaManager` 实现元数据的注册和管理
- [x] **基础字段类型**：支持多种基础数据类型（VARCHAR, NUMBER, DATE 等）
- [x] **基础业务实体**：已定义组织、部门、用户等基础业务实体
- [x] **元数据扩展点**：通过 `IMetaRegister` 接口支持元数据的扩展注册

### 2. 数据库操作

- [x] **数据库适配**：支持 PostgreSQL 和 MySQL 数据库
- [x] **表结构自动创建**：根据元数据自动创建和更新数据库表结构
- [x] **基础数据访问**：通过 `JdbcAccess` 和 `DataRecordAccess` 提供基础数据访问能力
- [x] **事务管理**：通过 `TransactionExecutor` 提供事务管理功能
- [x] **SQL方言支持**：通过 `SqlDialect` 和相关类支持不同数据库的SQL方言

### 3. GraphQL API

- [x] **动态GraphQL Schema**：根据元数据自动生成 GraphQL Schema
- [x] **基础查询操作**：支持基于实体的详情查询、列表查询和分页查询
- [x] **基础变更操作**：支持基于实体的创建、更新和删除操作
- [x] **GraphQL扩展点**：通过 `IGraphQLExtRegistry` 接口支持GraphQL的扩展
- [x] **GraphiQL集成**：提供GraphiQL界面用于API测试和调试

### 4. 扩展机制

- [x] **扩展点框架**：通过 `IExtension` 接口和 `ExtensionManager` 提供扩展点框架
- [x] **生命周期钩子**：支持实体操作的前置、后置和提交后钩子
- [x] **内置扩展实现**：提供 `BuiltInMutationExtension` 等内置扩展实现
- [x] **JavaScript引擎集成**：集成GraalVM JavaScript引擎，支持脚本执行
- [x] **流程节点抽象**：通过 `IFlowNode` 和 `AbsFlowNode` 提供流程节点抽象

## 待实现功能（TODO）

基于对已实现功能的分析，以下是需要优先实现的功能：

### 1. 元数据管理模块增强

- [ ] **元数据属性扩展**：增加实体的图标、颜色、分组等属性
    - [ ] 扩展 `DefEntity` 类，添加相关属性
    - [ ] 更新元数据注册和管理逻辑
    - [ ] 提供元数据属性的API访问

- [ ] **实体关系定义**：实现一对一、一对多、多对多关系定义
    - [ ] 设计并实现 `DefRelation` 类
    - [ ] 扩展 `MetaManager` 支持关系管理
    - [ ] 实现关系的数据库映射

- [ ] **字段验证规则**：增强字段验证规则定义
    - [ ] 设计并实现 `ValidationRule` 类
    - [ ] 扩展 `DefField` 支持验证规则
    - [ ] 实现验证规则执行引擎

- [ ] **元数据版本控制**：实现元数据的版本控制和迁移
    - [ ] 设计版本控制模型
    - [ ] 实现版本差异比较和迁移逻辑
    - [ ] 提供版本回滚机制

### 2. 表单设计器模块

- [ ] **表单元数据结构**：设计并实现表单元数据结构
    - [ ] 实现 `FormDef`、`FormField` 等核心类
    - [ ] 设计表单与实体的关联机制
    - [ ] 实现表单版本控制

- [ ] **表单控件库**：实现表单控件库管理
    - [ ] 设计控件类型系统
    - [ ] 实现基础控件（文本、数字、日期等）
    - [ ] 实现复合控件（表格、树形等）

- [ ] **表单布局引擎**：实现表单布局引擎
    - [ ] 设计布局模型（分组、选项卡、网格等）
    - [ ] 实现布局渲染逻辑
    - [ ] 支持响应式布局

### 3. 页面设计器模块

- [ ] **页面元数据结构**：设计并实现页面元数据结构
    - [ ] 实现 `PageDef`、`PageComponent` 等核心类
    - [ ] 设计页面组件模型
    - [ ] 实现页面版本控制

- [ ] **页面组件库**：实现页面组件库管理
    - [ ] 设计组件类型系统
    - [ ] 实现基础组件（容器、卡片、列表等）
    - [ ] 实现数据展示组件（表格、图表等）

- [ ] **页面布局引擎**：实现页面布局引擎
    - [ ] 设计布局模型（网格、自由布局等）
    - [ ] 实现布局渲染逻辑
    - [ ] 支持响应式布局

### 4. 工作流引擎模块

- [ ] **工作流元数据结构**：设计并实现工作流元数据结构
    - [ ] 完善 `FlowDef`、`FlowNode` 等核心类
    - [ ] 设计工作流状态模型
    - [ ] 实现工作流版本控制

- [ ] **工作流节点类型**：实现各种工作流节点类型
    - [ ] 实现人工节点（审批、填写表单等）
    - [ ] 实现自动节点（脚本执行、服务调用等）
    - [ ] 实现条件节点（分支、合并等）

- [ ] **工作流执行引擎**：实现工作流执行引擎
    - [ ] 完善 `IFlowNode` 接口实现
    - [ ] 实现工作流实例管理
    - [ ] 实现工作流状态跟踪和历史记录

### 5. 脚本引擎增强

- [ ] **JavaScript引擎增强**：完善JavaScript脚本支持
    - [ ] 设计脚本执行上下文
    - [ ] 提供内置函数和工具库
    - [ ] 实现脚本缓存和优化

- [ ] **脚本安全沙箱**：提供脚本安全沙箱
    - [ ] 实现资源限制和超时控制
    - [ ] 实现权限控制和安全检查
    - [ ] 提供脚本执行审计

### 6. 权限与角色管理

- [ ] **用户角色权限模型**：完善用户、角色、权限模型
    - [ ] 设计RBAC权限模型
    - [ ] 实现用户-角色-权限关系
    - [ ] 提供权限分配和管理接口

- [ ] **资源权限控制**：实现基于资源的权限控制
    - [ ] 设计资源权限模型
    - [ ] 实现权限检查机制
    - [ ] 提供权限缓存和优化

## 优先级建议

基于当前项目状态，建议按以下优先级实施：

1. **元数据管理模块增强**：作为低代码平台的基础，需要优先完善
2. **表单设计器模块**：作为用户交互的核心，需要尽早实现
3. **脚本引擎增强**：为表单和页面提供动态行为支持
4. **权限与角色管理**：确保系统安全性和可控性
5. **页面设计器模块**：提供完整的用户界面设计能力
6. **工作流引擎模块**：实现业务流程自动化

## 下一步具体工作

1. 扩展 `DefEntity` 和 `DefField` 类，增加更多元数据属性
2. 设计并实现实体关系定义模型
3. 设计并实现表单元数据结构
4. 完善JavaScript脚本引擎，提供更丰富的上下文和API
5. 设计并实现RBAC权限模型
