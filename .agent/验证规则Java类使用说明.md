# 验证规则 Java 类使用说明

本文档介绍了验证规则相关 Java 类的使用方法和示例。

## 1. 类结构

验证规则相关的 Java 类包括：

- `ValidationRuleType`：验证规则类型枚举
- `ValidationRule`：基本验证规则类
- `CompositeValidationRule`：复合验证规则类，用于组合多个验证规则
- `CustomValidationRule`：自定义验证规则类，用于执行自定义验证表达式
- `AsyncValidationRule`：异步验证规则类，用于执行需要远程 API 调用的验证

这些类的继承关系如下：

```
ValidationRule
  ├── CompositeValidationRule
  ├── CustomValidationRule
  └── AsyncValidationRule
```

## 2. ValidationRuleType 枚举

`ValidationRuleType` 是验证规则类型的枚举，包含以下值：

- `REQUIRED`：必填验证
- `MIN`：最小值/最小长度验证
- `MAX`：最大值/最大长度验证
- `PATTERN`：正则表达式验证
- `EMAIL`：电子邮件格式验证
- `URL`：URL 格式验证
- `DEPENDENCY`：依赖字段验证
- `CUSTOM`：自定义验证
- `ASYNC`：异步验证
- `ALL_OF`：满足所有子规则
- `ONE_OF`：满足任一子规则

### 2.1 基本用法

```java
// 获取验证规则类型的字符串值
String requiredTypeValue = ValidationRuleType.REQUIRED.getValue(); // "required"

// 根据字符串值获取验证规则类型枚举
ValidationRuleType type = ValidationRuleType.fromValue("min"); // ValidationRuleType.MIN

// 检查字符串值是否是有效的验证规则类型
boolean isValid = ValidationRuleType.isValid("custom"); // true
boolean isInvalid = ValidationRuleType.isValid("unknown"); // false
```

## 3. ValidationRule 类

`ValidationRule` 是所有验证规则的基类，包含以下属性：

- `type`：验证类型（ValidationRuleType 枚举）
- `params`：验证参数
- `message`：错误消息（支持模板语法）
- `condition`：启用条件表达式
- `dependsOn`：条件依赖的字段
- `async`：是否为异步验证

### 3.1 基本用法

```java
// 创建一个必填验证规则
ValidationRule requiredRule = ValidationRule.builder()
    .type(ValidationRuleType.REQUIRED)
    .message("此字段不能为空")
    .build();

// 创建一个最小值验证规则
Map<String, Object> minParams = new HashMap<>();
minParams.put("min", 0);
ValidationRule minRule = ValidationRule.builder()
    .type(ValidationRuleType.MIN)
    .params(minParams)
    .message("值不能小于0")
    .build();

// 创建一个带条件的验证规则
ValidationRule conditionalRule = ValidationRule.builder()
    .type(ValidationRuleType.MAX)
    .paramsMap(Map.of("max", 100))
    .message("值不能大于100")
    .condition("field_type === 'number'")
    .dependsOn(List.of("field_type"))
    .build();
```

## 4. CompositeValidationRule 类

`CompositeValidationRule` 用于组合多个验证规则，支持 `ONE_OF`（任一规则满足）和 `ALL_OF`（所有规则满足）两种组合方式。

### 4.1 基本用法

```java
// 创建子规则
ValidationRule rule1 = ValidationRule.builder()
    .type(ValidationRuleType.MIN)
    .paramsMap(Map.of("min", 0))
    .message("值不能小于0")
    .build();

ValidationRule rule2 = ValidationRule.builder()
    .type(ValidationRuleType.MAX)
    .paramsMap(Map.of("max", 100))
    .message("值不能大于100")
    .build();

// 创建一个ALL_OF复合规则（所有规则都必须满足）
CompositeValidationRule allOfRule = new CompositeValidationRule(
    ValidationRuleType.ALL_OF,
    List.of(rule1, rule2)
);
allOfRule.setMessage("值必须在0到100之间");

// 创建一个ONE_OF复合规则（满足任一规则即可）
ValidationRule rule3 = ValidationRule.builder()
    .type(ValidationRuleType.PATTERN)
    .paramsMap(Map.of("pattern", "^[a-zA-Z]+$"))
    .message("必须是字母")
    .build();

ValidationRule rule4 = ValidationRule.builder()
    .type(ValidationRuleType.PATTERN)
    .paramsMap(Map.of("pattern", "^[0-9]+$"))
    .message("必须是数字")
    .build();

CompositeValidationRule oneOfRule = new CompositeValidationRule(
    ValidationRuleType.ONE_OF,
    List.of(rule3, rule4)
);
oneOfRule.setMessage("必须是纯字母或纯数字");
```

## 4. CustomValidationRule 类

`CustomValidationRule` 用于执行自定义验证表达式，表达式可以是 JavaScript 代码，通过 GraalVM.js 执行。

### 4.1 基本用法

```java
// 创建一个自定义验证规则
CustomValidationRule customRule = new CustomValidationRule(
    "value !== null && value.length >= 3 && value.length <= 20"
);
customRule.setMessage("长度必须在3到20之间");

// 创建一个依赖其他字段的自定义验证规则
CustomValidationRule dependencyRule = new CustomValidationRule(
    "value > record.min_value && value < record.max_value"
);
dependencyRule.setMessage("值必须大于最小值且小于最大值");
dependencyRule.setDependsOn(List.of("min_value", "max_value"));
```

## 5. AsyncValidationRule 类

`AsyncValidationRule` 用于执行需要远程 API 调用的验证，如唯一性检查。

### 5.1 基本用法

```java
// 创建一个异步验证规则
AsyncValidationRule asyncRule = new AsyncValidationRule(
    "/api/validate/username",
    "POST",
    Map.of("Content-Type", "application/json")
);
asyncRule.setMessage("用户名已被使用");
asyncRule.setAsync(true);

// 创建一个带条件的异步验证规则
AsyncValidationRule conditionalAsyncRule = new AsyncValidationRule(
    "/api/validate/email",
    "POST",
    null
);
conditionalAsyncRule.setMessage("邮箱已被注册");
conditionalAsyncRule.setCondition("value && value.includes('@')");
conditionalAsyncRule.setAsync(true);
```

## 6. 在实体字段中使用验证规则

验证规则可以添加到实体字段的验证规则列表中：

```java
// 创建字段验证规则列表
List<ValidationRule> validationRules = new ArrayList<>();
validationRules.add(ValidationRule.builder()
    .type(ValidationRuleType.REQUIRED)
    .message("此字段不能为空")
    .build());
validationRules.add(ValidationRule.builder()
    .type(ValidationRuleType.PATTERN)
    .paramsMap(Map.of("pattern", "^[a-zA-Z0-9_]{3,20}$"))
    .message("只能包含字母、数字和下划线，长度3-20")
    .build());

// 将验证规则添加到字段中
DefField field = DefField.builder()
    .name("username")
    .displayName("用户名")
    .type(DataType.VARCHAR)
    .build();
// 注意：需要在DefField类中添加validationRules字段
// field.setValidationRules(validationRules);
```

## 7. 验证规则处理

验证规则的处理可以通过以下方式实现：

1. 在前端，使用 JavaScript 表达式引擎评估验证规则
2. 在后端，使用 GraalVM.js 执行 JavaScript 验证表达式
3. 对于异步验证，通过 HTTP 请求调用验证 API

示例：

```java
// 前端验证处理示例（伪代码）
function validateField(field, value, record) {
    for (const rule of field.validationRules) {
        // 检查条件
        if (rule.condition) {
            const conditionMet = evaluateExpression(rule.condition, { value, ...record });
            if (!conditionMet) continue;
        }

        // 根据规则类型执行验证
        // 注意：前端使用字符串值，后端使用枚举
        switch (rule.type) {
            case "required":
                if (value === null || value === undefined || value === "") {
                    return rule.message || "此字段不能为空";
                }
                break;
            case "custom":
                const isValid = evaluateExpression(rule.params.expression, { value, record });
                if (!isValid) {
                    return rule.message || "验证失败";
                }
                break;
            // 其他验证类型...
        }
    }

    return null; // 验证通过
}

// 后端验证处理示例（伪代码）
public String validateField(DefField field, Object value, IRecord record) {
    for (ValidationRule rule : field.getValidationRules()) {
        // 检查条件
        if (rule.getCondition() != null) {
            boolean conditionMet = jsEngine.evaluateCondition(rule.getCondition(), value, record);
            if (!conditionMet) continue;
        }

        // 根据规则类型执行验证
        ValidationRuleType ruleType = rule.getType();
        if (ruleType == ValidationRuleType.REQUIRED) {
            if (value == null || "".equals(value)) {
                return rule.getMessage() != null ? rule.getMessage() : "此字段不能为空";
            }
        } else if (ruleType == ValidationRuleType.CUSTOM) {
            if (rule instanceof CustomValidationRule) {
                CustomValidationRule customRule = (CustomValidationRule) rule;
                boolean isValid = jsEngine.evaluateExpression(customRule.getExpression(), value, record);
                if (!isValid) {
                    return customRule.getMessage() != null ? customRule.getMessage() : "验证失败";
                }
            }
        }
        // 其他验证类型...
        }
    }

    return null; // 验证通过
}
```

## 8. 注意事项

1. 验证规则的参数类型应与验证类型匹配，例如：

   - `min`/`max` 规则的参数应为数字
   - `pattern` 规则的参数应为正则表达式字符串
   - `custom` 规则的参数应为表达式字符串

2. 条件表达式和自定义验证表达式应使用 JavaScript 语法，以便在前后端都能执行

3. 异步验证规则应设置 `async=true`，并提供有效的 API 地址

4. 复合验证规则可以嵌套，但应避免过深的嵌套，以保持验证逻辑的可读性
