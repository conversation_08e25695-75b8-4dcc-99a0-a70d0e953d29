# 表单字段控件联动规则重构分析

## 1. 需求概述

重构表单字段控件联动规则，从原有的"推"模式改为"拉"模式，即在字段上配置"本字段受哪些来源影响"，而不是在源字段上配置"
影响哪些目标字段"。同时，扩展联动来源，使其不仅限于表单中的其他字段，还可以是其他非字段数据源。本次重构不考虑向后兼容，直接替换原有方案。

## 2. 现有联动规则实现分析

### 2.1 数据结构

当前的联动规则数据结构定义在 `EntityField` 的 `control.linkage` 字段中：

```typescript
interface LinkageRule {
  id?: string               // 规则唯一标识
  priority?: number         // 规则优先级，数字越小优先级越高
  target: string            // 目标字段
  effect: LinkageEffectType // 联动效果类型
  condition?: string        // 触发条件
  dependsOn?: string[]      // 显式依赖的字段（可选，如果不提供会自动从条件表达式中提取）
  mapping?: Record<string, any> // 值映射
  value?: any               // 静态值
  expression?: string       // 动态表达式
  props?: Record<string, any>   // 控件属性
}
```

### 2.2 联动效果类型

系统支持多种联动效果类型：

```typescript
type LinkageEffectType =
  | 'setValue'      // 设置值
  | 'setOptions'    // 设置选项
  | 'setVisibility' // 设置可见性
  | 'setRequired'   // 设置是否必填
  | 'setDisabled'   // 设置是否禁用
  | 'setValidation' // 设置验证规则
  | 'setProps'      // 设置控件属性
  | 'custom'        // 自定义效果
```

### 2.3 联动实现机制

联动功能主要通过以下组件实现：

1. **LinkageEngine**：负责注册字段联动规则、构建依赖图、执行联动规则
    - 维护字段依赖图和规则映射
    - 检测循环依赖
    - 按拓扑排序顺序执行联动规则

2. **LinkageProvider**：提供联动上下文，管理字段状态
    - 初始化字段状态
    - 监听表单值变化
    - 提供字段状态管理方法

3. **useFieldLinkage**：在组件中获取字段的联动状态
    - 返回字段的可见性、必填性、禁用状态和自定义属性

### 2.4 表达式引擎

系统使用 `ExpressionEngine` 类来评估条件表达式和提取依赖字段：

- 支持条件表达式评估
- 支持依赖字段提取
- 支持表达式缓存
- 提供内置辅助函数

### 2.5 现有模式的问题

1. **依赖关系不直观**：在"推"模式下，字段的行为受多个其他字段影响，但这些影响关系分散在不同字段的配置中，难以整体把握
2. **联动来源有限**：只能以表单中的其他字段作为联动来源，无法响应外部状态变化
3. **规则管理复杂**：当多个字段影响同一个目标字段时，规则分散在不同字段中，优先级和冲突处理变得复杂

## 3. 重构方案设计

### 3.1 新的联动规则数据结构

```typescript
// 联动来源类型
type LinkageSourceType = 
  | 'field'        // 表单字段
  | 'formState'    // 表单状态（如新建/编辑模式）
  | 'userRole'     // 用户角色
  | 'config'       // 全局配置
  | 'api'          // 外部API
  | 'context'      // 上下文环境变量
  | 'expression'   // 表达式计算结果

// 联动来源
interface LinkageSource {
  type: LinkageSourceType    // 来源类型
  name: string               // 来源名称（字段名或其他标识）
  path?: string              // 数据路径（用于访问嵌套属性）
  params?: Record<string, any> // 附加参数
}

// 联动规则（拉模式）
interface LinkageRule {
  id?: string               // 规则唯一标识
  priority?: number         // 规则优先级，数字越小优先级越高
  
  // 联动来源（拉模式的核心变化）
  sources: LinkageSource[]  // 影响本字段的来源列表
  
  // 联动效果
  effect: LinkageEffectType // 联动效果类型
  condition?: string        // 触发条件
  
  // 效果参数
  value?: any               // 静态值
  expression?: string       // 动态表达式
  mapping?: Record<string, any> // 值映射
  props?: Record<string, any>   // 控件属性
}
```

### 3.2 数据源管理

引入数据源管理器，统一管理各类数据源：

```typescript
class DataSourceManager {
  // 注册的数据源提供者
  private providers: Map<LinkageSourceType, DataSourceProvider> = new Map();
  
  // 注册数据源提供者
  registerProvider(type: LinkageSourceType, provider: DataSourceProvider): void {
    this.providers.set(type, provider);
  }
  
  // 获取数据源的值
  getValue(source: LinkageSource, context: Record<string, any>): any {
    const provider = this.providers.get(source.type);
    if (!provider) {
      console.warn(`No provider registered for source type: ${source.type}`);
      return undefined;
    }
    
    return provider.getValue(source, context);
  }
  
  // 监听数据源变化
  watchSource(source: LinkageSource, callback: (value: any) => void): () => void {
    const provider = this.providers.get(source.type);
    if (!provider || !provider.watch) {
      return () => {}; // 返回空函数
    }
    
    return provider.watch(source, callback);
  }
}

// 数据源提供者接口
interface DataSourceProvider {
  getValue(source: LinkageSource, context: Record<string, any>): any;
  watch?(source: LinkageSource, callback: (value: any) => void): () => void;
}
```

### 3.3 联动引擎重构

重构 `LinkageEngine` 类，使其支持"拉"模式的联动规则：

```typescript
class LinkageEngine {
  // 字段规则映射
  private fieldRules: Map<string, LinkageRule[]> = new Map();

  // 反向依赖图（sourceField -> targetFields[]）
  private reverseDependencyGraph: Map<string, Set<string>> = new Map();

  // 数据源管理器
  private dataSourceManager: DataSourceManager;

  // 表单操作接口
  private formOperations: FormOperations;

  constructor(dataSourceManager: DataSourceManager, formOperations: FormOperations) {
    this.dataSourceManager = dataSourceManager;
    this.formOperations = formOperations;
  }

  // 注册字段联动规则
  registerField(fieldName: string, rules: LinkageRule[]): void {
    // 存储规则
    this.fieldRules.set(fieldName, rules);

    // 更新反向依赖图
    rules.forEach(rule => {
      rule.sources.forEach(source => {
        if (source.type === 'field') {
          const sourceField = source.name;

          // 确保源字段在图中
          if (!this.reverseDependencyGraph.has(sourceField)) {
            this.reverseDependencyGraph.set(sourceField, new Set());
          }

          // 添加依赖关系：sourceField -> fieldName
          this.reverseDependencyGraph.get(sourceField)!.add(fieldName);
        }
      });
    });
  }

  // 当字段值变化时执行联动
  onFieldChange(changedField: string, formValues: Record<string, any>): void {
    // 获取受影响的字段
    const affectedFields = this.getAffectedFields(changedField);

    // 执行每个受影响字段的联动规则
    affectedFields.forEach(fieldName => {
      this.executeFieldRules(fieldName, formValues);
    });
  }

  // 当非字段数据源变化时执行联动
  onSourceChange(source: LinkageSource, formValues: Record<string, any>): void {
    // 查找受此数据源影响的所有字段
    const affectedFields = this.findFieldsAffectedBySource(source);

    // 执行每个受影响字段的联动规则
    affectedFields.forEach(fieldName => {
      this.executeFieldRules(fieldName, formValues);
    });
  }

  // 执行字段的联动规则
  private executeFieldRules(fieldName: string, formValues: Record<string, any>): void {
    const rules = this.fieldRules.get(fieldName) || [];

    // 执行每个规则
    rules.forEach(rule => {
      // 收集所有数据源的值
      const sourceValues: Record<string, any> = { ...formValues };

      // 获取非字段数据源的值
      rule.sources.forEach(source => {
        if (source.type !== 'field') {
          sourceValues[source.name] = this.dataSourceManager.getValue(source, formValues);
        }
      });

      // 检查条件
      if (rule.condition) {
        const conditionMet = ExpressionEngine.evaluate(rule.condition, sourceValues);
        if (!conditionMet) {
          return; // 条件不满足，跳过规则
        }
      }

      // 计算结果值
      let resultValue: any;

      if (rule.expression) {
        resultValue = ExpressionEngine.evaluate(rule.expression, sourceValues);
      } else if (rule.value !== undefined) {
        resultValue = rule.value;
      } else if (rule.mapping) {
        // 获取映射的源值
        const sourceField = rule.sources[0]?.name;
        const sourceValue = sourceField ? sourceValues[sourceField] : undefined;

        // 应用映射
        resultValue = sourceValue !== undefined && rule.mapping[sourceValue] !== undefined
          ? rule.mapping[sourceValue]
          : null;
      }

      // 应用联动效果
      this.applyEffect(fieldName, rule.effect, resultValue, rule.props);
    });
  }

  // 应用联动效果
  private applyEffect(
    fieldName: string,
    effect: LinkageEffectType,
    value: any,
    props?: Record<string, any>
  ): void {
    switch (effect) {
      case 'setValue':
        this.formOperations.setValue(fieldName, value);
        break;

      case 'setOptions':
        this.formOperations.setOptions(fieldName, value);
        break;

      case 'setVisibility':
        this.formOperations.setVisibility(fieldName, !!value);
        break;

      case 'setRequired':
        this.formOperations.setRequired(fieldName, !!value);
        break;

      case 'setDisabled':
        this.formOperations.setDisabled(fieldName, !!value);
        break;

      case 'setValidation':
        this.formOperations.setValidation(fieldName, value);
        break;

      case 'setProps':
        this.formOperations.setProps(fieldName, props || {});
        break;

      case 'custom':
        if (typeof value === 'function') {
          value(fieldName);
        }
        break;

      default:
        console.warn(`未知的联动效果类型: ${effect}`);
    }
  }

  // 获取受字段变化影响的字段
  private getAffectedFields(changedField: string): string[] {
    return Array.from(this.reverseDependencyGraph.get(changedField) || []);
  }

  // 查找受数据源影响的字段
  private findFieldsAffectedBySource(source: LinkageSource): string[] {
    const affectedFields: string[] = [];

    // 遍历所有字段的规则
    this.fieldRules.forEach((rules, fieldName) => {
      // 检查字段是否受此数据源影响
      const isAffected = rules.some(rule =>
        rule.sources.some(s =>
          s.type === source.type && s.name === source.name
        )
      );

      if (isAffected) {
        affectedFields.push(fieldName);
      }
    });

    return affectedFields;
  }
}
```

### 3.4 联动上下文扩展

扩展 `LinkageProvider` 组件，使其能够管理和提供非字段数据源：

```typescript
function LinkageProvider({ children, fields = [], formState = {}, context = {} }) {
  // 表单上下文
  const form = useFormContext();
  
  // 选项上下文
  const { setOptions } = useOptions();
  
  // 字段状态
  const [fieldStates, setFieldStates] = useState<Record<string, FieldState>>({});
  
  // 创建数据源管理器
  const dataSourceManager = useMemo(() => {
    const manager = new DataSourceManager();
    
    // 注册字段数据源提供者
    manager.registerProvider('field', {
      getValue: (source, context) => context[source.name],
      watch: (source, callback) => {
        const subscription = form.watch(source.name, callback);
        return () => subscription.unsubscribe();
      }
    });
    
    // 注册表单状态数据源提供者
    manager.registerProvider('formState', {
      getValue: (source) => formState[source.name]
    });
    
    // 注册上下文数据源提供者
    manager.registerProvider('context', {
      getValue: (source) => context[source.name]
    });
    
    // 注册其他数据源提供者...
    
    return manager;
  }, [form, formState, context]);
  
  // 创建表单操作接口
  const formOperations = useMemo<FormOperations>(() => ({
    setValue: (fieldName, value) => {
      form.setValue(fieldName, value);
    },
    setOptions: (fieldName, options) => {
      setOptions(fieldName, options);
    },
    setVisibility: (fieldName, visible) => {
      setFieldStates(prev => ({
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          visible
        }
      }));
    },
    setRequired: (fieldName, required) => {
      setFieldStates(prev => ({
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          required
        }
      }));
    },
    setDisabled: (fieldName, disabled) => {
      setFieldStates(prev => ({
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          disabled
        }
      }));
    },
    setValidation: (fieldName, rules) => {
      // 实现验证规则设置
    },
    setProps: (fieldName, props) => {
      setFieldStates(prev => ({
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          props: {
            ...prev[fieldName].props,
            ...props
          }
        }
      }));
    }
  }), [form, setOptions]);
  
  // 创建联动引擎
  const linkageEngine = useMemo(() => {
    return new LinkageEngine(dataSourceManager, formOperations);
  }, [dataSourceManager, formOperations]);
  
  // 注册字段
  useEffect(() => {
    // 清除旧规则
    linkageEngine.clear();
    
    // 注册字段
    fields.forEach(field => {
      // 初始化字段状态
      setFieldStates(prev => {
        if (prev[field.name]) {
          return prev;
        }
        
        return {
          ...prev,
          [field.name]: {
            visible: true,
            required: !field.flags.includes('NULLABLE'),
            disabled: false,
            props: {}
          }
        };
      });
      
      // 注册联动规则
      if (field.control?.linkage) {
        linkageEngine.registerField(field.name, field.control.linkage);
      }
    });
  }, [fields, linkageEngine]);
  
  // 监听表单值变化
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (type !== 'change' || !name) {
        return;
      }
      
      // 执行联动
      linkageEngine.onFieldChange(name, value);
    });
    
    return () => subscription.unsubscribe();
  }, [form, linkageEngine]);
  
  // 监听非字段数据源变化
  useEffect(() => {
    // 收集所有非字段数据源
    const nonFieldSources = new Set<LinkageSource>();
    
    fields.forEach(field => {
      if (!field.control?.linkage) return;
      
      field.control.linkage.forEach(rule => {
        rule.sources.forEach(source => {
          if (source.type !== 'field') {
            nonFieldSources.add(source);
          }
        });
      });
    });
    
    // 设置监听
    const unsubscribes = Array.from(nonFieldSources).map(source => {
      return dataSourceManager.watchSource(source, () => {
        // 当数据源变化时，执行联动
        linkageEngine.onSourceChange(source, form.getValues());
      });
    });
    
    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [fields, dataSourceManager, linkageEngine, form]);
  
  // 上下文值
  const contextValue = {
    getFieldState: (fieldName: string) => fieldStates[fieldName] || { visible: true, required: false, disabled: false, props: {} },
    isFieldVisible: (fieldName: string) => fieldStates[fieldName]?.visible ?? true,
    isFieldRequired: (fieldName: string) => fieldStates[fieldName]?.required ?? false,
    isFieldDisabled: (fieldName: string) => fieldStates[fieldName]?.disabled ?? false,
    getFieldProps: (fieldName: string) => fieldStates[fieldName]?.props || {}
  };
  
  return (
    <LinkageContext.Provider value={contextValue}>
      {children}
    </LinkageContext.Provider>
  );
}
```

## 4. 示例场景

### 4.1 基于表单状态的联动

```typescript
// 在编辑模式下禁用字段
{
  name: 'id',
  control: {
    linkage: [
      {
        sources: [{ type: 'formState', name: 'mode' }],
        effect: 'setDisabled',
        condition: "mode === 'edit'",
        value: true
      }
    ]
  }
}
```

### 4.2 基于用户角色的联动

```typescript
// 只有管理员可以看到敏感字段
{
  name: 'secretKey',
  control: {
    linkage: [
      {
        sources: [{ type: 'userRole', name: 'role' }],
        effect: 'setVisibility',
        condition: "role === 'admin'",
        value: true
      }
    ]
  }
}
```

### 4.3 基于多个字段的联动

```typescript
// 根据支付方式和金额显示不同的提示
{
  name: 'paymentNote',
  control: {
    linkage: [
      {
        sources: [
          { type: 'field', name: 'paymentMethod' },
          { type: 'field', name: 'amount' }
        ],
        effect: 'setValue',
        expression: "paymentMethod === 'creditCard' && amount > 10000 ? '需要额外审核' : ''"
      }
    ]
  }
}
```

### 4.4 基于外部API的联动

```typescript
// 根据API返回结果设置字段选项
{
  name: 'city',
  control: {
    linkage: [
      {
        sources: [
          { type: 'field', name: 'province' },
          { type: 'api', name: 'getCities', params: { provinceField: 'province' } }
        ],
        effect: 'setOptions',
        expression: "getCities(province)"
      }
    ]
  }
}
```

## 5. 迁移策略

由于本次重构不考虑向后兼容，需要将所有现有的联动规则转换为新格式。可以提供一个迁移工具函数：

```typescript
function migrateOldLinkageRules(fields: EntityField[]): EntityField[] {
  // 创建字段映射，方便查找
  const fieldMap = new Map<string, EntityField>();
  fields.forEach(field => {
    fieldMap.set(field.name, field);
  });

  // 收集所有旧的联动规则
  const oldRules: Array<{ sourceField: string; rule: LinkageRule }> = [];

  fields.forEach(field => {
    if (!field.control?.linkage) return;

    field.control.linkage.forEach(rule => {
      oldRules.push({
        sourceField: field.name,
        rule: { ...rule }
      });
    });

    // 清除旧规则
    field.control.linkage = [];
  });

  // 转换为新的联动规则
  oldRules.forEach(({ sourceField, rule }) => {
    const targetField = fieldMap.get(rule.target);
    if (!targetField) return;

    // 确保目标字段有control和linkage
    if (!targetField.control) {
      targetField.control = {};
    }
    if (!targetField.control.linkage) {
      targetField.control.linkage = [];
    }

    // 创建新规则
    const newRule: LinkageRule = {
      id: rule.id,
      priority: rule.priority,
      sources: [{ type: 'field', name: sourceField }],
      effect: rule.effect,
      condition: rule.condition,
      value: rule.value,
      expression: rule.expression,
      mapping: rule.mapping,
      props: rule.props
    };

    // 添加到目标字段
    targetField.control.linkage.push(newRule);
  });

  return fields;
}
```

## 6. 性能优化

### 6.1 依赖跟踪优化

在"拉"模式下，我们需要高效地跟踪哪些字段受到特定数据源的影响：

1. 使用反向依赖图（sourceField -> targetFields[]）
2. 对于非字段数据源，使用映射表（sourceType+sourceName -> targetFields[]）

### 6.2 联动执行优化

1. 批量更新：当多个规则同时触发时，批量更新字段状态
2. 防抖处理：对于频繁变化的数据源，使用防抖处理避免过多更新
3. 记忆化计算：缓存表达式计算结果，避免重复计算

### 6.3 条件评估优化

1. 表达式缓存：缓存已编译的表达式函数
2. 短路评估：如果条件明显不满足，跳过后续计算

## 7. 调试与错误处理

### 7.1 联动调试工具

扩展现有的 `LinkageDebugger`，支持新的联动模式：

```typescript
class LinkageDebugger {
  // 执行日志
  private static executionLog: LinkageExecutionLogItem[] = [];

  // 是否启用调试
  private static enabled = false;

  // 启用调试
  static enable(): void {
    this.enabled = true;
  }

  // 禁用调试
  static disable(): void {
    this.enabled = false;
  }

  // 记录执行日志
  static logExecution(
    fieldName: string,
    rule: LinkageRule,
    sourceValues: Record<string, any>,
    resultValue: any,
    error?: Error
  ): void {
    if (!this.enabled) return;

    this.executionLog.push({
      timestamp: new Date(),
      fieldName,
      rule,
      sourceValues,
      resultValue,
      error
    });

    // 限制日志大小
    if (this.executionLog.length > 1000) {
      this.executionLog = this.executionLog.slice(-1000);
    }

    // 输出到控制台
    if (error) {
      console.error(`联动规则执行错误 [${fieldName}]:`, error, { rule, sourceValues });
    } else {
      console.log(`联动规则执行 [${fieldName}]:`, { rule, sourceValues, resultValue });
    }
  }

  // 获取执行日志
  static getExecutionLog(): LinkageExecutionLogItem[] {
    return [...this.executionLog];
  }

  // 获取字段的执行日志
  static getFieldLog(fieldName: string): LinkageExecutionLogItem[] {
    return this.executionLog.filter(item => item.fieldName === fieldName);
  }

  // 清除日志
  static clearLog(): void {
    this.executionLog = [];
  }
}
```

### 7.2 错误处理策略

1. 表达式错误：捕获并记录表达式评估错误，返回默认值
2. 循环依赖检测：在注册规则时检测循环依赖，发出警告
3. 数据源错误：当数据源不可用时提供默认值
4. 规则冲突处理：当多个规则影响同一个属性时，根据优先级决定最终效果

## 8. 总结

本次重构将表单字段控件联动规则从"推"模式转换为"拉"模式，并扩展了联动来源类型。主要变化包括：

1. 联动规则数据结构改变，从指定目标字段改为指定来源字段
2. 引入数据源管理器，支持多种类型的数据源
3. 重构联动引擎，支持新的联动模式
4. 扩展联动上下文，提供更丰富的数据源
5. 提供迁移工具，帮助转换现有规则

这些变化将使表单字段联动更加直观、灵活和强大，能够响应更多类型的数据变化，并且更容易管理和维护。
