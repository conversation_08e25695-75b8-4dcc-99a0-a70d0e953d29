# 实体列表筛选功能系统分析

## 1. 系统架构分析

### 1.1 现有系统架构

当前系统采用了基于React的前端架构，使用了以下关键技术和组件：

- **React**：核心UI框架
- **React Hook Form**：表单状态管理
- **TanStack Query**：数据获取和缓存
- **TanStack Table**：表格组件
- **GraphQL**：API通信协议
- **Shadcn UI**：UI组件库

系统采用了元数据驱动的开发方式，通过EntityMetadata和EntityField等元数据结构定义实体和字段，并基于这些元数据动态生成UI组件。

### 1.2 筛选功能架构设计

筛选功能将扩展现有架构，主要包括以下部分：

1. **元数据扩展**：扩展EntityField类型，增加筛选相关的配置
2. **筛选组件**：新增EntityListFilter、KeywordSearch和AdvancedFilter组件
3. **条件构建**：实现Condition对象的构建和转换逻辑
4. **集成到EntityList**：将筛选功能集成到现有的EntityList组件中

## 2. 数据流分析

### 2.1 筛选数据流

1. 用户在筛选表单中输入查询条件
2. 筛选组件根据用户输入构建Condition对象
3. EntityList组件将Condition对象作为参数传递给fetchEntityList函数
4. fetchEntityList函数将Condition对象转换为GraphQL查询参数
5. 服务端根据查询参数过滤数据并返回结果
6. EntityList组件展示过滤后的数据

### 2.2 状态管理

筛选功能涉及的状态包括：

1. **筛选条件状态**：存储用户输入的筛选条件
2. **筛选表单状态**：管理筛选表单的值和验证
3. **高级筛选状态**：管理高级筛选条件的添加和删除

## 3. 接口设计

### 3.1 元数据接口扩展

扩展EntityField接口，增加筛选相关的配置：

```typescript
export interface FieldCondition {
  queryable?: boolean;                // 是否支持查询
  operators?: Compare[];              // 支持的操作符
  advancedQueryable?: boolean;        // 是否支持高级查询
  defaultOperator?: Compare;          // 默认操作符
}

export interface EntityField {
  // 现有字段...
  
  // 新增字段
  condition?: FieldCondition;         // 查询配置
}
```

### 3.2 组件接口设计

#### EntityListFilter组件

```typescript
interface EntityListFilterProps {
  metadata: EntityMetadata;           // 实体元数据
  onFilterChange: (condition: any) => void; // 筛选条件变更回调
  defaultCondition?: any;             // 默认筛选条件
}
```

#### KeywordSearch组件

```typescript
interface KeywordSearchProps {
  keyFields: EntityField[];           // 关键字段
  onSearch: (keyword: string) => void; // 搜索回调
  placeholder?: string;               // 占位文本
}
```

#### AdvancedFilter组件

```typescript
interface AdvancedFilterProps {
  metadata: EntityMetadata;           // 实体元数据
  onFilterChange: (filters: FilterItem[]) => void; // 筛选条件变更回调
  defaultFilters?: FilterItem[];      // 默认筛选条件
}
```

## 4. 组件设计

### 4.1 EntityListFilter组件

EntityListFilter组件是筛选功能的主要入口，负责整合基本查询和高级查询，并生成最终的Condition对象。

**主要职责**：

- 管理筛选表单状态
- 整合关键字搜索和字段筛选
- 提供重置和应用筛选的功能
- 生成Condition对象并通过回调传递给父组件

**组件结构**：

```jsx
<div className="filter-container">
  <div className="filter-header">
    <h3>筛选条件</h3>
    <div className="filter-actions">
      <Button onClick={handleReset}>重置</Button>
      <Button onClick={handleApply}>应用</Button>
    </div>
  </div>
  
  <div className="filter-body">
    <KeywordSearch keyFields={keyFields} onSearch={handleKeywordSearch} />
    
    <div className="basic-filters">
      {queryableFields.map(field => (
        <FieldFilter key={field.name} field={field} onChange={handleFieldFilterChange} />
      ))}
    </div>
    
    <Collapsible>
      <CollapsibleTrigger>高级筛选</CollapsibleTrigger>
      <CollapsibleContent>
        <AdvancedFilter metadata={metadata} onFilterChange={handleAdvancedFilterChange} />
      </CollapsibleContent>
    </Collapsible>
  </div>
</div>
```

### 4.2 KeywordSearch组件

KeywordSearch组件负责处理关键信息字段的模糊查询。

**主要职责**：

- 提供单一输入框用于关键字搜索
- 支持多个关键字段的组合查询
- 构建模糊查询条件

**组件结构**：

```jsx
<div className="keyword-search">
  <Input
    placeholder={placeholder || `搜索${keyFields.map(f => f.display_name).join('、')}...`}
    value={keyword}
    onChange={e => setKeyword(e.target.value)}
    onKeyDown={handleKeyDown}
  />
  <Button variant="ghost" size="icon" onClick={handleSearch}>
    <Search className="h-4 w-4" />
  </Button>
</div>
```

### 4.3 AdvancedFilter组件

AdvancedFilter组件负责处理高级查询条件的添加和管理。

**主要职责**：

- 支持添加多个筛选条件
- 支持选择字段、操作符和输入值
- 支持条件组合（AND/OR）
- 构建复杂的筛选条件

**组件结构**：

```jsx
<div className="advanced-filter">
  <div className="filter-items">
    {filters.map((filter, index) => (
      <div key={index} className="filter-item">
        <Select value={filter.field} onChange={e => handleFieldChange(index, e.target.value)}>
          {advancedQueryableFields.map(field => (
            <SelectItem key={field.name} value={field.name}>
              {field.display_name}
            </SelectItem>
          ))}
        </Select>

        <Select value={filter.compare} onChange={e => handleCompareChange(index, e.target.value)}>
          {getOperatorsForField(filter.field).map(op => (
            <SelectItem key={op} value={op}>
              {getOperatorLabel(op)}
            </SelectItem>
          ))}
        </Select>

        <FilterValueInput
          field={getFieldByName(filter.field)}
          compare={filter.compare}
          value={filter.value}
          onChange={value => handleValueChange(index, value)}
        />

        <Button variant="ghost" size="icon" onClick={() => removeFilter(index)}>
          <X className="h-4 w-4" />
        </Button>
      </div>
    ))}
  </div>

  <Button variant="outline" onClick={addFilter}>
    <Plus className="mr-2 h-4 w-4" />
    添加条件
  </Button>

  {filters.length > 1 && (
    <div className="filter-logic">
      <RadioGroup value={logic} onValueChange={setLogic}>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="AND" id="AND" />
          <Label htmlFor="AND">满足所有条件</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="OR" id="OR" />
          <Label htmlFor="OR">满足任一条件</Label>
        </div>
      </RadioGroup>
    </div>
  )}
</div>
```

### 4.4 FieldFilter组件

FieldFilter组件负责渲染单个字段的筛选控件。

**主要职责**：

- 根据字段类型渲染合适的筛选控件
- 支持选择操作符
- 处理字段值的输入和验证

**组件结构**：

```jsx
<div className="field-filter">
  <Label>{field.display_name}</Label>
  <div className="filter-input-group">
    {showOperator && (
      <Select value={operator} onChange={e => setOperator(e.target.value)}>
        {operators.map(op => (
          <SelectItem key={op} value={op}>
            {getOperatorLabel(op)}
          </SelectItem>
        ))}
      </Select>
    )}
    
    <FilterValueInput
      field={field}
      compare={operator}
      value={value}
      onChange={setValue}
    />
  </div>
</div>
```

## 5. 条件构建逻辑

### 5.1 基本条件构建

基本条件构建主要包括以下步骤：

1. 收集关键字搜索条件
2. 收集字段筛选条件
3. 合并条件并构建Condition对象

```typescript
function buildCondition(keywordSearch: string, fieldFilters: Record<string, any>, advancedFilters: FilterItem[]): Condition {
  const condition = new Condition();

  // 添加关键字搜索条件
  if (keywordSearch) {
    condition.quickFuzzy(keywordSearch);
  }

  // 添加字段筛选条件
  Object.entries(fieldFilters).forEach(([field, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      const fieldMeta = getFieldByName(field);
      const operator = fieldMeta.condition?.defaultOperator || '__EQ';

      addConditionByOperator(condition, field, operator, value);
    }
  });

  // 添加高级筛选条件
  if (advancedFilters.length > 0) {
    // 根据逻辑类型添加条件
    if (advancedLogic === '__OR') {
      condition.or(subCond => {
        advancedFilters.forEach(filter => {
          addConditionByOperator(subCond, filter.field, filter.compare, filter.value);
        });
      });
    } else {
      // 默认为AND逻辑
      advancedFilters.forEach(filter => {
        addConditionByOperator(condition, filter.field, filter.compare, filter.value);
      });
    }
  }

  return condition;
}
```

### 5.2 操作符处理

根据不同的操作符类型，需要不同的条件构建逻辑：

```typescript
function addConditionByOperator(condition: Condition, field: string, operator: Compare, value: any): void {
  switch (operator) {
    case '__EQ':
      condition.eq(field, value);
      break;
    case '__NE':
      condition.ne(field, value);
      break;
    case '__GT':
      condition.gt(field, value);
      break;
    case '__GE':
      condition.ge(field, value);
      break;
    case '__LT':
      condition.lt(field, value);
      break;
    case '__LE':
      condition.le(field, value);
      break;
    case '__LIKE':
      condition.like(field, value);
      break;
    case '__L_LIKE':
      condition.likeLeft(field, value);
      break;
    case '__R_LIKE':
      condition.likeRight(field, value);
      break;
    case '__IN':
      condition.in(field, Array.isArray(value) ? value : [value]);
      break;
    case '__BETWEEN':
      if (Array.isArray(value) && value.length === 2) {
        condition.between(field, value[0], value[1]);
      }
      break;
    case '__IS_NULL':
      condition.isNull(field);
      break;
    case '__IS_NOT_NULL':
      condition.isNotNull(field);
      break;
    default:
      condition.eq(field, value);
  }
}
```

## 6. 集成到EntityList组件

将筛选功能集成到EntityList组件中，主要包括以下步骤：

1. 在EntityList组件中添加筛选组件
2. 处理筛选条件变更事件
3. 更新查询参数并重新获取数据

```jsx
function EntityList({ entityName, metadata }) {
  // 现有状态...
  const [condition, setCondition] = useState({});

  // 处理筛选条件变更
  const handleFilterChange = (newCondition) => {
    setCondition(newCondition);
    // 重置分页
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">{metadata.display_name}列表</h1>
        <Button onClick={() => setDialog({ open: true, type: 'create' })}>
          <Plus className="mr-2 h-4 w-4" />
          新建{metadata.display_name}
        </Button>
      </div>

      <EntityListFilter
        metadata={metadata}
        onFilterChange={handleFilterChange}
        defaultCondition={condition}
      />

      <DataTable
        columns={columns}
        data={data?.items || []}
        isLoading={isLoading}
        pagination={{
          pageCount: data ? Math.ceil(data.total / pagination.pageSize) : 0,
          page: pagination.page - 1,
          pageSize: pagination.pageSize,
          onPageChange: (page) => setPagination(prev => ({ ...prev, page: page + 1 })),
          onPageSizeChange: (size) => setPagination({ page: 1, pageSize: size }),
        }}
      />

      {/* 其他组件... */}
    </div>
  );
}
```

## 7. 技术挑战与解决方案

### 7.1 复杂查询条件的构建

**挑战**：构建复杂的嵌套查询条件，支持多种操作符和逻辑组合。

**解决方案**：

- 利用Condition类的嵌套结构支持复杂条件
- 实现操作符映射和处理函数
- 使用递归方式处理嵌套条件

### 7.2 查询UI的灵活性

**挑战**：根据不同字段类型提供合适的查询控件，支持不同的操作符。

**解决方案**：

- 基于元数据动态生成查询表单
- 实现字段类型到控件类型的映射
- 支持自定义操作符和控件配置

### 7.3 查询性能优化

**挑战**：处理大量数据和复杂查询条件时的性能问题。

**解决方案**：

- 实现查询条件的延迟构建和防抖处理
- 优化条件对象的序列化过程
- 使用缓存减少重复计算

## 8. 实现路径

1. **扩展EntityField类型**：添加condition字段，定义查询相关配置
2. **实现基础组件**：开发KeywordSearch和FieldFilter组件
3. **实现AdvancedFilter**：开发高级筛选组件
4. **实现EntityListFilter**：整合基础组件，实现完整的筛选功能
5. **集成到EntityList**：将筛选功能集成到EntityList组件中
6. **实现条件构建逻辑**：开发Condition对象的构建和转换逻辑
7. **测试和优化**：测试筛选功能，优化性能和用户体验

## 9. 后续扩展

1. **条件保存和复用**：支持将常用查询条件保存为预设，方便用户快速应用
2. **更复杂的条件组合**：支持更复杂的条件嵌套和组合方式
3. **更多类型的查询控件**：支持更多特殊类型的查询控件，如地理位置、范围选择等
4. **查询条件可视化**：提供查询条件的可视化展示，帮助用户理解复杂查询
5. **查询性能分析**：提供查询性能分析工具，帮助优化查询条件
