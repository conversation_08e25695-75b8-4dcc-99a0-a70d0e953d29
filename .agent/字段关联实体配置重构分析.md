# 字段关联实体配置重构分析

## 1. 需求概述

1. 字段关联实体的配置需要重构成服务端可以共用的配置结构，允许为带出的关联实体信息字段定义自定义名称
2. 服务端 GraphQL 引擎动态获取关联数据，如人员关联部门时，查询时可以带出部门信息，列表或表单中的关联数据展示不需要二次请求

## 2. 现状分析

### 2.1 现有关联配置结构

目前系统中已有 `RelationConfig` 接口，用于配置字段与其他实体的关联关系：

```typescript
export interface RelationConfig {
  entity: string;              // 关联实体名称
  module?: string;             // 关联实体所属模块，默认为meta
  valueField?: string;         // 值字段，默认为_id
  labelField?: string;         // 标签字段，默认为name
  displayFields?: string[];    // 显示字段列表
  multiple?: boolean;          // 是否多选
  cascade?: {                  // 级联配置
    parent?: string;           // 父级字段
    children?: string;         // 子级字段
  };
  filter?: {                   // 过滤条件
    condition?: string;        // 条件表达式
    dependsOn?: string[];      // 依赖字段
  };
  searchable?: boolean;        // 是否可搜索
  createable?: boolean;        // 是否可创建
  previewable?: boolean;       // 是否可预览
}
```

这个配置主要用于前端的关联选择控件，但缺少服务端可共用的结构，无法支持 GraphQL 引擎动态获取关联数据。

### 2.2 现有查询生成器

当前的 `QueryGenerator` 类负责生成 GraphQL 查询，但不支持关联数据的自动获取：

```typescript
static generateDetailQuery(entityMetadata: EntityMetadata): string {
  const { name, fields } = entityMetadata
  const fieldNames = this.generateFieldsSelection(fields)
  
  return `
    query Get${name}Detail($_id: ID!) {
      ${name}_detail(_id: $_id) {
        ${fieldNames}
      }
    }
  `
}
```

### 2.3 现有关联数据处理

系统已有关联数据获取的 API 和缓存机制，但缺少与 GraphQL 查询的集成：

```typescript
export const fetchRelationData = async <T>(
  entity: string,
  module: string = 'meta',
  condition: Record<string, unknown> = {},
  page: number = 1,
  pageSize: number = 10,
  useCache: boolean = true
): Promise<PageResponse<T>> => {
  // ...
}
```

## 3. 重构方案

### 3.1 服务端共用的关联配置结构

需要定义一个新的 `EntityRelation` 接口，用于描述实体间的关联关系，并扩展 `EntityMetadata` 接口：

```typescript
/**
 * 实体关联信息
 */
export interface EntityRelation {
  field: string;             // 关联字段名
  entity: string;            // 关联实体名
  module?: string;           // 关联实体所属模块
  type: 'one' | 'many';      // 关联类型：一对一或一对多
  foreignField?: string;     // 外键字段，默认为_id
  displayFields?: string[];  // 显示字段列表
  alias?: string;            // 关联数据别名，默认为 ${field}_relation
}

/**
 * 扩展实体元数据，增加关联信息
 */
export interface EntityMetadata {
  // 现有字段...
  relations?: EntityRelation[]; // 实体关联信息
}
```

### 3.2 关联信息提取

从现有的 `RelationConfig` 中提取关联信息，转换为服务端可用的结构：

```typescript
/**
 * 从实体元数据中提取关联信息
 * @param metadata 实体元数据
 * @returns 关联信息数组
 */
export const extractRelations = (metadata: EntityMetadata): EntityRelation[] => {
  const relations: EntityRelation[] = [];

  metadata.fields.forEach(field => {
    if (field.control?.relation) {
      const relation = field.control.relation;
      const alias = relation.relationField || `${field.name}_relation`;

      relations.push({
        field: field.name,
        entity: relation.entity,
        module: relation.module || 'meta',
        type: relation.multiple ? 'many' : 'one',
        foreignField: relation.valueField || '_id',
        displayFields: relation.displayFields || [relation.labelField || 'name'],
        alias
      });
    }
  });

  return relations;
}
```

### 3.3 查询生成器扩展

扩展 `QueryGenerator` 类，支持关联数据查询：

```typescript
/**
 * 生成包含关联数据的详情查询
 * @param metadata 实体元数据
 * @param includeRelations 是否包含关联数据
 * @returns GraphQL查询字符串
 */
static generateDetailQuery(
  metadata: EntityMetadata,
  includeRelations: boolean = true
): string {
  const { name, fields } = metadata;

  // 基本字段
  const fieldNames = this.generateFieldsSelection(fields);

  // 如果不包含关联数据，直接返回基本查询
  if (!includeRelations) {
    return `
      query Get${name}Detail($_id: ID!) {
        ${name}_detail(_id: $_id) {
          ${fieldNames}
        }
      }
    `;
  }

  // 提取关联信息
  const relations = extractRelations(metadata);

  // 关联字段查询
  const relationQueries = relations.map(relation => {
    const displayFields = relation.displayFields?.join('\n        ') || '_id';

    return `
    ${relation.alias} {
      ${displayFields}
    }`;
  }).join('');

  return `
    query Get${name}Detail($_id: ID!) {
      ${name}_detail(_id: $_id) {
        ${fieldNames}
        ${relationQueries}
      }
    }
  `;
}
```

## 4. 实现计划

### 4.1 类型定义

1. 在 `src/features/metadata/types/relation.ts` 中定义 `EntityRelation` 接口
2. 扩展 `src/features/metadata/types/index.ts` 中的 `EntityMetadata` 接口，增加 `relations` 字段
3. 扩展 `src/features/metadata/types/index.ts` 中的 `RelationConfig` 接口，增加 `relationField` 字段

### 4.2 关联信息处理

1. 创建 `src/lib/relation/extractor.ts` 文件，实现关联信息提取功能
2. 创建 `src/lib/relation/processor.ts` 文件，实现关联数据处理功能

### 4.3 查询生成器扩展

1. 扩展 `src/lib/graphql/query-generator.ts`，支持关联数据查询
2. 实现 `generateDetailQueryWithRelations` 和 `generatePageQueryWithRelations` 方法

### 4.4 GraphQL 客户端扩展

1. 扩展 `src/lib/graphql/client.ts`，支持关联数据的处理
2. 实现关联数据的自动合并功能

## 5. 测试计划

1. 创建包含关联字段的测试实体元数据
2. 测试关联信息提取功能
3. 测试查询生成器扩展功能
4. 测试关联数据处理功能
5. 测试前端组件与关联数据的集成
