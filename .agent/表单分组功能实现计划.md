# 表单分组功能实现计划

## 1. 实现步骤概述

1. 创建 FieldGroup 组件，用于渲染分组
2. 实现分组处理函数，将字段按照 `control.layout.group` 属性分组
3. 修改 EntityForm 组件，使用分组逻辑渲染字段
4. 创建测试用例和示例数据，验证分组功能

## 2. 详细实现步骤

### 2.1 创建 FieldGroup 组件

创建新文件 `src/features/entity/components/FieldGroup.tsx`，实现分组组件：

```tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { EntityField as EntityFieldType } from '@/features/metadata/types';
import { EntityField } from './EntityField';
import { FieldContainer } from './FieldContainer';

interface FieldGroupProps {
  title: string;
  fields: EntityFieldType[];
  readOnly?: boolean;
}

/**
 * 字段分组组件
 * 将同一组的字段包装在一个卡片中
 */
export function FieldGroup({ title, fields, readOnly = false }: FieldGroupProps) {
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          {fields.map((field) => {
            // 判断字段是否应该占据整行
            const isFullWidth = field.type === 'TEXT' || 
                               field.type === 'JSON' || 
                               (field.control?.layout?.span === 2);
            
            return (
              <FieldContainer
                key={field._id}
                field={field}
                isFullWidth={isFullWidth}
              >
                <EntityField field={field} readOnly={readOnly} />
              </FieldContainer>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 2.2 实现分组处理函数

创建新文件 `src/features/entity/utils/groupUtils.ts`，实现分组处理函数：

```typescript
import { EntityField } from '@/features/metadata/types';

/**
 * 将字段按照分组进行分类
 * @param fields 字段列表
 * @param defaultGroupName 默认分组名称
 * @returns 分组后的字段映射
 */
export function groupFields(
  fields: EntityField[], 
  defaultGroupName: string = '基本信息'
): Record<string, EntityField[]> {
  const groups: Record<string, EntityField[]> = {};
  
  // 遍历所有字段，按照分组进行分类
  fields.forEach(field => {
    const groupName = field.control?.layout?.group || defaultGroupName;
    
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    
    groups[groupName].push(field);
  });
  
  // 对每个分组内的字段按照 order 排序
  Object.keys(groups).forEach(groupName => {
    groups[groupName].sort((a, b) => {
      const orderA = a.control?.layout?.order || 0;
      const orderB = b.control?.layout?.order || 0;
      return orderA - orderB;
    });
  });
  
  return groups;
}

/**
 * 获取分组顺序
 * 可以根据需要自定义分组的显示顺序
 * @param groupNames 分组名称列表
 * @param defaultGroupName 默认分组名称
 * @returns 排序后的分组名称列表
 */
export function getGroupOrder(
  groupNames: string[], 
  defaultGroupName: string = '基本信息'
): string[] {
  // 将默认分组放在最前面，其他分组按字母顺序排序
  return [
    ...groupNames.filter(name => name === defaultGroupName),
    ...groupNames.filter(name => name !== defaultGroupName).sort()
  ];
}
```

### 2.3 修改 EntityForm 组件

修改 `src/features/entity/components/EntityForm.tsx` 文件，使用分组逻辑渲染字段：

```tsx
// 导入分组相关组件和函数
import { FieldGroup } from './FieldGroup';
import { groupFields, getGroupOrder } from '../utils/groupUtils';

// 在 EntityForm 组件中
function EntityForm({ /* 现有参数 */ }) {
  // 现有代码...

  // 获取可编辑字段
  const editableFields = getEditableFields(metadata);

  // 将字段按照分组进行分类
  const fieldGroups = groupFields(editableFields);
  
  // 获取分组顺序
  const groupOrder = getGroupOrder(Object.keys(fieldGroups));

  return (
    <OptionsProvider>
      <Form {...form}>
        <LinkageProvider
          fields={editableFields}
          formState={{ ...formState, mode: isEdit ? 'edit' : 'create' }}
          userRole={userRole}
          config={config}
          context={context}
        >
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6 pb-2'>
            {/* 渲染各个分组 */}
            {groupOrder.map(groupName => (
              <FieldGroup
                key={groupName}
                title={groupName}
                fields={fieldGroups[groupName]}
                readOnly={readOnly}
              />
            ))}
          </form>
        </LinkageProvider>
      </Form>
    </OptionsProvider>
  );
}
```

### 2.4 创建测试用例和示例数据

创建新文件 `src/examples/group-form-example.tsx`，实现分组表单示例：

```tsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { OptionsProvider } from '@/lib/fields/options-context';
import { LinkageProvider } from '@/lib/linkage';
import { Form } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EntityMetadata } from '@/features/metadata/types';
import { FieldGroup } from '@/features/entity/components/FieldGroup';
import { groupFields, getGroupOrder } from '@/features/entity/utils/groupUtils';

/**
 * 分组表单示例
 */
export function GroupFormExample() {
  // 创建表单
  const form = useForm({
    defaultValues: {
      name: '',
      code: '',
      description: '',
      status: 'active',
      price: 0,
      stock: 0,
      category: '',
      tags: [],
      image_url: '',
      created_at: new Date().toISOString(),
    },
  });

  // 提交表单
  const onSubmit = (data: Record<string, unknown>) => {
    console.log('表单数据:', data);
    alert('表单提交成功！');
  };

  // 测试元数据
  const testMetadata: EntityMetadata = {
    name: 'product',
    display_name: '产品',
    module: 'test',
    description: '产品信息',
    _id: 'product_id',
    fields: [
      // 基本信息组
      {
        name: 'name',
        display_name: '产品名称',
        type: 'VARCHAR',
        default_value: '',
        flags: [],
        _id: 'name_id',
        control: {
          layout: {
            group: '基本信息',
            order: 1
          }
        }
      },
      {
        name: 'code',
        display_name: '产品编码',
        type: 'VARCHAR',
        default_value: '',
        flags: [],
        _id: 'code_id',
        control: {
          layout: {
            group: '基本信息',
            order: 2
          }
        }
      },
      {
        name: 'status',
        display_name: '状态',
        type: 'ENUM',
        default_value: 'active',
        flags: [],
        _id: 'status_id',
        options: {
          values: [
            { label: '上架', value: 'active' },
            { label: '下架', value: 'inactive' }
          ]
        },
        control: {
          layout: {
            group: '基本信息',
            order: 3
          }
        }
      },
      
      // 详细信息组
      {
        name: 'description',
        display_name: '产品描述',
        type: 'TEXT',
        default_value: '',
        flags: ['NULLABLE'],
        _id: 'description_id',
        control: {
          layout: {
            group: '详细信息',
            order: 1,
            span: 2
          }
        }
      },
      {
        name: 'category',
        display_name: '产品分类',
        type: 'VARCHAR',
        default_value: '',
        flags: [],
        _id: 'category_id',
        control: {
          layout: {
            group: '详细信息',
            order: 2
          }
        }
      },
      {
        name: 'tags',
        display_name: '标签',
        type: 'VARCHAR',
        default_value: '',
        flags: ['NULLABLE'],
        _id: 'tags_id',
        control: {
          layout: {
            group: '详细信息',
            order: 3
          }
        }
      },
      
      // 价格库存组
      {
        name: 'price',
        display_name: '价格',
        type: 'FLOAT',
        default_value: '0',
        flags: [],
        _id: 'price_id',
        control: {
          type: 'currency',
          layout: {
            group: '价格库存',
            order: 1
          }
        }
      },
      {
        name: 'stock',
        display_name: '库存',
        type: 'INT',
        default_value: '0',
        flags: [],
        _id: 'stock_id',
        control: {
          layout: {
            group: '价格库存',
            order: 2
          }
        }
      },
      
      // 未分组字段（将归入默认分组）
      {
        name: 'image_url',
        display_name: '产品图片',
        type: 'VARCHAR',
        default_value: '',
        flags: ['NULLABLE'],
        _id: 'image_url_id'
      },
      {
        name: 'created_at',
        display_name: '创建时间',
        type: 'DATE_TIME',
        default_value: null,
        flags: [],
        _id: 'created_at_id'
      }
    ]
  };

  // 将字段按照分组进行分类
  const fieldGroups = groupFields(testMetadata.fields);
  
  // 获取分组顺序
  const groupOrder = getGroupOrder(Object.keys(fieldGroups));

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>分组表单示例</CardTitle>
      </CardHeader>
      <CardContent>
        <OptionsProvider>
          <Form {...form}>
            <LinkageProvider
              fields={testMetadata.fields}
              formState={{ mode: 'create' }}
              userRole={{}}
              config={{}}
              context={{}}
            >
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* 渲染各个分组 */}
                {groupOrder.map(groupName => (
                  <FieldGroup
                    key={groupName}
                    title={groupName}
                    fields={fieldGroups[groupName]}
                    readOnly={false}
                  />
                ))}
                
                <div className="flex justify-end">
                  <Button type="submit">提交</Button>
                </div>
              </form>
            </LinkageProvider>
          </Form>
        </OptionsProvider>
      </CardContent>
    </Card>
  );
}
```

## 3. 测试计划

1. 创建包含多个分组的测试实体元数据
2. 实现分组表单示例页面，验证分组功能
3. 测试字段联动在分组环境下的表现
4. 测试不同屏幕尺寸下的响应式布局

## 4. 实施时间表

| 任务               | 预计时间 |
|------------------|------|
| 创建 FieldGroup 组件 | 1小时  |
| 实现分组处理函数         | 1小时  |
| 修改 EntityForm 组件 | 2小时  |
| 创建测试用例和示例数据      | 2小时  |
| 测试和调试            | 2小时  |
| 总计               | 8小时  |

## 5. 注意事项

1. 确保分组功能不影响现有的表单功能
2. 保持代码风格一致
3. 添加适当的注释和文档
4. 考虑边缘情况，如空分组、单个字段的分组等
