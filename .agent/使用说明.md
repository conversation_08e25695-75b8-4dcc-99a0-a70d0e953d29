# 低代码平台前端使用说明

## 1. 项目简介

本项目是基于shadcn-admin开发的低代码平台前端，采用元数据驱动的开发方式，使用GraphQL规范与后端进行通信，并实现统一范式的CRUD组件。

## 2. 环境要求

- Node.js 18+
- pnpm 8+

## 3. 安装与运行

### 3.1 安装依赖

```bash
pnpm install
```

### 3.2 配置环境变量

创建`.env.local`文件，配置以下环境变量：

```
VITE_GRAPHQL_ENDPOINT=http://your-api-endpoint/graphql
```

### 3.3 启动开发服务器

```bash
pnpm dev
```

### 3.4 构建生产版本

```bash
pnpm build
```

### 3.5 预览生产版本

```bash
pnpm preview
```

## 4. 项目结构

```
src/
├── features/
│   ├── metadata/           # 元数据相关功能
│   │   ├── api/            # 元数据API调用
│   │   ├── hooks/          # 元数据相关钩子函数
│   │   ├── types/          # 元数据类型定义
│   │   └── utils/          # 元数据工具函数
│   │
│   ├── entity/             # 实体通用功能
│       ├── api/            # 实体API调用
│       ├── components/     # 通用CRUD组件
│       ├── hooks/          # 实体相关钩子函数
│       └── utils/          # 实体工具函数
│
├── lib/
│   ├── graphql/            # GraphQL相关功能
│   └── metadata/           # 元数据处理工具
│
├── routes/                 # 路由定义
```

## 5. 功能说明

### 5.1 元数据管理

元数据管理页面位于`/metadata`路径，用于查看和管理所有实体元数据。

### 5.2 实体管理

实体管理页面位于`/entity/:name`路径，用于查看和管理指定实体的数据。

#### 5.2.1 实体列表

实体列表页面展示实体数据列表，支持分页、排序、筛选等功能。

#### 5.2.2 实体详情

实体详情页面位于`/entity/:name/:id`路径，用于查看实体详细信息。

#### 5.2.3 实体表单

实体表单用于创建和编辑实体，支持各种类型的字段。

## 6. 开发指南

### 6.1 添加新的实体

1. 在后端添加新的实体元数据
2. 前端无需额外配置，系统会自动根据元数据生成对应的页面和表单

### 6.2 自定义实体页面

如果需要自定义特定实体的页面，可以在`src/routes/_authenticated/entity/$name`目录下创建对应的路由文件。

### 6.3 自定义实体表单

如果需要自定义特定实体的表单，可以在`src/features/entity/components`目录下创建对应的组件。

## 7. API说明

### 7.1 元数据API

- 获取元数据：`_metadata(key: ID!): JSON`

### 7.2 实体API

- 获取实体详情：`{entity_name}_detail(_id: ID!): {entity_name}`
- 获取实体分页：`{entity_name}_page(condition: JSON, page: Int, page_size: Int): {entity_name}_page`
- 创建实体：`{entity_name}_insert(input: {entity_name}_input!): {entity_name}`
- 更新实体：`{entity_name}_update(_id: ID!, input: {entity_name}_input!): {entity_name}`
- 删除实体：`{entity_name}_delete(_id: ID!): Boolean`

## 8. 常见问题

### 8.1 如何添加自定义字段类型？

在`src/lib/metadata/parser.ts`文件中的`getFieldControlType`函数中添加新的字段类型映射。

### 8.2 如何添加自定义表单验证规则？

在`src/lib/metadata/validator.ts`文件中的`generateFieldValidation`函数中添加新的验证规则。

### 8.3 如何添加自定义表单控件？

在`src/features/entity/components/EntityField.tsx`文件中的`renderFieldControl`函数中添加新的控件渲染逻辑。

## 9. 故障排除

### 9.1 GraphQL请求失败

- 检查GraphQL端点配置是否正确
- 检查网络连接是否正常
- 检查认证信息是否有效

### 9.2 表单提交失败

- 检查表单数据是否符合验证规则
- 检查GraphQL变更操作是否正确
- 检查后端是否返回错误信息

### 9.3 页面加载失败

- 检查路由配置是否正确
- 检查元数据是否存在
- 检查数据获取是否成功
