# 双引擎驱动低代码平台实现文档

## 1. 概述

本文档介绍了双引擎驱动的低代码平台的设计和实现，该平台同时支持注解驱动和元数据驱动两种模式，为开发者提供了灵活的元数据定义方式。

## 2. 架构设计

### 2.1 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    双引擎元数据管理器                        │
│                  (DualEngineMetaManager)                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  注解元数据提供者  │    │  配置元数据提供者  │                │
│  │ AnnotationMeta  │    │  ConfigMeta     │                │
│  │    Provider     │    │   Provider      │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│                    统一元数据模型                            │
│                   (MetaDefinition)                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │    注解驱动      │    │    配置驱动      │                │
│  │   @Entity       │    │   JSON/YAML     │                │
│  │   @Field        │    │   配置文件       │                │
│  │   @Relation     │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 关键特性

1. **双引擎协同**：注解驱动和配置驱动可以同时工作
2. **优先级策略**：支持多种合并策略，可配置优先级
3. **统一API**：提供统一的元数据访问接口
4. **向后兼容**：兼容现有的MetaManager API
5. **缓存支持**：支持元数据缓存，提高性能
6. **版本控制**：支持元数据版本管理

## 3. 注解驱动引擎

### 3.1 核心注解

#### @Entity - 实体注解

```java
@Entity(
    module = "demo", 
    name = "product", 
    displayName = "产品",
    type = EntityType.TABLE
)
public class Product {
    // 字段定义
}
```

#### @Field - 字段注解

```java
@Field(
    name = "name",
    displayName = "产品名称",
    size = 100,
    flags = {FieldFlag.REQUIRED},
    validations = {
        @Field.Validation(
            type = "required",
            message = "产品名称不能为空"
        )
    },
    uiControl = @Field.UIControl(
        type = "input",
        placeholder = "请输入产品名称",
        group = "基本信息",
        order = 1
    )
)
private String name;
```

#### @Relation - 关联注解

```java
@Relation(
    name = "category",
    displayName = "产品分类",
    entity = "category",
    module = "demo",
    type = RelationType.REF_ONE,
    refField = "category_id"
)
private Object category;
```

#### @MetaSource - 元数据来源注解

```java
@MetaSource(
    source = MetaSource.SourceType.ANNOTATION,
    priority = 100,
    overridable = true
)
public class Product {
    // 实体定义
}
```

### 3.2 验证规则支持

支持多种验证规则类型：

- **required**: 必填验证
- **min/max**: 最小/最大值验证
- **pattern**: 正则表达式验证
- **email**: 邮箱格式验证
- **url**: URL格式验证
- **custom**: 自定义验证
- **async**: 异步验证

### 3.3 UI控件支持

支持丰富的UI控件配置：

- **input**: 文本输入框
- **textarea**: 多行文本框
- **number**: 数字输入框
- **select**: 下拉选择框
- **switch**: 开关控件
- **datetime**: 日期时间选择器
- **color-picker**: 颜色选择器
- **icon-picker**: 图标选择器

## 4. 配置驱动引擎

### 4.1 配置文件格式

支持JSON格式的配置文件，放置在`src/main/resources/meta/`目录下：

```json
{
  "name": "category",
  "displayName": "产品分类",
  "module": "demo",
  "source": "CONFIG",
  "priority": 50,
  "fields": [
    {
      "name": "name",
      "displayName": "分类名称",
      "dataType": "TEXT",
      "validationRules": [
        {
          "type": "REQUIRED",
          "message": "分类名称不能为空"
        }
      ],
      "uiControl": {
        "type": "input",
        "placeholder": "请输入分类名称"
      }
    }
  ]
}
```

### 4.2 配置文件扫描

系统会自动扫描以下路径的配置文件：

- `classpath*:meta/**/*.json`
- `classpath*:meta/**/*.yaml`
- `classpath*:meta/**/*.yml`

## 5. 双引擎协同机制

### 5.1 合并策略

支持四种合并策略：

1. **PRIORITY_BASED**: 基于优先级合并（默认）
2. **ANNOTATION_FIRST**: 注解优先
3. **CONFIG_FIRST**: 配置优先
4. **DEEP_MERGE**: 深度合并

### 5.2 优先级规则

- 注解驱动默认优先级：100
- 配置驱动默认优先级：50
- 数值越大优先级越高

### 5.3 配置示例

```yaml
vibe:
  meta:
    dual-engine-enabled: true
    merge-strategy: PRIORITY_BASED
    cache-enabled: true
    cache-expire-seconds: 3600
    entities:
      product:
        source: ANNOTATION
        cache-enabled: true
      category:
        source: CONFIG
        config-path: "classpath:meta/demo/category.json"
```

## 6. API使用

### 6.1 获取元数据

```java
// 通过双引擎管理器获取
@Autowired
private DualEngineMetaManager dualEngineMetaManager;

// 获取单个实体元数据
MetaDefinition definition = dualEngineMetaManager.getMetaDefinition("product");

// 获取所有元数据
List<MetaDefinition> allDefinitions = dualEngineMetaManager.getAllMetaDefinitions();

// 按模块获取元数据
List<MetaDefinition> demoDefinitions = dualEngineMetaManager.getMetaDefinitionsByModule("demo");
```

### 6.2 兼容现有API

```java
// 现有的MetaManager API仍然可用
DefEntity entity = MetaManager.get("product");
List<DefEntity> entities = MetaManager.getAll();
List<DefEntity> demoEntities = MetaManager.getByModule("demo");
```

## 7. 扩展功能

### 7.1 自定义元数据提供者

```java
@Component
public class DatabaseMetaProvider implements IMetaProvider {
    
    @Override
    public MetaSource.SourceType getSourceType() {
        return MetaSource.SourceType.DATABASE;
    }
    
    @Override
    public MetaDefinition getMetaDefinition(String entityName) {
        // 从数据库加载元数据
        return loadFromDatabase(entityName);
    }
    
    // 其他方法实现...
}
```

### 7.2 元数据版本控制

```java
// 获取元数据版本
String version = metaProvider.getVersion("product");

// 版本比较和迁移
if (needsMigration(version)) {
    migrateMetadata("product", version);
}
```

## 8. 最佳实践

### 8.1 选择合适的驱动方式

- **注解驱动**：适用于代码驱动的开发模式，类型安全，IDE支持好
- **配置驱动**：适用于动态配置，运行时修改，非技术人员维护

### 8.2 合理设置优先级

- 核心业务实体使用注解驱动，设置高优先级
- 配置性实体使用配置驱动，设置低优先级
- 允许配置覆盖注解的场景设置overridable=true

### 8.3 性能优化

- 启用缓存减少重复解析
- 合理设置缓存过期时间
- 使用懒加载避免启动时间过长

## 9. 测试

### 9.1 单元测试

```java
@Test
void testDualEngineMetaManager() {
    MetaDefinition definition = dualEngineMetaManager.getMetaDefinition("product");
    assertNotNull(definition);
    assertEquals("product", definition.getName());
}
```

### 9.2 集成测试

```java
@SpringBootTest
class DualEngineIntegrationTest {
    
    @Autowired
    private DualEngineMetaManager metaManager;
    
    @Test
    void testAnnotationAndConfigMerge() {
        // 测试注解和配置的合并
    }
}
```

## 10. 故障排除

### 10.1 常见问题

1. **元数据未找到**：检查包扫描路径和配置文件路径
2. **合并结果不符合预期**：检查优先级设置和合并策略
3. **性能问题**：启用缓存，检查扫描范围

### 10.2 调试技巧

- 启用DEBUG日志查看元数据加载过程
- 使用actuator端点查看元数据状态
- 通过JMX监控缓存命中率

## 11. 总结

双引擎驱动的低代码平台提供了灵活的元数据定义方式，既保持了代码的类型安全性，又提供了配置的灵活性。通过合理的架构设计和优先级策略，实现了两种驱动方式的完美融合，为低代码平台的发展奠定了坚实的基础。
