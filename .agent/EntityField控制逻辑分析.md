# EntityField组件控制逻辑分析

## 1. 组件概述

`EntityField`组件是低代码平台前端的核心组件之一，负责根据元数据动态渲染不同类型的表单字段控件。该组件采用元数据驱动的方式，根据字段的类型、标志和前端控制配置，自动选择合适的表单控件，并处理数据绑定、验证和交互逻辑。

## 2. 数据结构分析

### 2.1 元数据结构优化

为了更好地支持前端控制逻辑，同时保持服务端和前端职责分离，我们需要优化`EntityField`的数据结构：

```typescript
// 原有的EntityField结构
interface EntityField {
  name: string                // 字段名称
  display_name: string        // 显示名称
  type: string                // 字段类型（服务端数据类型）
  default_value: string | null // 默认值
  flags: string[]             // 标志数组
  _id: string                 // 唯一标识

  // 新增字段
  control?: FieldControl      // 前端控制配置
  options?: FieldOptions      // 选项配置（如枚举值）
  validation?: FieldValidation // 验证规则（服务端和前端共用）
}
```

### 2.2 前端控制配置

`control`字段专门用于前端控制逻辑，不影响服务端处理：

```typescript
interface FieldControl {
  type?: string               // 指定前端控件类型（覆盖默认映射）
  props?: Record<string, any> // 传递给控件的属性
  formatter?: string | {      // 值格式化配置
    display?: string          // 显示格式化（如日期格式）
    edit?: string             // 编辑格式化
  }
  visibility?: {              // 条件显示规则
    condition: string         // 条件表达式
    dependsOn: string[]       // 依赖的其他字段
  }
  linkage?: {                 // 字段联动规则
    target: string            // 目标字段
    effect: string            // 联动效果（如"setValue", "setOptions"）
    condition?: string        // 触发条件
    mapping?: Record<string, any> // 值映射
  }[]
  layout?: {                  // 布局配置
    span?: number             // 栅格跨度
    order?: number            // 显示顺序
    group?: string            // 分组名称
  }
}
```

### 2.3 选项配置

`options`字段用于配置选项类型字段（如下拉选择、单选、多选等）：

```typescript
interface FieldOptions {
  values?: {                  // 静态选项值
    label: string             // 显示标签
    value: string | number    // 实际值
    disabled?: boolean        // 是否禁用
  }[]
  source?: {                  // 动态选项来源
    type: 'entity' | 'api' | 'function' // 来源类型
    entity?: string           // 实体名称
    api?: string              // API路径
    function?: string         // 函数名称
    params?: Record<string, any> // 参数
    labelField?: string       // 标签字段
    valueField?: string       // 值字段
  }
  multiple?: boolean          // 是否多选
  filterable?: boolean        // 是否可筛选
}
```

### 2.4 验证规则

`validation`字段用于配置验证规则，服务端和前端共用。为了支持多个验证规则和条件启用，我们将其设计为数组：

```typescript
interface EntityField {
  // 其他字段保持不变...

  validation?: ValidationRule[] // 验证规则数组
}

interface ValidationRule {
  type: string               // 验证类型（required, min, max, pattern等）
  params?: any               // 验证参数
  message?: string           // 错误消息
  condition?: string         // 启用条件表达式
  dependsOn?: string[]       // 条件依赖的字段
}
```

常见的验证规则类型：

```typescript
// 验证规则类型
type ValidationRuleType =
  | 'required'      // 必填
  | 'min'           // 最小值/长度
  | 'max'           // 最大值/长度
  | 'range'         // 范围
  | 'pattern'       // 正则表达式
  | 'email'         // 电子邮件
  | 'url'           // URL
  | 'custom'        // 自定义验证
  | 'async'         // 异步验证（如远程验证）
  | 'unique'        // 唯一性验证
  | 'dependency'    // 依赖字段验证
```

## 3. 控制逻辑分析

### 3.1 控件类型确定逻辑

1. **优先级顺序**：
    - 首先检查`field.control.type`是否存在，如果存在则使用指定的控件类型
    - 如果不存在，则根据`field.type`和`getFieldControlType`函数确定默认控件类型

```typescript
function determineControlType(field: EntityField): string {
  // 优先使用前端指定的控件类型
  if (field.control?.type) {
    return field.control.type;
  }

  // 其次检查是否有选项配置，有则使用选择类控件
  if (field.options) {
    return field.options.multiple ? 'multiSelect' : 'select';
  }

  // 最后使用默认映射
  return getFieldControlType(field.type);
}
```

### 3.2 条件显示逻辑

实现基于表达式的条件显示：

```typescript
function evaluateVisibility(field: EntityField, formValues: Record<string, any>): boolean {
  if (!field.control?.visibility) {
    return true; // 默认显示
  }

  const { condition, dependsOn } = field.control.visibility;

  // 检查依赖字段的值
  const dependencyValues = {};
  dependsOn.forEach(fieldName => {
    dependencyValues[fieldName] = formValues[fieldName];
  });

  // 评估条件表达式
  // 可以使用Function构造函数或表达式解析库
  try {
    const evalFn = new Function(
      ...Object.keys(dependencyValues),
      `return ${condition};`
    );
    return evalFn(...Object.values(dependencyValues));
  } catch (error) {
    console.error('Error evaluating visibility condition:', error);
    return true; // 出错时默认显示
  }
}
```

### 3.3 值格式化逻辑

处理不同场景下的值格式化：

```typescript
function formatFieldValue(field: EntityField, value: any, mode: 'display' | 'edit' = 'display'): any {
  if (!field.control?.formatter) {
    return value; // 无格式化配置，返回原值
  }

  const formatter = field.control.formatter;

  // 如果formatter是字符串，直接使用同一格式
  if (typeof formatter === 'string') {
    return applyFormat(value, formatter);
  }

  // 否则根据模式选择格式
  const formatStr = mode === 'display' ? formatter.display : formatter.edit;
  if (!formatStr) {
    return value;
  }

  return applyFormat(value, formatStr);
}

// 应用格式化
function applyFormat(value: any, format: string): any {
  // 处理日期格式化
  if (format.startsWith('date:')) {
    const dateFormat = format.substring(5);
    return format(new Date(value), dateFormat);
  }

  // 处理数字格式化
  if (format.startsWith('number:')) {
    const numberFormat = format.substring(7);
    return new Intl.NumberFormat(undefined, JSON.parse(numberFormat)).format(value);
  }

  // 处理自定义格式化函数
  if (format.startsWith('function:')) {
    const fnName = format.substring(9);
    // 从全局格式化函数注册表中获取函数
    const formatFn = window.formatters?.[fnName];
    if (typeof formatFn === 'function') {
      return formatFn(value);
    }
  }

  return value;
}
```

### 3.4 字段联动逻辑

实现字段之间的联动效果：

```typescript
function setupFieldLinkage(field: EntityField, form: any): void {
  if (!field.control?.linkage || field.control.linkage.length === 0) {
    return; // 无联动配置
  }

  // 获取当前字段的值变化
  const fieldName = field.name;
  const watchCallback = (value: any) => {
    // 处理每个联动规则
    field.control.linkage.forEach(rule => {
      const { target, effect, condition, mapping } = rule;

      // 检查触发条件
      if (condition) {
        try {
          const evalFn = new Function('value', `return ${condition};`);
          if (!evalFn(value)) {
            return; // 条件不满足，不触发联动
          }
        } catch (error) {
          console.error('Error evaluating linkage condition:', error);
          return;
        }
      }

      // 应用联动效果
      switch (effect) {
        case 'setValue':
          // 设置目标字段的值
          const targetValue = mapping ? mapping[value] || null : value;
          form.setValue(target, targetValue);
          break;

        case 'setOptions':
          // 更新目标字段的选项
          // 这需要一个全局的选项管理机制
          window.fieldOptionsManager?.updateOptions(target, mapping?.[value] || []);
          break;

        case 'setVisibility':
          // 更新目标字段的可见性
          // 这需要一个全局的字段可见性管理机制
          window.fieldVisibilityManager?.setVisibility(target, !!mapping?.[value]);
          break;
      }
    });
  };

  // 监听字段值变化
  form.watch(fieldName, watchCallback);
}
```

### 3.5 验证逻辑

#### 3.5.1 验证规则解析

```typescript
function parseValidationRules(field: EntityField): any {
  if (!field.validation || field.validation.length === 0) {
    return {}; // 无验证规则
  }

  // 使用Zod或其他验证库构建验证规则
  let schema = z.any(); // 初始schema

  field.validation.forEach(rule => {
    // 根据规则类型构建验证器
    const validator = createValidator(rule);

    // 如果有条件，创建条件验证器
    if (rule.condition) {
      schema = schema.superRefine((data, ctx) => {
        // 获取表单上下文
        const formValues = ctx.path[0] ? ctx.path[0] : {};

        // 评估条件
        const conditionMet = ExpressionEngine.evaluate(rule.condition!, formValues);

        // 只有当条件满足时才应用验证
        if (conditionMet) {
          const result = validator.safeParse(data);
          if (!result.success) {
            // 添加验证错误
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: rule.message || '验证失败',
            });
          }
        }
      });
    } else {
      // 无条件，直接应用验证器
      schema = schema.pipe(validator);
    }
  });

  return schema;
}
```

#### 3.5.2 创建验证器

```typescript
function createValidator(rule: ValidationRule): z.ZodType<any> {
  switch (rule.type) {
    case 'required':
      return z.any().refine(val => val !== undefined && val !== null && val !== '', {
        message: rule.message || '此字段为必填项'
      });

    case 'min':
      return z.number().min(rule.params.min, {
        message: rule.message || `最小值为 ${rule.params.min}`
      });

    case 'max':
      return z.number().max(rule.params.max, {
        message: rule.message || `最大值为 ${rule.params.max}`
      });

    case 'pattern':
      return z.string().regex(new RegExp(rule.params.pattern), {
        message: rule.message || '格式不正确'
      });

    case 'email':
      return z.string().email({
        message: rule.message || '请输入有效的电子邮件地址'
      });

    case 'dependency':
      return z.any().superRefine((val, ctx) => {
        // 获取表单上下文
        const formValues = ctx.path[0] ? ctx.path[0] : {};

        // 获取依赖字段的值
        const dependencyValue = formValues[rule.params.field];

        // 执行依赖验证
        const isValid = ExpressionEngine.evaluate(rule.params.expression, {
          value: val,
          dependencyValue
        });

        if (!isValid) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message || '验证失败',
          });
        }
      });

    // 其他验证类型...

    default:
      return z.any(); // 默认不做验证
  }
}
```

#### 3.5.3 动态验证规则

```typescript
function useFieldValidation(field: EntityField, form: any) {
  // 获取所有依赖字段
  const dependsOnFields = new Set<string>();
  field.validation?.forEach(rule => {
    if (rule.dependsOn) {
      rule.dependsOn.forEach(fieldName => {
        dependsOnFields.add(fieldName);
      });
    }
  });

  // 监听依赖字段变化，重新触发验证
  useEffect(() => {
    if (dependsOnFields.size > 0) {
      const subscription = form.watch(Array.from(dependsOnFields), () => {
        // 重新触发当前字段的验证
        form.trigger(field.name);
      });

      return () => subscription.unsubscribe();
    }
  }, [form, field.name]);

  // 解析验证规则
  const validationRules = useMemo(() => {
    return parseValidationRules(field);
  }, [field.validation]);

  return validationRules;
}
```

#### 3.5.4 验证规则示例

**条件必填字段**：

```json
{
  "name": "phoneNumber",
  "display_name": "手机号码",
  "type": "VARCHAR",
  "default_value": null,
  "flags": [
    "NULLABLE"
  ],
  "_id": "123460",
  "validation": [
    {
      "type": "required",
      "message": "请输入手机号码",
      "condition": "contactMethod === 'phone'",
      "dependsOn": [
        "contactMethod"
      ]
    },
    {
      "type": "pattern",
      "params": {
        "pattern": "^1[3-9]\\d{9}$"
      },
      "message": "请输入有效的手机号码"
    }
  ]
}
```

**依赖字段验证**：

```json
{
  "name": "confirmPassword",
  "display_name": "确认密码",
  "type": "VARCHAR",
  "default_value": null,
  "flags": [
    "NULLABLE"
  ],
  "_id": "123461",
  "validation": [
    {
      "type": "required",
      "message": "请确认密码"
    },
    {
      "type": "dependency",
      "params": {
        "field": "password",
        "expression": "value === dependencyValue"
      },
      "message": "两次输入的密码不一致",
      "dependsOn": [
        "password"
      ]
    }
  ]
}
```

**范围验证**：

```json
{
  "name": "endDate",
  "display_name": "结束日期",
  "type": "DATE",
  "default_value": null,
  "flags": [
    "NULLABLE"
  ],
  "_id": "123462",
  "validation": [
    {
      "type": "required",
      "message": "请选择结束日期"
    },
    {
      "type": "dependency",
      "params": {
        "field": "startDate",
        "expression": "new Date(value) >= new Date(dependencyValue)"
      },
      "message": "结束日期必须晚于或等于开始日期",
      "dependsOn": [
        "startDate"
      ]
    }
  ]
}
```

### 3.6 布局控制逻辑

处理字段的布局配置：

```typescript
function getFieldLayoutProps(field: EntityField): Record<string, any> {
  if (!field.control?.layout) {
    return {}; // 默认布局
  }

  const { span, order, group } = field.control.layout;
  const layoutProps: Record<string, any> = {};

  if (span !== undefined) {
    layoutProps.colSpan = span;
  }

  if (order !== undefined) {
    layoutProps.order = order;
  }

  if (group) {
    layoutProps.group = group;
  }

  return layoutProps;
}
```

## 4. 实现架构设计

### 4.1 组件层次结构

```
EntityForm
├── FieldsRenderer
│   ├── FieldGroup (可选，用于分组)
│   │   └── EntityField (多个)
│   └── EntityField (多个)
│       ├── FieldLabel
│       ├── FieldControl (动态渲染不同控件)
│       └── FieldMessage
```

### 4.2 控件注册机制

创建一个控件注册表，支持自定义控件的注册：

```typescript
// 控件注册表
const controlRegistry = new Map<string, React.ComponentType<any>>();

// 注册默认控件
controlRegistry.set('text', TextInput);
controlRegistry.set('textarea', TextareaInput);
controlRegistry.set('number', NumberInput);
controlRegistry.set('checkbox', CheckboxInput);
controlRegistry.set('select', SelectInput);
controlRegistry.set('multiSelect', MultiSelectInput);
controlRegistry.set('date', DateInput);
controlRegistry.set('datetime', DateTimeInput);
// ...其他默认控件

// 注册自定义控件
export function registerControl(type: string, component: React.ComponentType<any>): void {
  controlRegistry.set(type, component);
}

// 获取控件组件
export function getControlComponent(type: string): React.ComponentType<any> {
  return controlRegistry.get(type) || controlRegistry.get('text'); // 默认返回文本输入
}
```

### 4.3 格式化器注册机制

创建一个格式化器注册表，支持自定义格式化函数：

```typescript
// 格式化器注册表
const formatterRegistry = new Map<string, (value: any, ...args: any[]) => any>();

// 注册默认格式化器
formatterRegistry.set('date', (value, format = 'yyyy-MM-dd') => format(new Date(value), format));
formatterRegistry.set('number', (value, options = {}) => new Intl.NumberFormat(undefined, options).format(value));
formatterRegistry.set('currency', (value, currency = 'CNY') => new Intl.NumberFormat(undefined, {
  style: 'currency',
  currency
}).format(value));
// ...其他默认格式化器

// 注册自定义格式化器
export function registerFormatter(name: string, formatter: (value: any, ...args: any[]) => any): void {
  formatterRegistry.set(name, formatter);
}

// 获取格式化器
export function getFormatter(name: string): (value: any, ...args: any[]) => any {
  return formatterRegistry.get(name) || ((value) => value); // 默认返回原值
}
```

### 4.4 表达式引擎

实现一个简单的表达式引擎，用于评估条件表达式：

```typescript
export class ExpressionEngine {
  // 评估表达式
  static evaluate(expression: string, context: Record<string, any>): any {
    try {
      // 创建参数列表和值列表
      const params = Object.keys(context);
      const values = Object.values(context);

      // 创建函数并执行
      const fn = new Function(...params, `return ${expression};`);
      return fn(...values);
    } catch (error) {
      console.error('Error evaluating expression:', error);
      return null;
    }
  }

  // 解析表达式中的依赖字段
  static extractDependencies(expression: string): string[] {
    // 简单实现：提取所有可能的字段名
    // 实际实现可能需要更复杂的解析逻辑
    const fieldPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
    const matches = expression.match(fieldPattern) || [];

    // 过滤掉JavaScript关键字和常见函数名
    const keywords = ['if', 'else', 'return', 'true', 'false', 'null', 'undefined', 'Math', 'Date', 'String', 'Number', 'Boolean'];
    return matches.filter(match => !keywords.includes(match));
  }
}
```

### 4.5 字段管理器

创建一个字段管理器，用于处理字段的可见性、选项和联动：

```typescript
export class FieldManager {
  private form: any;
  private fields: Map<string, EntityField> = new Map();
  private visibilityState: Map<string, boolean> = new Map();
  private optionsState: Map<string, any[]> = new Map();

  constructor(form: any, fields: EntityField[]) {
    this.form = form;

    // 初始化字段
    fields.forEach(field => {
      this.fields.set(field.name, field);
      this.visibilityState.set(field.name, true);

      // 设置初始选项
      if (field.options?.values) {
        this.optionsState.set(field.name, field.options.values);
      }

      // 设置字段联动
      this.setupFieldLinkage(field);
    });

    // 初始化字段可见性
    this.updateAllFieldsVisibility();
  }

  // 更新字段可见性
  updateFieldVisibility(fieldName: string): void {
    const field = this.fields.get(fieldName);
    if (!field || !field.control?.visibility) {
      return;
    }

    const formValues = this.form.getValues();
    const isVisible = ExpressionEngine.evaluate(field.control.visibility.condition, formValues);
    this.visibilityState.set(fieldName, !!isVisible);
  }

  // 更新所有字段可见性
  updateAllFieldsVisibility(): void {
    this.fields.forEach((field, fieldName) => {
      this.updateFieldVisibility(fieldName);
    });
  }

  // 获取字段可见性
  isFieldVisible(fieldName: string): boolean {
    return this.visibilityState.get(fieldName) || false;
  }

  // 更新字段选项
  updateFieldOptions(fieldName: string, options: any[]): void {
    this.optionsState.set(fieldName, options);
  }

  // 获取字段选项
  getFieldOptions(fieldName: string): any[] {
    return this.optionsState.get(fieldName) || [];
  }

  // 设置字段联动
  private setupFieldLinkage(field: EntityField): void {
    if (!field.control?.linkage || field.control.linkage.length === 0) {
      return;
    }

    // 获取所有依赖字段
    const dependsOn = new Set<string>();
    field.control.linkage.forEach(rule => {
      if (rule.condition) {
        ExpressionEngine.extractDependencies(rule.condition).forEach(dep => {
          dependsOn.add(dep);
        });
      }
    });

    // 监听依赖字段的变化
    if (dependsOn.size > 0) {
      this.form.watch(Array.from(dependsOn), (values: Record<string, any>) => {
        this.applyLinkageRules(field, values);
      });
    }
  }

  // 应用联动规则
  private applyLinkageRules(field: EntityField, values: Record<string, any>): void {
    if (!field.control?.linkage) {
      return;
    }

    field.control.linkage.forEach(rule => {
      // 检查条件
      if (rule.condition) {
        const conditionMet = ExpressionEngine.evaluate(rule.condition, values);
        if (!conditionMet) {
          return;
        }
      }

      // 应用效果
      const { target, effect, mapping } = rule;
      const sourceValue = values[field.name];

      switch (effect) {
        case 'setValue':
          const targetValue = mapping ? mapping[sourceValue] || null : sourceValue;
          this.form.setValue(target, targetValue);
          break;

        case 'setOptions':
          if (mapping && mapping[sourceValue]) {
            this.updateFieldOptions(target, mapping[sourceValue]);
          }
          break;

        case 'setVisibility':
          // 这里需要更复杂的逻辑来处理可见性
          // 因为可见性可能受多个字段影响
          this.updateAllFieldsVisibility();
          break;
      }
    });
  }
}
```

## 5. 前后端职责分离

### 5.1 服务端职责

1. **数据结构定义**：
    - 定义实体和字段的基本结构
    - 指定字段的数据类型、默认值和标志
    - 提供基本的验证规则（如必填、长度限制等）

2. **数据验证**：
    - 验证数据的类型和格式
    - 执行业务规则验证
    - 确保数据完整性和一致性

3. **选项数据**：
    - 提供枚举值和选项数据
    - 支持动态选项查询
    - 维护选项之间的关系

### 5.2 前端职责

1. **UI渲染**：
    - 根据字段类型和控制配置选择合适的控件
    - 处理字段的布局和分组
    - 实现条件显示和字段联动

2. **用户交互**：
    - 处理表单输入和验证
    - 提供即时反馈和错误提示
    - 实现高级交互功能（如自动完成、拖放等）

3. **数据格式化**：
    - 格式化显示值（如日期、货币等）
    - 处理编辑值的解析和转换
    - 支持本地化和国际化

### 5.3 通信接口

1. **元数据API**：
    - 获取实体和字段的元数据
    - 支持按需加载和缓存
    - 处理元数据的版本和更新

2. **选项数据API**：
    - 获取动态选项数据
    - 支持条件查询和分页
    - 处理选项的缓存和刷新

3. **数据验证API**：
    - 提供服务端验证
    - 返回详细的错误信息
    - 支持批量验证

## 6. 实现示例

### 6.1 字段元数据示例

```json
{
  "name": "price",
  "display_name": "价格",
  "type": "FLOAT",
  "default_value": "0",
  "flags": [],
  "_id": "123456",
  "validation": {
    "required": true,
    "min": 0,
    "max": 1000000,
    "message": "价格必须大于0且小于1000000"
  },
  "control": {
    "type": "currency",
    "props": {
      "step": 0.01,
      "placeholder": "请输入价格"
    },
    "formatter": {
      "display": "currency:CNY",
      "edit": "number:2"
    },
    "layout": {
      "span": 12
    }
  }
}
```

### 6.2 条件显示示例

```json
{
  "name": "discountPrice",
  "display_name": "折扣价",
  "type": "FLOAT",
  "default_value": null,
  "flags": [
    "NULLABLE"
  ],
  "_id": "123457",
  "control": {
    "type": "currency",
    "visibility": {
      "condition": "hasDiscount === true",
      "dependsOn": [
        "hasDiscount"
      ]
    },
    "formatter": {
      "display": "currency:CNY",
      "edit": "number:2"
    }
  }
}
```

### 6.3 字段联动示例

```json
{
  "name": "country",
  "display_name": "国家",
  "type": "VARCHAR",
  "default_value": null,
  "flags": [],
  "_id": "123458",
  "options": {
    "values": [
      {
        "label": "中国",
        "value": "CN"
      },
      {
        "label": "美国",
        "value": "US"
      },
      {
        "label": "日本",
        "value": "JP"
      }
    ]
  },
  "control": {
    "type": "select",
    "linkage": [
      {
        "target": "province",
        "effect": "setOptions",
        "mapping": {
          "CN": [
            {
              "label": "北京",
              "value": "BJ"
            },
            {
              "label": "上海",
              "value": "SH"
            },
            {
              "label": "广东",
              "value": "GD"
            }
          ],
          "US": [
            {
              "label": "加利福尼亚",
              "value": "CA"
            },
            {
              "label": "纽约",
              "value": "NY"
            },
            {
              "label": "德克萨斯",
              "value": "TX"
            }
          ],
          "JP": [
            {
              "label": "东京",
              "value": "TK"
            },
            {
              "label": "大阪",
              "value": "OS"
            },
            {
              "label": "京都",
              "value": "KT"
            }
          ]
        }
      },
      {
        "target": "province",
        "effect": "setValue",
        "mapping": {
          "CN": null,
          "US": null,
          "JP": null
        }
      }
    ]
  }
}
```

## 7. 性能优化考虑

### 7.1 表达式缓存

缓存已解析的表达式函数，避免重复解析：

```typescript
class ExpressionCache {
  private cache = new Map<string, Function>();

  getExpression(expression: string, params: string[]): Function {
    const key = `${expression}|${params.join(',')}`;

    if (!this.cache.has(key)) {
      try {
        const fn = new Function(...params, `return ${expression};`);
        this.cache.set(key, fn);
      } catch (error) {
        console.error('Error parsing expression:', error);
        return () => null;
      }
    }

    return this.cache.get(key)!;
  }

  clear(): void {
    this.cache.clear();
  }
}
```

### 7.2 选项数据缓存

缓存动态选项数据，减少不必要的API请求：

```typescript
class OptionsCache {
  private cache = new Map<string, { data: any[], timestamp: number }>();
  private maxAge = 5 * 60 * 1000; // 5分钟缓存

  async getOptions(source: any, params: any): Promise<any[]> {
    const key = JSON.stringify({ source, params });
    const cached = this.cache.get(key);

    // 检查缓存是否有效
    if (cached && Date.now() - cached.timestamp < this.maxAge) {
      return cached.data;
    }

    // 获取新数据
    try {
      const data = await this.fetchOptions(source, params);
      this.cache.set(key, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      console.error('Error fetching options:', error);
      return cached?.data || [];
    }
  }

  private async fetchOptions(source: any, params: any): Promise<any[]> {
    // 根据source类型获取选项数据
    // ...
    return [];
  }

  invalidate(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
}
```

### 7.3 渲染优化

使用React.memo和useMemo减少不必要的重渲染：

```typescript
const EntityField = React.memo(function EntityField({ field, ...props }: EntityFieldProps) {
  const { control } = field;

  // 使用useMemo缓存控件类型
  const controlType = useMemo(() => {
    return determineControlType(field);
  }, [field.type, field.control?.type, !!field.options]);

  // 使用useMemo缓存布局属性
  const layoutProps = useMemo(() => {
    return getFieldLayoutProps(field);
  }, [field.control?.layout]);

  // 使用useMemo缓存控件属性
  const controlProps = useMemo(() => {
    return {
      ...props,
      ...(field.control?.props || {})
    };
  }, [props, field.control?.props]);

  // 渲染字段
  return (
    <div { ...layoutProps } >
    {/* 字段内容 */ }
    < /div>
  );
});
```

## 8. 测试策略

### 8.1 单元测试

1. **表达式引擎测试**：
    - 测试条件表达式的解析和评估
    - 测试依赖字段的提取
    - 测试错误处理和边界情况

2. **格式化器测试**：
    - 测试各种格式化规则
    - 测试不同数据类型的格式化
    - 测试自定义格式化函数

3. **字段管理器测试**：
    - 测试字段可见性计算
    - 测试字段联动规则
    - 测试选项数据更新

### 8.2 集成测试

1. **表单集成测试**：
    - 测试字段与表单的集成
    - 测试表单验证和提交
    - 测试字段间的联动效果

2. **API集成测试**：
    - 测试元数据加载和解析
    - 测试动态选项数据获取
    - 测试数据提交和验证

### 8.3 端到端测试

1. **用户交互测试**：
    - 测试表单填写和提交
    - 测试条件显示和字段联动
    - 测试错误提示和反馈

2. **性能测试**：
    - 测试大型表单的渲染性能
    - 测试复杂联动规则的执行效率
    - 测试缓存机制的有效性
