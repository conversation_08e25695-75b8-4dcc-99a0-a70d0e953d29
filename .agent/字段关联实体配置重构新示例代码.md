# 字段关联实体配置重构新示例代码

## 1. 类型定义

### 1.1 关联实体类型定义 (src/features/metadata/types/relation.ts)

```typescript
/**
 * 关联条件类型
 */
export type RelationConditionType = 'always' | 'expression' | 'role' | 'mode';

/**
 * 关联条件
 */
export interface RelationCondition {
  type: RelationConditionType;           // 条件类型
  expression?: string;                   // 条件表达式
  roles?: string[];                      // 角色列表
  modes?: ('create' | 'edit' | 'view')[]; // 模式列表
}

/**
 * 关联控制配置
 */
export interface RelationControl {
  valueField?: string;                   // 值字段，默认为_id
  labelField?: string;                   // 标签字段，默认为name
  displayFields?: string[];              // 显示字段列表
  multiple?: boolean;                    // 是否多选
  cascade?: {                            // 级联配置
    parent?: string;                     // 父级字段
    children?: string;                   // 子级字段
  };
  filter?: {                             // 过滤条件
    condition?: string;                  // 条件表达式
    dependsOn?: string[];                // 依赖字段
  };
  searchable?: boolean;                  // 是否可搜索
  createable?: boolean;                  // 是否可创建
  previewable?: boolean;                 // 是否可预览
}

/**
 * 实体关联信息
 */
export interface EntityRelation {
  field: string;                         // 关联字段名
  entity: string;                        // 关联实体名
  module?: string;                       // 关联实体所属模块
  type: 'one' | 'many';                  // 关联类型：一对一或一对多
  foreignField?: string;                 // 外键字段，默认为_id
  alias?: string;                        // 关联数据别名，默认为 ${field}_relation
  condition?: RelationCondition;         // 关联条件
  control?: RelationControl;             // 关联控制配置
}
```

### 1.2 扩展实体元数据接口 (src/features/metadata/types/index.ts)

```typescript
import { EntityRelation } from './relation';

/**
 * 实体元数据
 */
export interface EntityMetadata {
  name: string;
  display_name: string;
  module: string;
  description: string | null;
  fields: EntityField[];
  _id: string;
  
  // 新增字段
  relations?: EntityRelation[]; // 实体关联信息
}
```

## 2. 关联信息处理

### 2.1 关联条件判断 (src/lib/relation/condition.ts)

```typescript
import { RelationCondition } from '@/features/metadata/types/relation';

/**
 * 判断关联条件是否满足
 * @param condition 关联条件
 * @param context 上下文
 * @returns 是否满足
 */
export const isRelationConditionMet = (
  condition: RelationCondition | undefined,
  context: Record<string, any> = {}
): boolean => {
  // 如果没有条件，默认满足
  if (!condition) return true;
  
  const { type } = condition;
  
  switch (type) {
    case 'always':
      return true;
      
    case 'expression':
      if (!condition.expression) return true;
      try {
        // 使用 Function 构造函数创建一个函数，传入上下文变量
        const keys = Object.keys(context);
        const values = Object.values(context);
        const fn = new Function(...keys, `return ${condition.expression}`);
        return fn(...values);
      } catch (error) {
        console.error('Error evaluating relation condition:', error);
        return false;
      }
      
    case 'role':
      if (!condition.roles || condition.roles.length === 0) return true;
      const userRole = context.userRole || '';
      return condition.roles.includes(userRole);
      
    case 'mode':
      if (!condition.modes || condition.modes.length === 0) return true;
      const formMode = context.mode || '';
      return condition.modes.includes(formMode);
      
    default:
      return true;
  }
};

/**
 * 获取满足条件的关联配置
 * @param relations 关联配置列表
 * @param field 字段名
 * @param context 上下文
 * @returns 满足条件的关联配置
 */
export const getMatchingRelation = (
  relations: EntityRelation[] | undefined,
  field: string,
  context: Record<string, any> = {}
): EntityRelation | undefined => {
  if (!relations || relations.length === 0) return undefined;
  
  // 过滤出字段相关的关联配置
  const fieldRelations = relations.filter(relation => relation.field === field);
  
  if (fieldRelations.length === 0) return undefined;
  
  // 找到第一个满足条件的关联配置
  return fieldRelations.find(relation => isRelationConditionMet(relation.condition, context));
};
```

### 2.2 关联数据处理器 (src/lib/relation/processor.ts)

```typescript
import { EntityMetadata } from '@/features/metadata/types';
import { EntityRelation } from '@/features/metadata/types/relation';
import { getMatchingRelation } from './condition';

/**
 * 处理实体数据，合并关联数据
 * @param data 原始数据
 * @param metadata 实体元数据
 * @param context 上下文
 * @returns 处理后的数据
 */
export const processEntityData = (
  data: any,
  metadata: EntityMetadata,
  context: Record<string, any> = {}
): any => {
  if (!data) return data;
  
  // 如果没有关联信息，直接返回原始数据
  if (!metadata.relations || metadata.relations.length === 0) return data;
  
  // 处理关联数据
  const result = { ...data };
  
  // 获取所有字段
  const fields = metadata.fields.map(field => field.name);
  
  // 处理每个字段的关联数据
  fields.forEach(field => {
    // 获取满足条件的关联配置
    const relation = getMatchingRelation(metadata.relations, field, context);
    
    if (relation) {
      const { alias } = relation;
      const relationAlias = alias || `${field}_relation`;
      const relationData = data[relationAlias];
      
      if (relationData) {
        // 将关联数据合并到原始数据中
        result[`${field}_data`] = relationData;
      }
    }
  });
  
  return result;
}

/**
 * 处理实体列表数据，合并关联数据
 * @param data 原始数据
 * @param metadata 实体元数据
 * @param context 上下文
 * @returns 处理后的数据
 */
export const processEntityListData = (
  data: any[],
  metadata: EntityMetadata,
  context: Record<string, any> = {}
): any[] => {
  if (!data || !Array.isArray(data)) return data;
  
  return data.map(item => processEntityData(item, metadata, context));
}
```

## 3. 查询生成器扩展

### 3.1 扩展查询生成器 (src/lib/graphql/query-generator.ts)

```typescript
import { EntityField, EntityMetadata } from '@/features/metadata/types';
import { EntityRelation } from '@/features/metadata/types/relation';
import { getMatchingRelation } from '@/lib/relation/condition';

export class QueryGenerator {
  /**
   * 生成实体详情查询
   * @param entityMetadata 实体元数据
   * @param context 上下文
   * @param includeRelations 是否包含关联数据
   * @returns GraphQL查询字符串
   */
  static generateDetailQuery(
    entityMetadata: EntityMetadata,
    context: Record<string, any> = {},
    includeRelations: boolean = true
  ): string {
    const { name, fields } = entityMetadata;
    const fieldNames = this.generateFieldsSelection(fields);
    
    // 如果不包含关联数据，直接返回基本查询
    if (!includeRelations) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail($_id: $_id) {
            ${fieldNames}
          }
        }
      `;
    }
    
    // 如果没有关联信息，直接返回基本查询
    if (!entityMetadata.relations || entityMetadata.relations.length === 0) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail($_id: $_id) {
            ${fieldNames}
          }
        }
      `;
    }
    
    // 获取所有字段
    const fieldNames2 = fields.map(field => field.name);
    
    // 关联字段查询
    const relationQueries: string[] = [];
    
    // 处理每个字段的关联数据
    fieldNames2.forEach(field => {
      // 获取满足条件的关联配置
      const relation = getMatchingRelation(entityMetadata.relations, field, context);
      
      if (relation) {
        const { alias, control } = relation;
        const relationAlias = alias || `${field}_relation`;
        const displayFields = control?.displayFields?.join('\n        ') || '_id';
        
        relationQueries.push(`
      ${relationAlias} {
        ${displayFields}
      }`);
      }
    });
    
    return `
      query Get${name}Detail($_id: ID!) {
        ${name}_detail($_id: $_id) {
          ${fieldNames}
          ${relationQueries.join('')}
        }
      }
    `;
  }
  
  // 其他方法...
}
```
