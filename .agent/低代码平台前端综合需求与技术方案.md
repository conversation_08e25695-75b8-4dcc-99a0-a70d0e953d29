# 低代码平台前端综合需求与技术方案

## 1. 项目概述

本项目旨在基于vibe-admin开发一个低代码平台前端，该平台采用元数据驱动的开发方式，使用GraphQL规范与后端进行通信，并实现以EntityMetadata元数据为核心的统一范式CRUD组件。

### 1.1 项目目标

- 实现基于元数据驱动的低代码平台前端
- 实现统一范式的CRUD组件，支持列表、详情、新增、编辑、删除等功能
- 支持表单字段的灵活配置，包括验证规则、联动规则、条件显示等
- 支持实体列表的筛选功能，包括关键字搜索和高级筛选
- 提供良好的用户体验和性能优化

### 1.2 技术栈

- **UI框架**：React 19 + ShadcnUI (基于TailwindCSS和RadixUI)
- **构建工具**：Vite
- **路由**：TanStack Router
- **状态管理**：Zustand
- **表单处理**：React Hook Form + Zod
- **数据获取**：TanStack Query (React Query)
- **HTTP客户端**：Axios
- **表格组件**：TanStack Table
- **类型检查**：TypeScript
- **GraphQL客户端**：graphql-request

## 2. 当前开发进度

根据CHANGELOG和todolist，项目当前已完成以下功能：

### 2.1 已完成功能

1. **基础架构搭建**
    - 元数据类型定义
    - GraphQL客户端配置
    - 实体API调用
    - 实体组件开发
    - 路由配置

2. **实体表单优化**
    - 表单双列布局
    - 表单只读模式
    - 特殊字段自动占据整行

3. **实体列表优化**
    - "查看"操作
    - 操作列固定
    - 表格响应式布局

4. **实体对话框优化**
    - 统一工具栏
    - 状态切换按钮
    - 表单提交按钮

5. **EntityField组件控制逻辑**
    - 条件显示功能
    - 字段联动功能
    - 多规则验证
    - 值格式化

6. **实体列表筛选功能**
    - 关键信息字段模糊查询
    - 字段筛选
    - 筛选字段配置

7. **实体表单验证优化**
    - 自定义验证规则
    - 条件验证
    - 动态错误消息

8. **表单字段联动规则**
    - 联动规则增强
    - 联动效果类型扩展
    - 表达式引擎增强
    - 联动调试工具

9. **表单字段联动规则重构**
    - 从"推"模式改为"拉"模式
    - 数据源管理器
    - 联动引擎重构
    - 联动上下文扩展

### 2.2 待实现功能

1. **基础功能**
    - 环境变量配置（GraphQL端点等）
    - GraphQL请求拦截器（添加认证信息等）
    - GraphQL响应拦截器（错误处理等）
    - 元数据缓存机制

2. **高级功能**
    - 表单字段控件的联动规则可视化配置
    - 实体表单的自定义字段类型
    - 实体表单的文件上传功能
    - 实体表单的富文本编辑功能
    - 实体表单的关联选择功能
    - 实体表单的多语言支持
    - 实体表单的权限控制
    - 实体表单的工作流支持
    - 实体表单的版本控制
    - 实体表单的审计日志

3. **优化与测试**
    - 组件性能优化
    - 页面加载速度优化
    - 移动端适配优化
    - 单元测试
    - 集成测试
    - 端到端测试
    - 性能测试
    - 安全测试
    - 可访问性测试

## 3. 核心功能详细分析

### 3.1 元数据驱动

#### 3.1.1 元数据类型定义

```typescript
export interface EntityField {
  name: string;
  display_name: string;
  type: string;
  default_value: string | null;
  flags: string[];
  _id: string;

  // 扩展字段
  control?: FieldControl;
  options?: FieldOption[];
  validation?: ValidationRule[];
  condition?: FieldCondition;
}

export interface EntityMetadata {
  name: string;
  display_name: string;
  module: string;
  description: string | null;
  fields: EntityField[];
  _id: string;
}
```

#### 3.1.2 元数据解析器

元数据解析器用于解析元数据，提取有用的信息，如：

- 获取字段类型对应的表单控件类型
- 判断字段是否为主键
- 判断字段是否为内部字段
- 判断字段是否可为空
- 获取实体中的可编辑字段
- 获取实体中的可显示字段
- 获取实体中的关键信息字段
- 获取实体中的可查询字段

### 3.2 统一范式CRUD组件

#### 3.2.1 实体列表组件 (EntityList)

- 展示实体数据列表
- 支持分页、排序、筛选
- 支持查看、编辑、删除操作
- 操作列固定在表格右侧
- 支持响应式布局

#### 3.2.2 实体详情组件 (EntityDetail)

- 展示实体详细信息
- 支持表格视图和表单视图
- 表单视图使用只读模式的EntityForm组件

#### 3.2.3 实体表单组件 (EntityForm)

- 用于创建和编辑实体
- 支持双列布局
- 支持只读模式
- 特殊字段（文本区域、描述等）自动占据整行
- 支持自定义验证规则
- 支持字段联动

#### 3.2.4 实体对话框组件 (EntityDialog)

- 用于创建、编辑和查看实体
- 统一的工具栏
- 左侧显示状态切换按钮（编辑/取消）
- 右侧显示表单提交按钮（创建/保存）和关闭按钮

#### 3.2.5 实体字段组件 (EntityField)

- 根据字段类型渲染不同的表单控件
- 支持条件显示
- 支持字段联动
- 支持多规则验证
- 支持值格式化

### 3.3 实体列表筛选功能

#### 3.3.1 筛选功能需求

- EntityField.flags标记关键信息字段，支持模糊查询
- EntityField.condition定义查询行为
- 统一查询区域，支持关键字搜索
- 支持自定义高级查询条件
- 所有条件合并到Condition对象

#### 3.3.2 筛选组件设计

- **EntityListFilter**：渲染查询表单，处理基本查询和高级查询
- **KeywordSearch**：处理关键信息字段的模糊查询
- **FieldFilter**：根据字段类型渲染不同的筛选控件
- **筛选字段配置**：用户可以自定义显示的筛选字段，每行3列展示

#### 3.3.3 智能推断功能

- 根据字段类型自动推断支持的操作符
- 定义了options的字段自动使用选择控件
- 列表显示时，定义了options的字段按options定义显示

### 3.4 实体表单自定义验证规则

#### 3.4.1 验证规则数据结构

```typescript
export interface ValidationRule {
  type: string               // 验证类型
  params?: any               // 验证参数
  message?: string           // 错误消息（支持模板语法）
  condition?: string         // 启用条件表达式
  dependsOn?: string[]       // 条件依赖的字段
  async?: boolean            // 是否为异步验证
}
```

#### 3.4.2 支持的验证类型

- `required`：必填验证
- `min`：最小值/最小长度验证
- `max`：最大值/最大长度验证
- `pattern`：正则表达式验证
- `email`：电子邮件格式验证
- `url`：URL格式验证
- `dependency`：依赖字段验证
- `custom`：自定义验证
- `oneOf`：复合验证（至少一个子规则验证通过）
- `allOf`：复合验证（所有子规则都必须验证通过）
- `async`：异步验证

#### 3.4.3 动态错误消息

支持在错误消息中引用字段值和其他字段值：

```typescript
"message"
:
"价格必须大于 ${minPrice} 且小于 ${maxPrice}"
```

### 3.5 表单字段控件联动规则

#### 3.5.1 联动规则数据结构（旧版-推模式）

```typescript
interface LinkageRule {
  id?: string               // 规则唯一标识
  priority?: number         // 规则优先级，数字越小优先级越高
  target: string            // 目标字段
  effect: LinkageEffectType // 联动效果类型
  condition?: string        // 触发条件
  dependsOn?: string[]      // 显式依赖的字段
  mapping?: Record<string, any> // 值映射
  value?: any               // 静态值
  expression?: string       // 动态表达式
  props?: Record<string, any>   // 控件属性
}
```

#### 3.5.2 联动规则数据结构（新版-拉模式）

```typescript
// 联动来源类型
type LinkageSourceType =
  | 'field'        // 表单字段
  | 'formState'    // 表单状态（如新建/编辑模式）
  | 'userRole'     // 用户角色
  | 'config'       // 全局配置
  | 'api'          // 外部API
  | 'context'      // 上下文环境变量
  | 'expression'   // 表达式计算结果

// 联动来源
interface LinkageSource {
  type: LinkageSourceType    // 来源类型
  name: string               // 来源名称（字段名或其他标识）
  path?: string              // 数据路径（用于访问嵌套属性）
  params?: Record<string, any> // 附加参数
}

// 联动规则（拉模式）
interface LinkageRule {
  id?: string               // 规则唯一标识
  priority?: number         // 规则优先级，数字越小优先级越高

  // 联动来源（拉模式的核心变化）
  sources: LinkageSource[]  // 影响本字段的来源列表

  // 联动效果
  effect: LinkageEffectType // 联动效果类型
  condition?: string        // 触发条件

  // 效果参数
  value?: any               // 静态值
  expression?: string       // 动态表达式
  mapping?: Record<string, any> // 值映射
  props?: Record<string, any>   // 控件属性
}
```

#### 3.5.3 联动效果类型

```typescript
type LinkageEffectType =
  | 'setValue'      // 设置值
  | 'setOptions'    // 设置选项
  | 'setVisibility' // 设置可见性
  | 'setRequired'   // 设置是否必填
  | 'setDisabled'   // 设置是否禁用
  | 'setValidation' // 设置验证规则
  | 'setProps'      // 设置控件属性
  | 'custom'        // 自定义效果
```

#### ******* 联动效果类型实现状态与副作用分析

当前系统中各种联动效果类型的实现状态与副作用分析：

| 效果类型          | 实现状态 | 表单中的行为                                                      | 副作用分析                                      |
|---------------|------|-------------------------------------------------------------|--------------------------------------------|
| setValue      | 完全实现 | 通过 form.setValue 设置字段的值，并触发验证                               | 会触发表单的 onChange 事件，可能引起连锁反应，需要注意循环依赖       |
| setOptions    | 完全实现 | 通过 OptionsProvider 上下文更新选项列表，在 EntityField 中正确应用            | 只影响选项显示，不会自动改变字段值，可能需要配合 setValue 使用       |
| setVisibility | 完全实现 | 通过 fieldStates 状态更新可见性，在 EntityField 中正确应用，不可见时返回 null      | 当字段不可见时，其值仍然存在于表单中，可能需要配合清除值               |
| setRequired   | 完全实现 | 通过 fieldStates 状态更新必填状态，在 EntityField 中正确应用，显示必填标记(*)       | 只影响 UI 显示，不会自动更新验证规则，需要配合 setValidation 使用 |
| setDisabled   | 完全实现 | 通过 fieldStates 状态更新禁用状态，在 EntityField 中正确应用到控件的 disabled 属性 | 禁用的字段仍然会提交其值，但用户无法修改                       |
| setValidation | 部分实现 | 在 LinkageProvider 中只有占位实现，没有实际功能                            | 无法动态更新字段的验证规则，需要完善实现                       |
| setProps      | 完全实现 | 通过 fieldStates 状态更新控件属性，在 EntityField 中正确应用到控件的 props       | 可以动态设置控件的各种属性，功能强大但需要注意属性的兼容性              |
| custom        | 基本实现 | 执行自定义函数，传入目标字段名和表单值                                         | 存在安全风险，因为可以执行任意代码，需要增强安全性和限制范围             |

#### 3.5.4 数据源管理

引入数据源管理器，统一管理各类数据源：

```typescript
class DataSourceManager {
  // 注册的数据源提供者
  private providers: Map<LinkageSourceType, DataSourceProvider> = new Map();

  // 注册数据源提供者
  registerProvider(type: LinkageSourceType, provider: DataSourceProvider): void {
    this.providers.set(type, provider);
  }

  // 获取数据源的值
  getValue(source: LinkageSource, context: Record<string, any>): any {
    const provider = this.providers.get(source.type);
    if (!provider) {
      console.warn(`No provider registered for source type: ${source.type}`);
      return undefined;
    }

    return provider.getValue(source, context);
  }

  // 监听数据源变化
  watchSource(source: LinkageSource, callback: (value: any) => void): () => void {
    const provider = this.providers.get(source.type);
    if (!provider || !provider.watch) {
      return () => {
      }; // 返回空函数
    }

    return provider.watch(source, callback);
  }
}
```

## 4. 系统架构设计

### 4.1 项目结构

```
src/
├── features/
│   ├── metadata/           # 元数据相关功能
│   │   ├── api/            # 元数据API调用
│   │   ├── hooks/          # 元数据相关钩子函数
│   │   ├── types/          # 元数据类型定义
│   │   └── utils/          # 元数据工具函数
│   │
│   ├── entity/             # 实体通用功能
│       ├── api/            # 实体API调用
│       ├── components/     # 通用CRUD组件
│       │   ├── EntityList.tsx       # 实体列表组件
│       │   ├── EntityDetail.tsx     # 实体详情组件
│       │   ├── EntityForm.tsx       # 实体表单组件
│       │   ├── EntityField.tsx      # 实体字段组件
│       │   └── EntityDialog.tsx     # 实体对话框组件
│       ├── hooks/          # 实体相关钩子函数
│       └── utils/          # 实体工具函数
│
├── lib/
│   ├── graphql/            # GraphQL相关功能
│   │   ├── client.ts       # GraphQL客户端配置
│   │   ├── queries.ts      # 查询定义
│   │   └── mutations.ts    # 变更定义
│   │
│   └── metadata/           # 元数据处理工具
│       ├── parser.ts       # 元数据解析器
│       └── validator.ts    # 元数据验证器
│
├── routes/                 # 路由定义
│   ├── _authenticated/     # 需要认证的路由
│   │   ├── entity/         # 实体相关路由
│   │   │   ├── $name/      # 实体列表路由
│   │   │   │   └── $id/    # 实体详情路由
│   │   │
│   │   └── metadata/       # 元数据管理路由
```

### 4.2 数据流设计

#### 4.2.1 元数据获取流程

1. 应用启动时，从服务端获取所有实体元数据
2. 将元数据存储在缓存中，供组件使用
3. 根据元数据动态生成路由和组件

#### 4.2.2 实体数据流程

1. 用户访问实体列表页面
2. 根据元数据生成表格列和筛选表单
3. 发送GraphQL查询获取实体数据
4. 展示实体数据列表
5. 用户可以查看、编辑、删除实体数据

#### 4.2.3 表单数据流程

1. 用户创建或编辑实体
2. 根据元数据生成表单字段
3. 应用验证规则和联动规则
4. 用户提交表单
5. 发送GraphQL变更操作
6. 更新实体数据列表

### 4.3 组件设计

#### 4.3.1 EntityField组件架构

1. **基础控件封装**：为每种控件类型创建专门的组件
2. **复合控件抽象**：将复杂控件抽象为独立组件
3. **字段工厂**：根据字段类型返回相应的组件
4. **表单上下文**：共享表单配置和状态
5. **字段依赖关系**：管理字段之间的依赖关系
6. **插件系统**：允许添加自定义字段类型
7. **主题定制**：支持通过主题配置自定义外观

#### 4.3.2 联动引擎设计

1. **数据源管理器**：统一管理各类数据源
2. **联动引擎**：注册字段联动规则、构建依赖图、执行联动规则
3. **联动上下文**：提供联动状态和方法
4. **表达式引擎**：评估条件表达式和提取依赖字段

#### 4.3.3 筛选组件设计

1. **EntityListFilter**：整合关键字搜索和字段筛选功能
2. **KeywordSearch**：处理关键信息字段的模糊查询
3. **FieldFilter**：根据字段类型渲染不同的筛选控件
4. **筛选字段配置**：用户可以自定义显示的筛选字段

## 5. 下一步开发计划

根据当前进度和待实现功能，下一步开发计划如下：

### 5.1 短期计划（优先级高）

1. **环境变量配置**
    - 实现GraphQL端点配置
    - 实现其他环境变量配置

2. **GraphQL请求/响应拦截器**
    - 实现认证信息添加
    - 实现错误处理

3. **元数据缓存机制**
    - 实现元数据缓存
    - 实现元数据更新机制

4. **表单字段联动规则完善**
    - 完善 `setValidation` 效果的实现，目前在 LinkageProvider 中只有占位实现
    - 优化 `custom` 效果的实现，增强其灵活性和安全性
    - 完善 `setVisibility` 效果，当字段不可见时自动清除其值
    - 完善 `setRequired` 效果，使其自动更新验证规则
    - 添加更多的联动效果类型测试用例
    - 完善联动规则的错误处理和调试功能

5. **表单字段控件的联动规则可视化配置**
    - 设计联动规则配置界面
    - 实现联动规则可视化编辑

### 5.2 中期计划（优先级中）

1. **实体表单的自定义字段类型**
    - 实现自定义字段类型注册机制
    - 实现常用自定义字段类型

2. **实体表单的文件上传功能**
    - 实现文件上传控件
    - 实现文件预览和下载

3. **实体表单的富文本编辑功能**
    - 集成富文本编辑器
    - 实现富文本内容的存储和展示

4. **实体表单的关联选择功能**
    - 实现关联实体选择控件
    - 实现关联数据的加载和展示

### 5.3 长期计划（优先级低）

1. **实体表单的多语言支持**
    - 实现多语言配置
    - 实现多语言切换

2. **实体表单的权限控制**
    - 实现基于角色的权限控制
    - 实现字段级别的权限控制

3. **实体表单的工作流支持**
    - 实现工作流状态管理
    - 实现工作流流转

4. **实体表单的版本控制**
    - 实现数据版本管理
    - 实现版本比较和回滚

5. **实体表单的审计日志**
    - 实现操作日志记录
    - 实现日志查询和展示

## 6. 技术挑战与解决方案

### 6.1 动态生成表单

**挑战**：根据元数据动态生成表单，支持不同类型的字段。

**解决方案**：

- 使用React Hook Form和Zod进行表单处理和验证
- 创建通用的表单字段组件，根据字段类型渲染不同的控件
- 使用元数据解析器和验证器生成表单配置

### 6.2 字段联动规则

**挑战**：实现复杂的字段联动规则，支持多种联动效果和数据源。

**解决方案**：

- 使用"拉"模式的联动规则，在字段上配置"本字段受哪些来源影响"
- 实现数据源管理器，支持多种类型的数据源
- 使用表达式引擎评估条件表达式和提取依赖字段
- 实现联动调试工具，帮助开发者调试联动规则

### 6.3 性能优化

**挑战**：处理大量数据和复杂表单时的性能问题。

**解决方案**：

- 使用React Query进行数据缓存和失效处理
- 实现分页、排序和筛选等功能，减少数据传输量
- 使用虚拟滚动技术，只渲染可见区域的数据
- 使用React.memo和useMemo减少不必要的重渲染

### 6.4 用户体验优化

**挑战**：提供良好的用户体验，特别是在表单操作和数据加载方面。

**解决方案**：

- 使用骨架屏和加载指示器提示用户数据正在加载
- 使用Toast提示用户操作结果
- 实现表单验证和错误提示
- 使用对话框进行确认操作

## 7. 总结

本文档综合了低代码平台前端的需求和技术方案，详细分析了当前开发进度、核心功能、系统架构设计和下一步开发计划。通过这些分析，我们可以清晰地了解项目的整体情况，为后续开发提供足够的上下文和指导。

项目已经完成了基础架构搭建和核心功能实现，包括实体表单、实体列表、实体对话框、字段联动规则、表单验证规则和列表筛选功能等。下一步将重点关注环境变量配置、GraphQL请求/响应拦截器、元数据缓存机制和表单字段控件的联动规则可视化配置等功能。

通过持续优化和完善，我们将打造一个功能强大、易用性高的低代码平台前端，为用户提供灵活、高效的开发体验。
