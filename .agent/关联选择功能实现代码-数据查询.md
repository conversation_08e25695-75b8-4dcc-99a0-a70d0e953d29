# 关联选择功能实现代码 - 数据查询

## 1. 关联数据缓存实现 (src/lib/relation/cache.ts)

```typescript
/**
 * 关联数据缓存
 */
class RelationDataCache {
  private cache: Map<string, {
    data: any[]
    timestamp: number
    hasMore: boolean
    total: number
  }> = new Map()
  
  private readonly TTL = 5 * 60 * 1000 // 5分钟缓存过期
  
  /**
   * 生成缓存键
   */
  private generateKey(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number
  ): string {
    return `${module}.${entity}:${page}:${pageSize}:${JSON.stringify(condition)}`
  }
  
  /**
   * 获取缓存数据
   */
  get<T>(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number
  ): { data: T[], hasMore: boolean, total: number } | null {
    const key = this.generateKey(entity, module, condition, page, pageSize)
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }
    
    return {
      data: cached.data as T[],
      hasMore: cached.hasMore,
      total: cached.total
    }
  }
  
  /**
   * 设置缓存数据
   */
  set<T>(
    entity: string,
    module: string,
    condition: Record<string, unknown>,
    page: number,
    pageSize: number,
    data: T[],
    hasMore: boolean,
    total: number
  ): void {
    const key = this.generateKey(entity, module, condition, page, pageSize)
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hasMore,
      total
    })
  }
  
  /**
   * 清除缓存
   */
  clear(entity?: string, module?: string): void {
    if (!entity && !module) {
      this.cache.clear()
      return
    }
    
    const prefix = module ? `${module}.${entity || ''}` : entity
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix!)) {
        this.cache.delete(key)
      }
    }
  }
  
  /**
   * 获取实体详情缓存
   */
  getDetail<T>(
    entity: string,
    module: string,
    id: string
  ): T | null {
    const key = `${module}.${entity}:detail:${id}`
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data[0] as T
  }
  
  /**
   * 设置实体详情缓存
   */
  setDetail<T>(
    entity: string,
    module: string,
    id: string,
    data: T
  ): void {
    const key = `${module}.${entity}:detail:${id}`
    this.cache.set(key, {
      data: [data],
      timestamp: Date.now(),
      hasMore: false,
      total: 1
    })
  }
}

// 导出单例
export const relationDataCache = new RelationDataCache()
```

## 2. 关联数据查询实现 (src/lib/relation/api.ts)

```typescript
import { PageResponse } from '@/features/metadata/types'
import { fetchEntityMetadata } from '@/features/metadata/api/metadataApi'
import { fetchEntityList, fetchEntityDetail } from '@/features/entity/api/entityApi'
import { relationDataCache } from './cache'

/**
 * 查询关联实体数据
 * @param entity 实体名称
 * @param module 模块名称
 * @param condition 查询条件
 * @param page 页码
 * @param pageSize 每页条数
 * @param context 上下文
 * @param useCache 是否使用缓存
 * @returns 分页数据
 */
export const fetchRelationData = async <T>(
  entity: string,
  module: string = 'meta',
  condition: Record<string, unknown> = {},
  page: number = 1,
  pageSize: number = 10,
  context: Record<string, any> = {},
  useCache: boolean = true
): Promise<PageResponse<T>> => {
  // 如果使用缓存，先尝试从缓存获取
  if (useCache) {
    const cached = relationDataCache.get<T>(entity, module, condition, page, pageSize)
    if (cached) {
      return {
        items: cached.data,
        page,
        page_size: pageSize,
        has_more: cached.hasMore,
        total_page: Math.ceil(cached.total / pageSize),
        total_size: cached.total,
      }
    }
  }
  
  try {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 使用元数据查询实体列表
    const response = await fetchEntityList<T>(
      entity,
      page,
      pageSize,
      condition,
      module,
      metadata,
      context
    )
    
    // 如果使用缓存，将结果存入缓存
    if (useCache) {
      relationDataCache.set(
        entity,
        module,
        condition,
        page,
        pageSize,
        response.items,
        response.has_more,
        response.total_size
      )
    }
    
    return response
  } catch (error) {
    console.error(`Failed to fetch relation data for ${entity}:`, error)
    throw error
  }
}

/**
 * 查询关联实体详情
 * @param entity 实体名称
 * @param id 实体ID
 * @param module 模块名称
 * @param context 上下文
 * @param useCache 是否使用缓存
 * @returns 实体详情
 */
export const fetchRelationDetail = async <T>(
  entity: string,
  id: string,
  module: string = 'meta',
  context: Record<string, any> = {},
  useCache: boolean = true
): Promise<T> => {
  // 如果使用缓存，先尝试从缓存获取
  if (useCache) {
    const cached = relationDataCache.getDetail<T>(entity, module, id)
    if (cached) {
      return cached
    }
  }
  
  try {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 查询实体详情
    const detail = await fetchEntityDetail<T>(
      entity,
      id,
      module,
      metadata,
      context
    )
    
    // 如果使用缓存，将结果存入缓存
    if (useCache) {
      relationDataCache.setDetail(entity, module, id, detail)
    }
    
    return detail
  } catch (error) {
    console.error(`Failed to fetch relation detail for ${entity}:`, error)
    throw error
  }
}

/**
 * 批量查询关联实体详情
 * @param entity 实体名称
 * @param ids 实体ID数组
 * @param module 模块名称
 * @param context 上下文
 * @param useCache 是否使用缓存
 * @returns 实体详情数组
 */
export const fetchRelationBatch = async <T>(
  entity: string,
  ids: string[],
  module: string = 'meta',
  context: Record<string, any> = {},
  useCache: boolean = true
): Promise<T[]> => {
  if (ids.length === 0) {
    return []
  }
  
  // 如果使用缓存，先尝试从缓存获取
  if (useCache) {
    const cachedItems: T[] = []
    const missingIds: string[] = []
    
    // 检查每个ID是否有缓存
    ids.forEach(id => {
      const cached = relationDataCache.getDetail<T>(entity, module, id)
      if (cached) {
        cachedItems.push(cached)
      } else {
        missingIds.push(id)
      }
    })
    
    // 如果所有ID都有缓存，直接返回
    if (missingIds.length === 0) {
      return cachedItems
    }
    
    // 否则只查询缺失的ID
    try {
      const missingItems = await fetchMissingItems(missingIds)
      return [...cachedItems, ...missingItems]
    } catch (error) {
      console.error(`Failed to fetch missing relation items for ${entity}:`, error)
      throw error
    }
  }
  
  // 不使用缓存，直接查询所有ID
  return fetchMissingItems(ids)
  
  // 内部函数：查询缺失的ID
  async function fetchMissingItems(missingIds: string[]): Promise<T[]> {
    // 获取实体元数据
    const metadata = await fetchEntityMetadata(entity, module)
    
    // 查询条件
    const condition = {
      where: [
        {
          _id: {
            __IN: missingIds
          }
        }
      ]
    }
    
    // 查询实体列表
    const response = await fetchEntityList<T>(
      entity,
      1,
      missingIds.length,
      condition,
      module,
      metadata,
      context
    )
    
    // 如果使用缓存，将结果存入缓存
    if (useCache) {
      response.items.forEach(item => {
        const id = (item as any)._id
        if (id) {
          relationDataCache.setDetail(entity, module, id, item)
        }
      })
    }
    
    return response.items
  }
}
```
