# 实体表单自定义验证规则分析

## 1. 需求概述

实现实体表单的自定义验证规则功能，使得用户可以通过元数据配置灵活的表单验证逻辑，包括：

1. 支持多种验证类型（必填、最小/最大值、正则表达式、依赖字段验证等）
2. 支持条件验证（根据其他字段的值决定是否启用验证规则）
3. 支持自定义错误消息
4. 支持字段间的依赖验证（如结束日期必须晚于开始日期）
5. 支持自定义验证函数

## 2. 现有实现分析

### 2.1 数据结构

目前系统已经定义了基本的验证规则数据结构：

```typescript
/**
 * 验证规则
 */
export interface ValidationRule {
  type: string               // 验证类型（required, min, max, pattern等）
  params?: any               // 验证参数
  message?: string           // 错误消息
  condition?: string         // 启用条件表达式
  dependsOn?: string[]       // 条件依赖的字段
}

/**
 * 实体字段元数据
 */
export interface EntityField {
  // 其他字段...
  validation?: ValidationRule[] // 验证规则（服务端和前端共用）
}
```

### 2.2 验证规则处理

当前系统中的验证规则处理主要通过以下几个部分实现：

1. `getFieldValidation` 函数：将字段的验证规则转换为 React Hook Form 可用的验证规则
2. `generateFieldValidation` 函数：根据字段类型生成 Zod 验证规则
3. `useFieldDependencyValidation` hook：处理字段依赖验证

### 2.3 已支持的验证类型

目前系统已支持的验证类型包括：

- `required`：必填验证
- `min`：最小值/最小长度验证
- `max`：最大值/最大长度验证
- `pattern`：正则表达式验证
- `email`：电子邮件格式验证
- `url`：URL格式验证
- `dependency`：依赖字段验证
- `custom`：自定义验证（有限支持）

### 2.4 条件验证实现

条件验证通过 `ExpressionEngine` 实现，可以根据表单中其他字段的值决定是否应用验证规则：

```typescript
if (rule.condition) {
  try {
    const conditionMet = ExpressionEngine.evaluate(
      rule.condition,
      formValues
    )
    if (!conditionMet) {
      continue // 条件不满足，跳过此规则
    }
  } catch (error) {
    console.error(
      `Error evaluating condition for ${field.name}:`,
      error
    )
    continue // 条件评估出错，跳过此规则
  }
}
```

## 3. 问题与不足

1. **自定义验证函数支持有限**：当前的 `custom` 类型验证只支持几种简单模式，无法处理复杂的自定义验证逻辑
2. **验证规则与表单状态的联动不完善**：当依赖字段值变化时，需要更可靠地触发验证
3. **验证错误消息不够灵活**：无法根据字段值或其他字段值动态生成错误消息
4. **缺乏复合验证规则**：无法方便地组合多个验证规则（如 AND/OR 逻辑）
5. **缺乏异步验证支持**：无法进行需要异步操作的验证（如远程唯一性检查）

## 4. 改进方案

### 4.1 增强自定义验证函数

改进 `custom` 类型验证，支持更灵活的自定义验证逻辑：

```typescript
case 'custom':
  try {
    // 使用表达式引擎评估自定义验证函数
    const isValid = ExpressionEngine.evaluate(
      rule.params.expression,
      { value, ...formValues }
    )
    
    if (!isValid) {
      return rule.message || `验证失败`
    }
  } catch (error) {
    console.error('Error in custom validation:', error)
    return `验证失败`
  }
  break
```

### 4.2 增强条件验证

支持更复杂的条件表达式，包括对多个字段的引用和复合条件：

```typescript
// 条件表达式示例
"condition": "age > 18 && (gender === 'male' || hasParentalConsent === true)"
```

### 4.3 动态错误消息

支持在错误消息中引用字段值和其他字段值：

```typescript
// 动态错误消息示例
"message": "价格必须大于 ${minPrice} 且小于 ${maxPrice}"
```

实现方式：

```typescript
// 处理动态错误消息
if (rule.message && rule.message.includes('${')) {
  const interpolatedMessage = rule.message.replace(
    /\${([^}]+)}/g,
    (match, fieldName) => {
      return String(formValues[fieldName] || match)
    }
  )
  return interpolatedMessage
}
```

### 4.4 复合验证规则

增加对复合验证规则的支持，如 `oneOf`、`allOf` 等：

```typescript
case 'oneOf':
  // 至少一个子规则验证通过
  for (const subRule of rule.params.rules) {
    // 递归验证子规则
    const subResult = validateRule(subRule, value, formValues)
    if (subResult === true) {
      return true
    }
  }
  return rule.message || `验证失败`

case 'allOf':
  // 所有子规则都必须验证通过
  for (const subRule of rule.params.rules) {
    // 递归验证子规则
    const subResult = validateRule(subRule, value, formValues)
    if (subResult !== true) {
      return subResult
    }
  }
  return true
```

### 4.5 异步验证支持

增加对异步验证的支持，特别是对于需要远程检查的验证规则：

```typescript
case 'async':
  // 返回一个异步验证函数
  return async () => {
    try {
      // 调用异步验证API
      const response = await validateAsync(
        rule.params.url,
        { field: field.name, value, ...formValues }
      )
      
      if (!response.valid) {
        return response.message || rule.message || `验证失败`
      }
      return true
    } catch (error) {
      console.error('Async validation error:', error)
      return `验证失败`
    }
  }
```

## 5. 实现计划

### 5.1 改进 ValidationRule 接口

```typescript
export interface ValidationRule {
  type: string               // 验证类型
  params?: any               // 验证参数
  message?: string           // 错误消息（支持模板语法）
  condition?: string         // 启用条件表达式
  dependsOn?: string[]       // 条件依赖的字段
  async?: boolean            // 是否为异步验证
}

// 复合验证规则
export interface CompositeValidationRule extends ValidationRule {
  type: 'oneOf' | 'allOf'
  params: {
    rules: ValidationRule[]
  }
}

// 自定义验证规则
export interface CustomValidationRule extends ValidationRule {
  type: 'custom'
  params: {
    expression: string       // 验证表达式
  }
}

// 异步验证规则
export interface AsyncValidationRule extends ValidationRule {
  type: 'async'
  params: {
    url: string              // 验证API地址
    method?: string          // HTTP方法
    headers?: Record<string, string> // 请求头
  }
  async: true
}
```

### 5.2 改进 getFieldValidation 函数

1. 重构 `getFieldValidation` 函数，支持更多验证类型和复合验证
2. 增加对动态错误消息的处理
3. 增加对异步验证的支持

### 5.3 改进 useFieldDependencyValidation hook

1. 优化依赖字段变化时的验证触发逻辑
2. 减少不必要的验证触发，提高性能

### 5.4 添加验证规则测试用例

为各种验证规则类型添加测试用例，确保验证逻辑正确工作。

## 6. 示例配置

### 6.1 基本验证规则

```json
{
  "name": "price",
  "display_name": "价格",
  "type": "FLOAT",
  "default_value": "0",
  "flags": [],
  "_id": "123456",
  "validation": [
    {
      "type": "required",
      "message": "请输入价格"
    },
    {
      "type": "min",
      "params": {
        "min": 0
      },
      "message": "价格不能为负数"
    },
    {
      "type": "max",
      "params": {
        "max": 1000000
      },
      "message": "价格不能超过100万"
    }
  ]
}
```

### 6.2 条件验证规则

```json
{
  "name": "discountPrice",
  "display_name": "折扣价",
  "type": "FLOAT",
  "default_value": null,
  "flags": ["NULLABLE"],
  "_id": "123457",
  "validation": [
    {
      "type": "required",
      "message": "请输入折扣价",
      "condition": "hasDiscount === true",
      "dependsOn": ["hasDiscount"]
    },
    {
      "type": "min",
      "params": {
        "min": 0
      },
      "message": "折扣价不能为负数"
    },
    {
      "type": "custom",
      "params": {
        "expression": "value < formValues.price"
      },
      "message": "折扣价必须低于原价",
      "dependsOn": ["price"]
    }
  ]
}
```

### 6.3 依赖字段验证

```json
{
  "name": "endDate",
  "display_name": "结束日期",
  "type": "DATE",
  "default_value": null,
  "flags": [],
  "_id": "123458",
  "validation": [
    {
      "type": "required",
      "message": "请选择结束日期"
    },
    {
      "type": "dependency",
      "params": {
        "field": "startDate",
        "expression": "new Date(value) > new Date(dependencyValue)"
      },
      "message": "结束日期必须晚于开始日期",
      "dependsOn": ["startDate"]
    }
  ]
}
```

### 6.4 复合验证规则

```json
{
  "name": "contactInfo",
  "display_name": "联系方式",
  "type": "VARCHAR",
  "default_value": null,
  "flags": ["NULLABLE"],
  "_id": "123459",
  "validation": [
    {
      "type": "oneOf",
      "params": {
        "rules": [
          {
            "type": "pattern",
            "params": {
              "pattern": "^1[3-9]\\d{9}$"
            },
            "message": "请输入有效的手机号码"
          },
          {
            "type": "email",
            "message": "请输入有效的电子邮件地址"
          }
        ]
      },
      "message": "请输入有效的手机号码或电子邮件地址"
    }
  ]
}
```

### 6.5 异步验证规则

```json
{
  "name": "username",
  "display_name": "用户名",
  "type": "VARCHAR",
  "default_value": null,
  "flags": [],
  "_id": "123460",
  "validation": [
    {
      "type": "required",
      "message": "请输入用户名"
    },
    {
      "type": "pattern",
      "params": {
        "pattern": "^[a-zA-Z0-9_]{3,20}$"
      },
      "message": "用户名只能包含字母、数字和下划线，长度3-20"
    },
    {
      "type": "async",
      "params": {
        "url": "/api/validate/username",
        "method": "POST"
      },
      "message": "用户名已被使用",
      "async": true
    }
  ]
}
```

## 7. 总结

实体表单的自定义验证规则功能将大大增强表单的灵活性和可用性，使得用户可以通过元数据配置实现复杂的表单验证逻辑，而无需编写代码。通过支持条件验证、依赖字段验证、复合验证规则和异步验证，可以满足各种复杂的业务场景需求。
