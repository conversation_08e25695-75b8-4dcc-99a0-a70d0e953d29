# 双引擎驱动低代码平台

## 🚀 项目概述

本项目实现了一个双引擎驱动的低代码平台，同时支持注解驱动和元数据驱动两种模式，为开发者提供了灵活的元数据定义方式。

## ✨ 核心特性

- **🔄 双引擎协同**：注解驱动和配置驱动可以同时工作
- **⚡ 智能合并**：支持多种合并策略，可配置优先级
- **🔌 统一API**：提供统一的元数据访问接口
- **🔙 向后兼容**：完全兼容现有的MetaManager API
- **💾 缓存支持**：内置缓存机制，提高性能
- **📝 丰富注解**：支持验证规则和UI控件配置
- **🎯 类型安全**：注解驱动提供编译时类型检查

## 🏗️ 架构设计

```
双引擎元数据管理器 (DualEngineMetaManager)
├── 注解元数据提供者 (AnnotationMetaProvider)
├── 配置元数据提供者 (ConfigMetaProvider)
└── 统一元数据模型 (MetaDefinition)
```

## 📦 新增组件

### 核心管理器

- `DualEngineMetaManager` - 双引擎元数据管理器
- `IMetaProvider` - 元数据提供者接口
- `AnnotationMetaProvider` - 注解元数据提供者
- `ConfigMetaProvider` - 配置元数据提供者

### 统一模型

- `MetaDefinition` - 统一元数据定义
- `EntityDefinition` - 实体定义
- `FieldDefinition` - 字段定义
- `RelationDefinition` - 关联定义
- `UIControlDefinition` - UI控件定义

### 配置管理

- `MetaConfiguration` - 元数据配置类
- `MetaSource` - 元数据来源注解

## 🔧 使用方法

### 1. 注解驱动示例

```java

@Entity(module = "demo", name = "product", displayName = "产品")
@MetaSource(source = MetaSource.SourceType.ANNOTATION, priority = 100)
public class Product {

    @Field(
            displayName = "产品名称",
            flags = {FieldFlag.REQUIRED},
            validations = {
                    @Field.Validation(type = "required", message = "产品名称不能为空")
            },
            uiControl = @Field.UIControl(
                    type = "input",
                    placeholder = "请输入产品名称",
                    group = "基本信息"
            )
    )
    private String name;

    @Relation(
            displayName = "产品分类",
            entity = "category",
            type = RelationType.REF_ONE
    )
    private Object category;
}
```

### 2. 配置驱动示例

```json
{
  "name": "category",
  "displayName": "产品分类",
  "module": "demo",
  "source": "CONFIG",
  "priority": 50,
  "fields": [
    {
      "name": "name",
      "displayName": "分类名称",
      "dataType": "TEXT",
      "validationRules": [
        {
          "type": "REQUIRED",
          "message": "分类名称不能为空"
        }
      ],
      "uiControl": {
        "type": "input",
        "placeholder": "请输入分类名称"
      }
    }
  ]
}
```

### 3. API使用

```java

@Autowired
private DualEngineMetaManager metaManager;

// 获取元数据定义
MetaDefinition definition = metaManager.getMetaDefinition("product");

// 获取DefEntity（兼容现有API）
DefEntity entity = metaManager.getDefEntity("product");

// 或者使用传统方式（自动路由到双引擎）
DefEntity entity2 = MetaManager.get("product");
```

## ⚙️ 配置说明

### application.yml 配置

```yaml
vibe:
  meta:
    dual-engine-enabled: true
    merge-strategy: PRIORITY_BASED
    cache-enabled: true
    cache-expire-seconds: 3600
    entities:
      product:
        source: ANNOTATION
        cache-enabled: true
      category:
        source: CONFIG
        config-path: "classpath:meta/demo/category.json"
```

### 合并策略

- `PRIORITY_BASED`: 基于优先级合并（推荐）
- `ANNOTATION_FIRST`: 注解优先
- `CONFIG_FIRST`: 配置优先
- `DEEP_MERGE`: 深度合并

## 📁 文件结构

```
src/main/java/com/edc/vibe_engine/
├── meta/
│   ├── annotation/
│   │   ├── MetaSource.java          # 元数据来源注解
│   │   └── Field.java               # 扩展的字段注解
│   ├── config/
│   │   └── MetaConfiguration.java   # 元数据配置类
│   ├── manager/
│   │   └── DualEngineMetaManager.java # 双引擎管理器
│   ├── model/
│   │   ├── MetaDefinition.java      # 统一元数据模型
│   │   ├── FieldDefinition.java     # 字段定义
│   │   └── UIControlDefinition.java # UI控件定义
│   ├── provider/
│   │   ├── AnnotationMetaProvider.java # 注解提供者
│   │   └── ConfigMetaProvider.java     # 配置提供者
│   └── interfaces/
│       └── IMetaProvider.java       # 提供者接口
├── demo/entity/
│   └── Product.java                 # 示例实体
└── config/
    └── DualEngineConfiguration.java # 配置类

src/main/resources/
├── meta/demo/
│   └── category.json               # 示例配置文件
└── application-dual-engine.yml    # 应用配置
```

## 🧪 测试

### 运行测试

```bash
# 运行单元测试
./gradlew test

# 运行特定测试
./gradlew test --tests DualEngineMetaManagerTest
```

### 测试覆盖

- ✅ 注解元数据解析
- ✅ 配置元数据加载
- ✅ 双引擎合并策略
- ✅ 缓存机制
- ✅ API兼容性

## 🚀 快速开始

### 1. 启用双引擎模式

```yaml
# application.yml
spring:
  profiles:
    active: dual-engine

vibe:
  meta:
    dual-engine-enabled: true
```

### 2. 创建注解实体

```java

@Entity(module = "demo", name = "product", displayName = "产品")
public class Product {
    @Field(displayName = "产品名称", flags = {FieldFlag.REQUIRED})
    private String name;
}
```

### 3. 创建配置文件

```json
// src/main/resources/meta/demo/category.json
{
  "name": "category",
  "displayName": "产品分类",
  "module": "demo",
  "fields": [
    ...
  ]
}
```

### 4. 使用API

```java

@Autowired
private DualEngineMetaManager metaManager;

MetaDefinition definition = metaManager.getMetaDefinition("product");
```

## 📈 性能优化

- **缓存机制**：启用元数据缓存，减少重复解析
- **懒加载**：按需加载元数据，避免启动时间过长
- **批量操作**：支持批量获取元数据
- **内存优化**：合理的缓存过期策略

## 🔍 故障排除

### 常见问题

1. **元数据未找到**
    - 检查包扫描路径
    - 确认配置文件路径正确

2. **合并结果不符合预期**
    - 检查优先级设置
    - 确认合并策略配置

3. **性能问题**
    - 启用缓存
    - 检查扫描范围

### 调试技巧

```yaml
# 启用调试日志
logging:
  level:
    com.edc.vibe_engine.meta: DEBUG
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**注意**：这是一个实验性功能，建议在生产环境使用前进行充分测试。
