# GraphQL数据接口交互系统分析

## 1. 系统架构

本次迭代将完善前端与服务端的数据接口交互，采用GraphQL规范实现。系统架构如下：

```
前端应用 <---> GraphQL客户端 <---> GraphQL服务端 <---> 业务服务 <---> 数据库
```

主要组件：

1. **GraphQL客户端**：使用graphql-request库，负责构建和发送GraphQL请求
2. **GraphQL查询生成器**：根据实体元数据动态生成GraphQL查询
3. **条件构建器**：构建Condition类型的查询条件
4. **数据访问层**：封装GraphQL操作，提供统一的数据访问接口

## 2. 技术方案

### 2.1 GraphQL客户端配置

#### 2.1.1 模块化端点支持

当前系统已有GraphQL客户端配置，需要扩展以支持模块化端点：

```typescript
// 创建GraphQL客户端实例
export const createGraphQLClient = (module: string = 'meta'): IGraphQLClient => {
  const token = useAuthStore.getState().auth.accessToken
  const endpoint = `${import.meta.env.VITE_API_BASE_URL || ''}/${module}/graphql`

  return new GraphQLClient(endpoint, {
    headers: token
      ? {
          Authorization: `Bearer ${token}`,
        }
      : {},
  })
}

// 获取特定模块的GraphQL客户端
export const getModuleGraphQLClient = (module: string): IGraphQLClient => {
  if (import.meta.env.DEV) {
    return mockGraphQLClient
  }
  return createGraphQLClient(module)
}
```

#### 2.1.2 GraphQL客户端接口扩展

扩展现有的IGraphQLClient接口，增加模块支持：

```typescript
export interface IGraphQLClient {
  request<T = any, V = Record<string, unknown>>(
    query: string,
    variables?: V
  ): Promise<T>
  
  // 新增方法
  setModule(module: string): void
  getModule(): string
}
```

### 2.2 查询生成器

#### 2.2.1 基于元数据生成查询

创建一个查询生成器，根据实体元数据生成GraphQL查询：

```typescript
export class QueryGenerator {
  // 生成实体详情查询
  static generateDetailQuery(entityMetadata: EntityMetadata): string {
    const { name, fields } = entityMetadata
    const fieldNames = fields.map(field => field.name).join('\n    ')
    
    return `
      query Get${name}Detail($_id: ID!) {
        ${name}_detail(_id: $_id) {
          ${fieldNames}
        }
      }
    `
  }
  
  // 生成实体分页查询
  static generatePageQuery(entityMetadata: EntityMetadata): string {
    const { name, fields } = entityMetadata
    const fieldNames = fields.map(field => field.name).join('\n      ')
    
    return `
      query Get${name}Page($condition: JSON, $page: Int, $page_size: Int) {
        ${name}_page(condition: $condition, page: $page, page_size: $page_size) {
          items {
            ${fieldNames}
          }
          total
          page
          page_size
        }
      }
    `
  }
  
  // 其他查询生成方法...
}
```

### 2.3 条件构建器

扩展现有的Condition类，增加对GraphQL查询条件的支持：

```typescript
export class GraphQLConditionBuilder {
  private condition: Condition
  
  constructor() {
    this.condition = new Condition()
  }
  
  // 添加基本条件
  addBasicCondition(field: string, operator: Compare, value: any): this {
    // 根据操作符类型添加条件
    switch (operator) {
      case '__EQ':
        this.condition.eq(field, value)
        break
      case '__NE':
        this.condition.ne(field, value)
        break
      // 其他操作符...
    }
    return this
  }
  
  // 添加AND条件组
  addAndGroup(conditions: Array<{field: string, operator: Compare, value: any}>): this {
    this.condition.and(subCond => {
      conditions.forEach(({ field, operator, value }) => {
        this.addBasicCondition(field, operator, value)
      })
    })
    return this
  }
  
  // 添加OR条件组
  addOrGroup(conditions: Array<{field: string, operator: Compare, value: any}>): this {
    this.condition.or(subCond => {
      conditions.forEach(({ field, operator, value }) => {
        this.addBasicCondition(field, operator, value)
      })
    })
    return this
  }
  
  // 获取JSON格式的条件
  build(): any {
    return this.condition.asJson()
  }
}
```

### 2.4 数据访问层

#### 2.4.1 实体API扩展

扩展现有的entityApi.ts，增加模块支持：

```typescript
// 获取实体列表（分页）
export const fetchEntityList = async <T>(
  entityName: string,
  page: number,
  pageSize: number,
  condition: Record<string, unknown> = {},
  module: string = 'meta'
): Promise<PageResponse<T>> => {
  const client = getModuleGraphQLClient(module)
  const query = getEntityPageQuery(entityName)

  const variables = {
    condition,
    page,
    page_size: pageSize,
  }

  const response = await client.request(query, variables)
  return response[`${entityName}_page`] as PageResponse<T>
}

// 其他API方法类似扩展...
```

#### 2.4.2 元数据API扩展

扩展现有的metadataApi.ts，增加模块支持：

```typescript
// 获取实体元数据
export const fetchEntityMetadata = async (
  entityName: string,
  module: string = 'meta'
): Promise<EntityMetadata> => {
  const client = getModuleGraphQLClient(module)
  const response = await client.request<{
    _metadata: EntityMetadata
  }>(GET_ENTITY_METADATA, { key: `def_entity.${entityName}` })
  return response._metadata as EntityMetadata
}
```

## 3. 实现路径

### 3.1 GraphQL客户端扩展

1. 修改src/lib/graphql/client.ts，增加模块支持
2. 修改src/lib/graphql/types.ts，扩展客户端接口
3. 修改src/lib/graphql/mock-client.ts，实现模拟客户端的模块支持

### 3.2 查询生成器实现

1. 创建src/lib/graphql/query-generator.ts，实现查询生成器
2. 修改src/lib/graphql/queries.ts和mutations.ts，使用查询生成器

### 3.3 条件构建器实现

1. 创建src/lib/condition/graphql-builder.ts，实现GraphQL条件构建器
2. 集成到实体列表筛选组件中

### 3.4 数据访问层扩展

1. 修改src/features/entity/api/entityApi.ts，增加模块支持
2. 修改src/features/metadata/api/metadataApi.ts，增加模块支持

## 4. 测试计划

1. 单元测试：测试查询生成器和条件构建器
2. 集成测试：测试GraphQL客户端与后端的交互
3. 端到端测试：测试完整的数据流程

## 5. 风险与应对措施

1. **GraphQL查询复杂度**：对于复杂实体，生成的查询可能过大
    - 应对：实现字段筛选，只查询必要字段

2. **模块化端点兼容性**：现有代码可能假设单一端点
    - 应对：确保所有API调用都使用新的模块化客户端

3. **条件构建复杂度**：复杂的筛选条件可能难以构建
    - 应对：提供更高级的条件构建API，简化使用
