# 关联选择功能实现代码 - 组件优化

## 1. 关联选择组件优化 (src/lib/relation/components/relation-select.tsx)

```typescript
import React, { useState, useEffect, useCallback } from 'react'
import { useFormContext } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { ChevronDown, X, Search, Loader2 } from 'lucide-react'
import { EntityMetadata } from '@/features/metadata/types'
import { EntityRelation } from '@/features/metadata/types/relation'
import { getMatchingRelation } from '../condition'
import { fetchRelationData, fetchRelationDetail } from '../api'
import { useRelationDependency } from '../hooks/useRelationDependency'

/**
 * 关联选择组件属性
 */
export interface RelationSelectProps {
  value: string | null
  onChange: (value: string | null) => void
  disabled?: boolean
  readOnly?: boolean
  placeholder?: string
  metadata: EntityMetadata
  field: string
  context?: Record<string, any>
}

/**
 * 关联选择组件
 */
export const RelationSelect: React.FC<RelationSelectProps> = ({
  value,
  onChange,
  disabled = false,
  readOnly = false,
  placeholder = '请选择',
  metadata,
  field,
  context = {},
}) => {
  // 状态管理
  const [isOpen, setIsOpen] = useState(false)
  const [items, setItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedItem, setSelectedItem] = useState<any | null>(null)
  const [searchText, setSearchText] = useState('')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const [totalSize, setTotalSize] = useState(0)
  
  // 获取表单上下文
  const formContext = useFormContext()
  
  // 获取关联配置
  const relation = getMatchingRelation(metadata.relations, field, context)
  
  // 如果没有关联配置，显示错误信息
  if (!relation) {
    return (
      <div className="text-destructive">
        错误：未找到字段 {field} 的关联配置
      </div>
    )
  }
  
  // 获取关联配置参数
  const {
    entity,
    module = 'meta',
    control,
  } = relation
  
  const labelField = control?.labelField || 'name'
  const valueField = control?.valueField || '_id'
  const displayFields = control?.displayFields || [labelField]
  const isSearchable = control?.searchable !== false
  
  // 使用关联依赖 Hook
  const { filter, isDependencyReady, hasDependencyChanged } = useRelationDependency(field, control)
  
  // 加载数据
  const loadItems = useCallback(async (
    page: number,
    searchText: string,
    refresh: boolean = false
  ) => {
    // 如果依赖未就绪，不加载数据
    if (!isDependencyReady) {
      setItems([])
      setHasMore(false)
      setTotalSize(0)
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // 构建查询条件
      const condition: Record<string, unknown> = { ...filter }
      
      // 添加搜索条件
      if (searchText) {
        if (!condition.where) {
          condition.where = []
        }
        
        // 在标签字段上搜索
        const searchCondition: Record<string, unknown> = {}
        searchCondition[labelField] = { __LIKE: searchText }
        
        // 添加到 where 条件中
        if (Array.isArray(condition.where)) {
          condition.where.push(searchCondition)
        }
      }
      
      // 查询数据
      const response = await fetchRelationData(
        entity,
        module,
        condition,
        page,
        10,
        context
      )
      
      // 更新状态
      if (refresh || page === 1) {
        setItems(response.items)
      } else {
        setItems(prev => [...prev, ...response.items])
      }
      
      setPage(page)
      setHasMore(response.has_more)
      setTotalSize(response.total_size)
    } catch (error) {
      console.error('Failed to load relation items:', error)
      setError('加载数据失败，请重试')
    } finally {
      setLoading(false)
    }
  }, [entity, module, filter, labelField, isDependencyReady, context])
  
  // 当依赖变化时，重新加载数据
  useEffect(() => {
    if (isOpen && hasDependencyChanged) {
      loadItems(1, searchText, true)
    }
  }, [isOpen, hasDependencyChanged, loadItems, searchText])
  
  // 当对话框打开时，加载数据
  useEffect(() => {
    if (isOpen) {
      loadItems(1, searchText)
    }
  }, [isOpen, loadItems, searchText])
  
  // 当值变化时，加载选中项
  useEffect(() => {
    if (!value) {
      setSelectedItem(null)
      return
    }
    
    // 检查是否已经在加载的数据中
    const existingItem = items.find(item => item[valueField] === value)
    if (existingItem) {
      setSelectedItem(existingItem)
      return
    }
    
    // 否则需要单独加载
    const loadSelectedItem = async () => {
      try {
        setLoading(true)
        
        // 查询详情
        const item = await fetchRelationDetail(
          entity,
          value,
          module,
          context
        )
        
        if (item) {
          setSelectedItem(item)
        }
      } catch (error) {
        console.error('Failed to load selected item:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadSelectedItem()
  }, [value, valueField, items, entity, module, context])
  
  // 处理搜索
  const handleSearch = (text: string) => {
    setSearchText(text)
    setPage(1)
    loadItems(1, text, true)
  }
  
  // 处理加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadItems(page + 1, searchText)
    }
  }
  
  // 处理选择
  const handleSelect = (item: any) => {
    setSelectedItem(item)
    onChange(item[valueField])
    setIsOpen(false)
  }
  
  // 处理清除
  const handleClear = () => {
    setSelectedItem(null)
    onChange(null)
  }
  
  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className="w-full justify-between"
            disabled={disabled || readOnly || !isDependencyReady}
          >
            {selectedItem ? (
              <div className="flex items-center justify-between w-full">
                <span>{selectedItem[labelField]}</span>
                {!readOnly && (
                  <X
                    className="h-4 w-4 opacity-50 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleClear()
                    }}
                  />
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">
                {!isDependencyReady ? '请先填写依赖字段' : placeholder}
              </span>
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>选择{relation.entity}</DialogTitle>
          </DialogHeader>
          
          {isSearchable && (
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 opacity-50" />
              <Input
                placeholder="搜索..."
                value={searchText}
                onChange={(e) => handleSearch(e.target.value)}
                className="flex-1"
              />
            </div>
          )}
          
          <div className="max-h-[300px] overflow-y-auto">
            {loading && items.length === 0 ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin opacity-50" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : error ? (
              <div className="text-center py-4 text-destructive">
                {error}
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                暂无数据
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    {displayFields.map((field) => (
                      <TableHead key={field}>{field}</TableHead>
                    ))}
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item) => (
                    <TableRow key={item[valueField]}>
                      {displayFields.map((field) => (
                        <TableCell key={field}>{item[field]}</TableCell>
                      ))}
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelect(item)}
                        >
                          选择
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
          
          {hasMore && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </>
                ) : (
                  <>加载更多</>
                )}
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
```

## 2. 关联多选组件优化 (src/lib/relation/components/relation-multi-select.tsx)

```typescript
import React, { useState, useEffect, useCallback } from 'react'
import { useFormContext } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, X, Search, Loader2 } from 'lucide-react'
import { EntityMetadata } from '@/features/metadata/types'
import { EntityRelation } from '@/features/metadata/types/relation'
import { getMatchingRelation } from '../condition'
import { fetchRelationData, fetchRelationBatch } from '../api'
import { useRelationDependency } from '../hooks/useRelationDependency'

/**
 * 关联多选组件属性
 */
export interface RelationMultiSelectProps {
  value: string[] | null
  onChange: (value: string[]) => void
  disabled?: boolean
  readOnly?: boolean
  placeholder?: string
  metadata: EntityMetadata
  field: string
  context?: Record<string, any>
}

/**
 * 关联多选组件
 */
export const RelationMultiSelect: React.FC<RelationMultiSelectProps> = ({
  value,
  onChange,
  disabled = false,
  readOnly = false,
  placeholder = '请选择',
  metadata,
  field,
  context = {},
}) => {
  // 状态管理
  const [isOpen, setIsOpen] = useState(false)
  const [items, setItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedItems, setSelectedItems] = useState<any[]>([])
  const [searchText, setSearchText] = useState('')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const [totalSize, setTotalSize] = useState(0)
  
  // 获取表单上下文
  const formContext = useFormContext()
  
  // 获取关联配置
  const relation = getMatchingRelation(metadata.relations, field, context)
  
  // 如果没有关联配置，显示错误信息
  if (!relation) {
    return (
      <div className="text-destructive">
        错误：未找到字段 {field} 的关联配置
      </div>
    )
  }
  
  // 获取关联配置参数
  const {
    entity,
    module = 'meta',
    control,
  } = relation
  
  const labelField = control?.labelField || 'name'
  const valueField = control?.valueField || '_id'
  const displayFields = control?.displayFields || [labelField]
  const isSearchable = control?.searchable !== false
  
  // 使用关联依赖 Hook
  const { filter, isDependencyReady, hasDependencyChanged } = useRelationDependency(field, control)
  
  // 规范化值
  const normalizeValue = (val: string[] | null): string[] => {
    if (!val) return []
    return Array.isArray(val) ? val : [val]
  }
  
  // 当前值
  const currentValue = normalizeValue(value)
  
  // 加载数据
  const loadItems = useCallback(async (
    page: number,
    searchText: string,
    refresh: boolean = false
  ) => {
    // 如果依赖未就绪，不加载数据
    if (!isDependencyReady) {
      setItems([])
      setHasMore(false)
      setTotalSize(0)
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // 构建查询条件
      const condition: Record<string, unknown> = { ...filter }
      
      // 添加搜索条件
      if (searchText) {
        if (!condition.where) {
          condition.where = []
        }
        
        // 在标签字段上搜索
        const searchCondition: Record<string, unknown> = {}
        searchCondition[labelField] = { __LIKE: searchText }
        
        // 添加到 where 条件中
        if (Array.isArray(condition.where)) {
          condition.where.push(searchCondition)
        }
      }
      
      // 查询数据
      const response = await fetchRelationData(
        entity,
        module,
        condition,
        page,
        10,
        context
      )
      
      // 更新状态
      if (refresh || page === 1) {
        setItems(response.items)
      } else {
        setItems(prev => [...prev, ...response.items])
      }
      
      setPage(page)
      setHasMore(response.has_more)
      setTotalSize(response.total_size)
    } catch (error) {
      console.error('Failed to load relation items:', error)
      setError('加载数据失败，请重试')
    } finally {
      setLoading(false)
    }
  }, [entity, module, filter, labelField, isDependencyReady, context])
  
  // 当依赖变化时，重新加载数据
  useEffect(() => {
    if (isOpen && hasDependencyChanged) {
      loadItems(1, searchText, true)
    }
  }, [isOpen, hasDependencyChanged, loadItems, searchText])
  
  // 当对话框打开时，加载数据
  useEffect(() => {
    if (isOpen) {
      loadItems(1, searchText)
    }
  }, [isOpen, loadItems, searchText])
  
  // 当值变化时，加载选中项
  useEffect(() => {
    if (!currentValue.length) {
      setSelectedItems([])
      return
    }
    
    // 检查是否已经在加载的数据中
    const existingItems = items.filter(item => currentValue.includes(item[valueField]))
    
    // 如果所有选中项都在加载的数据中，直接使用
    if (existingItems.length === currentValue.length) {
      setSelectedItems(existingItems)
      return
    }
    
    // 否则需要加载缺失的项
    const missingIds = currentValue.filter(id => !items.some(item => item[valueField] === id))
    
    if (missingIds.length > 0) {
      const loadMissingItems = async () => {
        try {
          setLoading(true)
          
          // 批量查询
          const missingItems = await fetchRelationBatch(
            entity,
            missingIds,
            module,
            context
          )
          
          // 合并结果
          setSelectedItems([...existingItems, ...missingItems])
        } catch (error) {
          console.error('Failed to load selected items:', error)
        } finally {
          setLoading(false)
        }
      }
      
      loadMissingItems()
    } else {
      setSelectedItems(existingItems)
    }
  }, [currentValue, valueField, items, entity, module, context])
  
  // 处理搜索
  const handleSearch = (text: string) => {
    setSearchText(text)
    setPage(1)
    loadItems(1, text, true)
  }
  
  // 处理加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadItems(page + 1, searchText)
    }
  }
  
  // 处理选择
  const handleSelect = (item: any) => {
    const itemId = item[valueField]
    const newValue = [...currentValue]
    
    if (newValue.includes(itemId)) {
      // 如果已选中，则取消选中
      const index = newValue.indexOf(itemId)
      newValue.splice(index, 1)
    } else {
      // 如果未选中，则选中
      newValue.push(itemId)
    }
    
    onChange(newValue)
  }
  
  // 处理清除
  const handleClear = () => {
    setSelectedItems([])
    onChange([])
  }
  
  // 处理移除单个项
  const handleRemoveItem = (itemId: string) => {
    const newValue = currentValue.filter(id => id !== itemId)
    onChange(newValue)
  }
  
  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className="w-full justify-between min-h-10"
            disabled={disabled || readOnly || !isDependencyReady}
          >
            <div className="flex flex-wrap gap-1 items-center justify-start w-full">
              {selectedItems.length > 0 ? (
                selectedItems.map(item => (
                  <Badge key={item[valueField]} variant="secondary" className="mr-1">
                    {item[labelField]}
                    {!readOnly && (
                      <X
                        className="ml-1 h-3 w-3 opacity-50 hover:opacity-100"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemoveItem(item[valueField])
                        }}
                      />
                    )}
                  </Badge>
                ))
              ) : (
                <span className="text-muted-foreground">
                  {!isDependencyReady ? '请先填写依赖字段' : placeholder}
                </span>
              )}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>选择{relation.entity}</DialogTitle>
          </DialogHeader>
          
          {isSearchable && (
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 opacity-50" />
              <Input
                placeholder="搜索..."
                value={searchText}
                onChange={(e) => handleSearch(e.target.value)}
                className="flex-1"
              />
            </div>
          )}
          
          <div className="max-h-[300px] overflow-y-auto">
            {loading && items.length === 0 ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin opacity-50" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : error ? (
              <div className="text-center py-4 text-destructive">
                {error}
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                暂无数据
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]"></TableHead>
                    {displayFields.map((field) => (
                      <TableHead key={field}>{field}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item) => (
                    <TableRow key={item[valueField]}>
                      <TableCell>
                        <Checkbox
                          checked={currentValue.includes(item[valueField])}
                          onCheckedChange={() => handleSelect(item)}
                        />
                      </TableCell>
                      {displayFields.map((field) => (
                        <TableCell key={field}>{item[field]}</TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
          
          {hasMore && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </>
                ) : (
                  <>加载更多</>
                )}
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
```
