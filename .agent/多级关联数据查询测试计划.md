# 多级关联数据查询测试计划

## 1. 单元测试

### 1.1 查询生成器测试

创建 `src/lib/graphql/__tests__/query-generator-nested.test.ts` 文件，测试查询生成器的多级关联查询功能：

```typescript
import { describe, it, expect, beforeAll } from 'vitest'
import { QueryGenerator } from '../query-generator'
import { employeeMetadata, departmentMetadata, companyMetadata } from '@/features/metadata/mock/relation-test-metadata'
import { setEntityMetadataCache } from '@/features/metadata/api/metadataCache'

describe('QueryGenerator - 多级关联查询', () => {
  // 在所有测试前预加载元数据
  beforeAll(() => {
    setEntityMetadataCache('employee', 'hr', employeeMetadata)
    setEntityMetadataCache('department', 'hr', departmentMetadata)
    setEntityMetadataCache('company', 'hr', companyMetadata)
  })

  it('应该生成包含一级关联的详情查询', () => {
    const query = QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      1 // 最大深度为1
    )
    
    // 验证查询包含实体名称
    expect(query).toContain('employee_detail')
    
    // 验证查询包含一级关联
    expect(query).toContain('department_info')
    expect(query).toContain('position_info')
    expect(query).toContain('manager_info')
    
    // 验证查询不包含二级关联
    expect(query).not.toContain('parent_department')
    expect(query).not.toContain('company_info')
  })

  it('应该生成包含多级关联的详情查询', () => {
    const query = QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      3 // 最大深度为3
    )
    
    // 验证查询包含实体名称
    expect(query).toContain('employee_detail')
    
    // 验证查询包含一级关联
    expect(query).toContain('department_info')
    expect(query).toContain('position_info')
    expect(query).toContain('manager_info')
    
    // 验证查询包含二级关联
    expect(query).toContain('parent_department')
    expect(query).toContain('company_info')
    
    // 验证查询包含三级关联
    expect(query).toContain('parent_company')
  })

  it('应该尊重关联配置中的嵌套设置', () => {
    const query = QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      3 // 最大深度为3
    )
    
    // position_info 不允许嵌套查询，不应包含其关联数据
    expect(query).toContain('position_info')
    expect(query).not.toContain('position_info {')
    
    // department_info 允许嵌套查询，应包含其关联数据
    expect(query).toContain('department_info {')
    expect(query).toContain('parent_department {')
    expect(query).toContain('company_info {')
  })

  it('应该尊重关联配置中的最大深度设置', () => {
    const query = QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      5 // 全局最大深度为5
    )
    
    // manager_info 的最大深度为1，不应包含其二级关联
    expect(query).toContain('manager_info {')
    expect(query).not.toContain('manager_info {')
    
    // department_info 的最大深度为2，应包含其一级关联，但不包含二级关联
    expect(query).toContain('department_info {')
    expect(query).toContain('parent_department {')
    expect(query).not.toContain('parent_department {')
  })

  it('应该处理循环引用', () => {
    const query = QueryGenerator.generateDetailQuery(
      departmentMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      5 // 全局最大深度为5
    )
    
    // 部门的上级部门是自关联，应该避免无限递归
    expect(query).toContain('parent_department {')
    expect(query).toContain('parent_department {')
    expect(query).toContain('parent_department {')
    // 不应该有第四级嵌套
    expect(query.match(/parent_department {/g)?.length).toBeLessThanOrEqual(3)
  })
})
```

### 1.2 数据处理器测试

创建 `src/lib/relation/__tests__/processor-nested.test.ts` 文件，测试数据处理器的多级关联数据处理功能：

```typescript
import { describe, it, expect, beforeAll } from 'vitest'
import { processEntityData } from '../processor'
import { employeeMetadata, departmentMetadata, companyMetadata } from '@/features/metadata/mock/relation-test-metadata'
import { setEntityMetadataCache } from '@/features/metadata/api/metadataCache'
import { mockEmployeeDetailData, mockDepartmentDetailData } from '@/features/metadata/mock/relation-test-data'

describe('数据处理器 - 多级关联数据', () => {
  // 在所有测试前预加载元数据
  beforeAll(() => {
    setEntityMetadataCache('employee', 'hr', employeeMetadata)
    setEntityMetadataCache('department', 'hr', departmentMetadata)
    setEntityMetadataCache('company', 'hr', companyMetadata)
  })

  it('应该处理一级关联数据', () => {
    const processedData = processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      1 // 最大深度为1
    )
    
    // 验证基本字段
    expect(processedData._id).toBe(mockEmployeeDetailData._id)
    expect(processedData.name).toBe(mockEmployeeDetailData.name)
    
    // 验证一级关联数据
    expect(processedData.department_id_data).toBeDefined()
    expect(processedData.position_id_data).toBeDefined()
    expect(processedData.manager_id_data).toBeDefined()
    
    // 验证不包含二级关联数据
    expect(processedData.department_id_data.parent_id_data).toBeUndefined()
    expect(processedData.department_id_data.company_id_data).toBeUndefined()
  })

  it('应该处理多级关联数据', () => {
    const processedData = processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      3 // 最大深度为3
    )
    
    // 验证基本字段
    expect(processedData._id).toBe(mockEmployeeDetailData._id)
    expect(processedData.name).toBe(mockEmployeeDetailData.name)
    
    // 验证一级关联数据
    expect(processedData.department_id_data).toBeDefined()
    expect(processedData.position_id_data).toBeDefined()
    expect(processedData.manager_id_data).toBeDefined()
    
    // 验证二级关联数据
    expect(processedData.department_id_data.parent_id_data).toBeDefined()
    expect(processedData.department_id_data.company_id_data).toBeDefined()
    
    // 验证三级关联数据
    expect(processedData.department_id_data.parent_id_data.parent_id_data).toBeDefined()
  })

  it('应该尊重关联配置中的嵌套设置', () => {
    const processedData = processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      3 // 最大深度为3
    )
    
    // position_info 不允许嵌套查询，不应包含其关联数据
    expect(processedData.position_id_data).toBeDefined()
    expect(processedData.position_id_data.department_id_data).toBeUndefined()
    
    // department_info 允许嵌套查询，应包含其关联数据
    expect(processedData.department_id_data).toBeDefined()
    expect(processedData.department_id_data.parent_id_data).toBeDefined()
    expect(processedData.department_id_data.company_id_data).toBeDefined()
  })

  it('应该尊重关联配置中的最大深度设置', () => {
    const processedData = processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      5 // 全局最大深度为5
    )
    
    // manager_info 的最大深度为1，不应包含其二级关联
    expect(processedData.manager_id_data).toBeDefined()
    expect(processedData.manager_id_data.department_id_data).toBeDefined()
    expect(processedData.manager_id_data.department_id_data.parent_id_data).toBeUndefined()
    
    // department_info 的最大深度为2，应包含其一级关联，但不包含二级关联
    expect(processedData.department_id_data).toBeDefined()
    expect(processedData.department_id_data.parent_id_data).toBeDefined()
    expect(processedData.department_id_data.parent_id_data.parent_id_data).toBeUndefined()
  })

  it('应该处理循环引用', () => {
    const processedData = processEntityData(
      mockDepartmentDetailData,
      departmentMetadata,
      { userRole: 'admin', mode: 'view' },
      5 // 全局最大深度为5
    )
    
    // 部门的上级部门是自关联，应该避免无限递归
    expect(processedData.parent_id_data).toBeDefined()
    expect(processedData.parent_id_data.parent_id_data).toBeDefined()
    expect(processedData.parent_id_data.parent_id_data.parent_id_data).toBeDefined()
    // 不应该有第四级嵌套
    expect(processedData.parent_id_data.parent_id_data.parent_id_data.parent_id_data).toBeUndefined()
  })
})
```

### 1.3 元数据预加载测试

创建 `src/features/metadata/api/__tests__/metadataCache.test.ts` 文件，测试元数据预加载功能：

```typescript
import { describe, it, expect } from 'vitest'
import { setEntityMetadataCache, getEntityMetadataSync, clearEntityMetadataCache } from '../metadataCache'
import { preloadRelationMetadata } from '../metadataApi'
import { employeeMetadata } from '@/features/metadata/mock/relation-test-metadata'

describe('元数据缓存', () => {
  it('应该正确设置和获取元数据缓存', () => {
    // 清除缓存
    clearEntityMetadataCache()
    
    // 设置缓存
    setEntityMetadataCache('employee', 'hr', employeeMetadata)
    
    // 获取缓存
    const cachedMetadata = getEntityMetadataSync('employee', 'hr')
    
    // 验证缓存
    expect(cachedMetadata).toBeDefined()
    expect(cachedMetadata?.name).toBe('employee')
    expect(cachedMetadata?.module).toBe('hr')
  })

  it('应该正确清除元数据缓存', () => {
    // 设置缓存
    setEntityMetadataCache('employee', 'hr', employeeMetadata)
    
    // 清除特定缓存
    clearEntityMetadataCache('employee', 'hr')
    
    // 验证缓存已清除
    const cachedMetadata = getEntityMetadataSync('employee', 'hr')
    expect(cachedMetadata).toBeUndefined()
    
    // 设置多个缓存
    setEntityMetadataCache('employee', 'hr', employeeMetadata)
    setEntityMetadataCache('department', 'hr', employeeMetadata)
    
    // 清除所有缓存
    clearEntityMetadataCache()
    
    // 验证所有缓存已清除
    expect(getEntityMetadataSync('employee', 'hr')).toBeUndefined()
    expect(getEntityMetadataSync('department', 'hr')).toBeUndefined()
  })

  it('应该正确预加载关联元数据', async () => {
    // 清除缓存
    clearEntityMetadataCache()
    
    // Mock fetchEntityMetadata 函数
    const mockFetchEntityMetadata = vi.fn().mockImplementation((entity, module) => {
      if (entity === 'department') {
        return Promise.resolve({
          name: 'department',
          module: 'hr',
          fields: [],
          relations: [
            {
              field: 'company_id',
              entity: 'company',
              module: 'hr',
              type: 'one',
              nestedRelations: true,
            },
          ],
        })
      }
      if (entity === 'company') {
        return Promise.resolve({
          name: 'company',
          module: 'hr',
          fields: [],
          relations: [],
        })
      }
      return Promise.resolve({ name: entity, module, fields: [], relations: [] })
    })
    
    // 替换原始函数
    vi.mock('../metadataApi', () => ({
      fetchEntityMetadata: mockFetchEntityMetadata,
      preloadRelationMetadata: vi.fn(),
    }))
    
    // 预加载关联元数据
    await preloadRelationMetadata(employeeMetadata, {}, 2)
    
    // 验证 fetchEntityMetadata 被调用
    expect(mockFetchEntityMetadata).toHaveBeenCalledWith('department', 'hr')
    expect(mockFetchEntityMetadata).toHaveBeenCalledWith('position', 'hr')
    expect(mockFetchEntityMetadata).toHaveBeenCalledWith('employee', 'hr')
    expect(mockFetchEntityMetadata).toHaveBeenCalledWith('company', 'hr')
    
    // 验证缓存已设置
    expect(getEntityMetadataSync('department', 'hr')).toBeDefined()
    expect(getEntityMetadataSync('position', 'hr')).toBeDefined()
    expect(getEntityMetadataSync('employee', 'hr')).toBeDefined()
    expect(getEntityMetadataSync('company', 'hr')).toBeDefined()
  })
})
```

## 2. 集成测试

创建 `src/lib/relation/__tests__/nested-relation-integration.test.ts` 文件，测试多级关联数据查询的完整流程：

```typescript
import { describe, it, expect, beforeAll } from 'vitest'
import { QueryGenerator } from '@/lib/graphql/query-generator'
import { processEntityData } from '@/lib/relation/processor'
import { preloadRelationMetadata } from '@/features/metadata/api/metadataApi'
import { employeeMetadata } from '@/features/metadata/mock/relation-test-metadata'
import { mockEmployeeDetailData } from '@/features/metadata/mock/relation-test-data'

describe('多级关联数据查询 - 集成测试', () => {
  // 在所有测试前预加载元数据
  beforeAll(async () => {
    await preloadRelationMetadata(employeeMetadata, { userRole: 'admin', mode: 'view' }, 3)
  })

  it('应该完成多级关联数据查询的完整流程', () => {
    // 1. 生成查询
    const query = QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      3
    )
    
    // 验证查询包含多级关联
    expect(query).toContain('department_info {')
    expect(query).toContain('parent_department {')
    expect(query).toContain('company_info {')
    
    // 2. 处理查询结果
    const processedData = processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      3
    )
    
    // 验证处理结果包含多级关联数据
    expect(processedData.department_id_data).toBeDefined()
    expect(processedData.department_id_data.parent_id_data).toBeDefined()
    expect(processedData.department_id_data.company_id_data).toBeDefined()
  })
})
```

## 3. 性能测试

创建 `src/lib/relation/__tests__/nested-relation-performance.test.ts` 文件，测试多级关联数据查询的性能：

```typescript
import { describe, it, expect, beforeAll } from 'vitest'
import { QueryGenerator } from '@/lib/graphql/query-generator'
import { processEntityData } from '@/lib/relation/processor'
import { preloadRelationMetadata } from '@/features/metadata/api/metadataApi'
import { employeeMetadata } from '@/features/metadata/mock/relation-test-metadata'
import { mockEmployeeDetailData } from '@/features/metadata/mock/relation-test-data'

describe('多级关联数据查询 - 性能测试', () => {
  // 在所有测试前预加载元数据
  beforeAll(async () => {
    await preloadRelationMetadata(employeeMetadata, { userRole: 'admin', mode: 'view' }, 3)
  })

  it('应该在合理时间内生成多级关联查询', () => {
    const startTime = performance.now()
    
    // 生成查询
    QueryGenerator.generateDetailQuery(
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      true,
      3
    )
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    // 验证生成时间在合理范围内（例如小于50ms）
    expect(duration).toBeLessThan(50)
  })

  it('应该在合理时间内处理多级关联数据', () => {
    const startTime = performance.now()
    
    // 处理数据
    processEntityData(
      mockEmployeeDetailData,
      employeeMetadata,
      { userRole: 'admin', mode: 'view' },
      3
    )
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    // 验证处理时间在合理范围内（例如小于50ms）
    expect(duration).toBeLessThan(50)
  })

  it('应该在合理时间内预加载关联元数据', async () => {
    const startTime = performance.now()
    
    // 预加载元数据
    await preloadRelationMetadata(employeeMetadata, { userRole: 'admin', mode: 'view' }, 3)
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    // 验证预加载时间在合理范围内（例如小于200ms）
    expect(duration).toBeLessThan(200)
  })
})
```
