# 字段关联实体配置重构示例代码

## 1. 类型定义

### 1.1 关联实体类型定义 (src/features/metadata/types/relation.ts)

```typescript
/**
 * 实体关联信息
 */
export interface EntityRelation {
  field: string;             // 关联字段名
  entity: string;            // 关联实体名
  module?: string;           // 关联实体所属模块
  type: 'one' | 'many';      // 关联类型：一对一或一对多
  foreignField?: string;     // 外键字段，默认为_id
  displayFields?: string[];  // 显示字段列表
  alias?: string;            // 关联数据别名，默认为 ${field}_relation
}
```

### 1.2 扩展实体元数据接口 (src/features/metadata/types/index.ts)

```typescript
import { EntityRelation } from './relation';

// 现有接口...

/**
 * 关联配置
 */
export interface RelationConfig {
  entity: string;              // 关联实体名称
  module?: string;             // 关联实体所属模块，默认为meta
  valueField?: string;         // 值字段，默认为_id
  labelField?: string;         // 标签字段，默认为name
  displayFields?: string[];    // 显示字段列表
  multiple?: boolean;          // 是否多选
  relationField?: string;      // 关联数据字段名称，默认为 ${field.name}_relation
  cascade?: {                  // 级联配置
    parent?: string;           // 父级字段
    children?: string;         // 子级字段
  };
  filter?: {                   // 过滤条件
    condition?: string;        // 条件表达式
    dependsOn?: string[];      // 依赖字段
  };
  searchable?: boolean;        // 是否可搜索
  createable?: boolean;        // 是否可创建
  previewable?: boolean;       // 是否可预览
}

/**
 * 实体元数据
 */
export interface EntityMetadata {
  name: string;
  display_name: string;
  module: string;
  description: string | null;
  fields: EntityField[];
  _id: string;
  
  // 新增字段
  relations?: EntityRelation[]; // 实体关联信息
}
```

## 2. 关联信息处理

### 2.1 关联信息提取器 (src/lib/relation/extractor.ts)

```typescript
import { EntityMetadata, EntityField } from '@/features/metadata/types';
import { EntityRelation } from '@/features/metadata/types/relation';

/**
 * 从实体元数据中提取关联信息
 * @param metadata 实体元数据
 * @returns 关联信息数组
 */
export const extractRelations = (metadata: EntityMetadata): EntityRelation[] => {
  // 如果元数据已经包含关联信息，直接返回
  if (metadata.relations) {
    return metadata.relations;
  }
  
  const relations: EntityRelation[] = [];

  metadata.fields.forEach(field => {
    if (field.control?.relation) {
      const relation = field.control.relation;
      const alias = relation.relationField || `${field.name}_relation`;

      relations.push({
        field: field.name,
        entity: relation.entity,
        module: relation.module || 'meta',
        type: relation.multiple ? 'many' : 'one',
        foreignField: relation.valueField || '_id',
        displayFields: relation.displayFields || [relation.labelField || 'name'],
        alias
      });
    }
  });

  return relations;
}
```

### 2.2 关联数据处理器 (src/lib/relation/processor.ts)

```typescript
import { EntityMetadata } from '@/features/metadata/types';
import { extractRelations } from './extractor';

/**
 * 处理实体数据，合并关联数据
 * @param data 原始数据
 * @param metadata 实体元数据
 * @returns 处理后的数据
 */
export const processEntityData = (
  data: any,
  metadata: EntityMetadata
): any => {
  if (!data) return data;
  
  // 提取关联信息
  const relations = extractRelations(metadata);
  
  // 如果没有关联信息，直接返回原始数据
  if (relations.length === 0) return data;
  
  // 处理关联数据
  const result = { ...data };
  
  relations.forEach(relation => {
    const { field, alias } = relation;
    const relationData = data[alias];
    
    if (relationData) {
      // 将关联数据合并到原始数据中
      result[`${field}_data`] = relationData;
    }
  });
  
  return result;
}

/**
 * 处理实体列表数据，合并关联数据
 * @param data 原始数据
 * @param metadata 实体元数据
 * @returns 处理后的数据
 */
export const processEntityListData = (
  data: any[],
  metadata: EntityMetadata
): any[] => {
  if (!data || !Array.isArray(data)) return data;
  
  return data.map(item => processEntityData(item, metadata));
}
```

## 3. 查询生成器扩展

### 3.1 扩展查询生成器 (src/lib/graphql/query-generator.ts)

```typescript
import { EntityField, EntityMetadata } from '@/features/metadata/types';
import { extractRelations } from '@/lib/relation/extractor';

export class QueryGenerator {
  /**
   * 生成实体详情查询
   * @param entityMetadata 实体元数据
   * @param includeRelations 是否包含关联数据
   * @returns GraphQL查询字符串
   */
  static generateDetailQuery(
    entityMetadata: EntityMetadata,
    includeRelations: boolean = true
  ): string {
    const { name, fields } = entityMetadata;
    const fieldNames = this.generateFieldsSelection(fields);
    
    // 如果不包含关联数据，直接返回基本查询
    if (!includeRelations) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail(_id: $_id) {
            ${fieldNames}
          }
        }
      `;
    }
    
    // 提取关联信息
    const relations = extractRelations(entityMetadata);
    
    // 如果没有关联信息，直接返回基本查询
    if (relations.length === 0) {
      return `
        query Get${name}Detail($_id: ID!) {
          ${name}_detail(_id: $_id) {
            ${fieldNames}
          }
        }
      `;
    }
    
    // 关联字段查询
    const relationQueries = relations.map(relation => {
      const displayFields = relation.displayFields?.join('\n        ') || '_id';
      
      return `
      ${relation.alias} {
        ${displayFields}
      }`;
    }).join('');
    
    return `
      query Get${name}Detail($_id: ID!) {
        ${name}_detail($_id: $_id) {
          ${fieldNames}
          ${relationQueries}
        }
      }
    `;
  }
  
  /**
   * 生成实体分页查询
   * @param entityMetadata 实体元数据
   * @param includeRelations 是否包含关联数据
   * @returns GraphQL查询字符串
   */
  static generatePageQuery(
    entityMetadata: EntityMetadata,
    includeRelations: boolean = true
  ): string {
    const { name, fields } = entityMetadata;
    const fieldNames = this.generateFieldsSelection(fields);
    
    // 如果不包含关联数据，直接返回基本查询
    if (!includeRelations) {
      return `
        query Get${name}Page($condition: JSON, $page: Int, $page_size: Int) {
          ${name}_page(condition: $condition, page: $page, page_size: $page_size) {
            items {
              ${fieldNames}
            }
            has_more
            page
            page_size
            total_page
            total_size
          }
        }
      `;
    }
    
    // 提取关联信息
    const relations = extractRelations(entityMetadata);
    
    // 如果没有关联信息，直接返回基本查询
    if (relations.length === 0) {
      return `
        query Get${name}Page($condition: JSON, $page: Int, $page_size: Int) {
          ${name}_page(condition: $condition, page: $page, page_size: $page_size) {
            items {
              ${fieldNames}
            }
            has_more
            page
            page_size
            total_page
            total_size
          }
        }
      `;
    }
    
    // 关联字段查询
    const relationQueries = relations.map(relation => {
      const displayFields = relation.displayFields?.join('\n          ') || '_id';
      
      return `
        ${relation.alias} {
          ${displayFields}
        }`;
    }).join('');
    
    return `
      query Get${name}Page($condition: JSON, $page: Int, $page_size: Int) {
        ${name}_page(condition: $condition, page: $page, page_size: $page_size) {
          items {
            ${fieldNames}
            ${relationQueries}
          }
          has_more
          page
          page_size
          total_page
          total_size
        }
      }
    `;
  }
  
  // 其他方法...
}
```
