# 更新日志 (Changelog)

## v0.3.3

### 功能特性

- **关联选择功能完善**
    - 实现关联数据缓存机制，优化查询性能
    - 实现条件过滤功能，支持依赖字段变化时更新查询条件
    - 优化关联选择组件，提供更友好的用户界面和交互体验
    - 实现依赖字段提示和禁用功能，提高用户体验
    - 支持批量查询关联实体详情，减少请求次数
    - 支持多级关联数据查询，如员工关联部门，部门关联公司

### 代码优化

- 实现 `buildRelationFilter` 函数，根据 `filter` 配置构建查询条件
- 实现 `useRelationDependency` Hook，监听依赖字段变化，更新查询条件
- 扩展 `RelationDataCache` 类，添加对实体详情的缓存支持
- 优化 `fetchRelationData`、`fetchRelationDetail` 和 `fetchRelationBatch` 函数
- 修复 ESLint 错误，将 `require()` 风格的导入改为 ES 模块导入风格
- 扩展 `EntityRelation` 接口，添加 `nestedRelations` 和 `maxDepth` 字段
- 实现元数据缓存机制，支持同步获取元数据
- 扩展 `QueryGenerator` 类，支持生成包含多级关联的查询
- 扩展 `processEntityData` 函数，支持处理多级关联数据
- 实现循环引用检测和深度控制机制

### 文档

- 添加关联选择功能完善需求分析文档
- 添加关联选择功能完善实施计划文档
- 添加关联选择功能测试数据文档
- 添加关联选择功能完善迭代总结文档
- 添加多级关联数据查询需求分析文档
- 添加多级关联数据查询实施计划文档
- 添加多级关联数据查询示例实现文档
- 添加多级关联数据查询测试计划文档
- 添加多级关联数据查询实现总结文档
- 更新变更日志和待办事项文档

## v0.3.2

### 功能特性

- **表单分组折叠功能**
    - 为表单分组添加折叠功能，允许用户折叠/展开分组
    - 添加折叠/展开图标，提供视觉反馈
    - 实现全局折叠控制，支持一键展开/折叠所有分组
    - 默认只展开第一个分组，其他分组折叠，减少长表单的视觉复杂度
    - 保持分组标题始终可见，点击标题可以折叠/展开分组内容

### 代码优化

- 修改 FieldGroup 组件，使用 Shadcn UI 的 Collapsible 组件实现折叠功能
- 修改 EntityForm 组件，添加分组折叠状态管理和全局折叠控制
- 更新示例组件，展示分组折叠功能的使用方法
- 优化折叠/展开动画，提供平滑的视觉反馈

### 文档

- 添加表单分组折叠功能实现总结文档
- 更新变更日志和待办事项文档

## v0.3.1

### 功能特性

- **表单分组功能**
    - 实现表单分组功能，支持通过 `control.layout.group` 属性将表单字段分组展示
    - 创建 FieldGroup 组件，使用 Card 组件将同一组的字段包装在一起
    - 实现分组内字段排序，通过 `control.layout.order` 属性指定字段在分组内的顺序
    - 支持未指定分组的字段自动归入默认分组
    - 支持分组顺序控制，默认分组始终显示在最前面

### 代码优化

- 实现 groupFields 函数，将字段按照分组进行分类
- 实现 getGroupOrder 函数，获取分组顺序
- 修改 EntityForm 组件，使用分组逻辑渲染字段
- 添加单元测试，验证分组功能的正确性

### 文档

- 添加表单分组功能需求分析文档
- 添加表单分组功能实现计划文档
- 添加表单分组功能实现总结文档
- 添加表单分组测试数据示例文档

## v0.3.0

### 功能特性

- **字段关联实体配置重构**
    - 实现以 `EntityMetadata.relations` 为核心的关联实体配置
    - 支持同一字段多个关联配置，根据条件判断显示
    - 支持自定义关联数据字段名
    - 支持服务端 GraphQL 引擎动态获取关联数据
    - 实现关联选择组件和关联多选组件
    - 添加关联实体配置示例页面

### 代码优化

- 重构关联条件判断逻辑，支持条件优先级
- 扩展查询生成器，支持关联数据查询
- 优化关联数据处理功能
- 添加详细的测试用例，验证功能正确性

### 文档

- 添加字段关联实体配置重构分析文档
- 添加字段关联实体配置重构实现计划文档
- 添加字段关联实体配置示例代码文档
- 添加字段关联实体配置测试数据示例文档

## v0.2.1

### 功能特性

- 实现实体表单关联选择功能
    - 添加关联数据缓存机制，减少重复请求
    - 实现关联数据查询API，支持条件查询、分页和缓存
    - 实现关联选择单选和多选控件，支持搜索、分页和预览
    - 添加关联选择示例页面和测试用例

### 修复的问题

- 修复关联选择控件中的属性传递问题
- 修复测试环境中的TypeScript类型错误
- 优化模拟GraphQL客户端，支持关联选择示例数据

## v0.2.0

### 功能特性

- **GraphQL数据接口交互**
    - 实现基于GraphQL规范的数据接口交互
    - 支持按模块划分的GraphQL端点（如/meta/graphql）
    - 基于元数据信息生成GraphQL查询
    - 支持列表过滤条件的Condition类型

- **模块化GraphQL客户端**
    - 创建模块化GraphQL客户端，支持按模块划分的GraphQL端点
    - 实现模块切换功能，可以在不同模块之间切换
    - 扩展模拟GraphQL客户端，支持多模块数据

- **查询生成器**
    - 实现QueryGenerator类，基于EntityMetadata元数据生成GraphQL查询
    - 支持生成详情查询、列表查询、分页查询、插入变更、更新变更和删除变更

- **条件构建器**
    - 实现GraphQLConditionBuilder类，用于构建GraphQL查询条件
    - 支持基本条件、AND条件组、OR条件组、NOT条件组和快速过滤条件

### 代码优化

- 扩展数据访问层，增加模块支持和元数据支持
- 扩展元数据API，增加模块支持
- 优化类型定义，确保类型安全
- 添加详细的测试用例，提高代码质量

### 文档

- 添加GraphQL数据接口交互需求分析文档
- 添加GraphQL数据接口交互系统分析文档
- 添加GraphQL模块示例组件

### 修复的问题

- 修复GraphQL客户端类型约束问题
- 修复模拟客户端中的类型错误

## v0.1.9

### 功能改进

- 实现了表单滚动功能，当字段数量太多时表单支持滚动
- 添加了 product_order 的 mock 数据用于测试
- 改进了 EntityList 组件对复杂类型（如数组和对象）的处理

### 问题修复

- 修复了联动引擎中的错误处理问题，解决了 `Cannot read properties of undefined (reading 'forEach')` 错误
- 修复了 default_value 为 null 时联动功能无法正常工作的问题
- 修复了字段被隐藏时显示空白占位的问题

### 代码优化

- 创建了新的 FieldContainer 组件，优化了字段的可见性控制
- 增强了 ExpressionEngine 类的表达式评估功能，特别是对 null 和 undefined 值的处理
- 改进了 LinkageEngine 类的条件评估和映射处理逻辑
- 添加了更多的调试日志，帮助排查问题

## 待实现功能

- 环境变量配置（GraphQL端点等）
- GraphQL请求拦截器（添加认证信息等）
- GraphQL响应拦截器（错误处理等）
- 元数据缓存机制
- 实体列表页面的高级筛选功能
- 实体表单的文件上传功能
- 实体表单的富文本编辑功能
- 实体表单的关联选择功能 ✓
- 实体表单的分组功能 ✓
- 实体表单的多语言支持
- 实体表单的权限控制
- 实体表单的工作流支持
- 实体表单的版本控制
- 实体表单的审计日志
