# 低代码平台数据模型设计

## 1. 概述

本文档详细描述低代码平台的核心数据模型，包括元数据管理、表单设计、页面设计、工作流设计等模块的数据结构。

## 2. 元数据管理模块

### 2.1 实体定义 (def_entity)

| 字段名          | 类型       | 描述     | 备注             |
|--------------|----------|--------|----------------|
| _id          | VARCHAR  | 主键     | 雪花ID           |
| name         | VARCHAR  | 实体编码   | 唯一标识           |
| module       | VARCHAR  | 所属模块   | 如 base, meta 等 |
| display_name | VARCHAR  | 显示名称   | 用于UI展示         |
| description  | VARCHAR  | 描述     | 详细说明           |
| icon         | VARCHAR  | 图标     | 图标名称或URL       |
| color        | VARCHAR  | 颜色     | 十六进制颜色值        |
| group        | VARCHAR  | 分组     | 实体分组           |
| is_system    | BOOLEAN  | 是否系统实体 | 系统实体不可删除       |
| _create_by   | VARCHAR  | 创建人    |                |
| _create_at   | DATETIME | 创建时间   |                |
| _update_by   | VARCHAR  | 更新人    |                |
| _update_at   | DATETIME | 更新时间   |                |

### 2.2 字段定义 (def_field)

| 字段名           | 类型       | 描述     | 备注                      |
|---------------|----------|--------|-------------------------|
| _id           | VARCHAR  | 主键     | 雪花ID                    |
| entity_id     | VARCHAR  | 所属实体ID | 外键                      |
| name          | VARCHAR  | 字段编码   | 唯一标识                    |
| display_name  | VARCHAR  | 显示名称   | 用于UI展示                  |
| type          | VARCHAR  | 数据类型   | VARCHAR, NUMBER, DATE 等 |
| default_value | VARCHAR  | 默认值    |                         |
| is_required   | BOOLEAN  | 是否必填   |                         |
| is_unique     | BOOLEAN  | 是否唯一   |                         |
| min_length    | INTEGER  | 最小长度   | 文本类型适用                  |
| max_length    | INTEGER  | 最大长度   | 文本类型适用                  |
| min_value     | DECIMAL  | 最小值    | 数值类型适用                  |
| max_value     | DECIMAL  | 最大值    | 数值类型适用                  |
| pattern       | VARCHAR  | 正则表达式  | 验证规则                    |
| format        | VARCHAR  | 格式化规则  | 如日期格式                   |
| help_text     | VARCHAR  | 帮助文本   | 提示信息                    |
| placeholder   | VARCHAR  | 占位文本   | 输入框占位符                  |
| is_system     | BOOLEAN  | 是否系统字段 | 系统字段不可删除                |
| _create_by    | VARCHAR  | 创建人    |                         |
| _create_at    | DATETIME | 创建时间   |                         |
| _update_by    | VARCHAR  | 更新人    |                         |
| _update_at    | DATETIME | 更新时间   |                         |

### 2.3 实体关系 (def_relation)

| 字段名              | 类型       | 描述     | 备注                                    |
|------------------|----------|--------|---------------------------------------|
| _id              | VARCHAR  | 主键     | 雪花ID                                  |
| name             | VARCHAR  | 关系名称   | 唯一标识                                  |
| source_entity_id | VARCHAR  | 源实体ID  | 外键                                    |
| target_entity_id | VARCHAR  | 目标实体ID | 外键                                    |
| relation_type    | VARCHAR  | 关系类型   | ONE_TO_ONE, ONE_TO_MANY, MANY_TO_MANY |
| source_field_id  | VARCHAR  | 源字段ID  | 外键                                    |
| target_field_id  | VARCHAR  | 目标字段ID | 外键                                    |
| junction_table   | VARCHAR  | 中间表名   | 多对多关系适用                               |
| cascade_delete   | BOOLEAN  | 是否级联删除 |                                       |
| _create_by       | VARCHAR  | 创建人    |                                       |
| _create_at       | DATETIME | 创建时间   |                                       |
| _update_by       | VARCHAR  | 更新人    |                                       |
| _update_at       | DATETIME | 更新时间   |                                       |

### 2.4 字段选项 (def_option)

| 字段名        | 类型       | 描述     | 备注       |
|------------|----------|--------|----------|
| _id        | VARCHAR  | 主键     | 雪花ID     |
| field_id   | VARCHAR  | 所属字段ID | 外键       |
| label      | VARCHAR  | 选项标签   | 显示文本     |
| value      | VARCHAR  | 选项值    | 实际值      |
| order      | INTEGER  | 排序     |          |
| color      | VARCHAR  | 颜色     | 十六进制颜色值  |
| icon       | VARCHAR  | 图标     | 图标名称或URL |
| _create_by | VARCHAR  | 创建人    |          |
| _create_at | DATETIME | 创建时间   |          |
| _update_by | VARCHAR  | 更新人    |          |
| _update_at | DATETIME | 更新时间   |          |

## 3. 表单设计模块

### 3.1 表单定义 (form_def)

| 字段名                | 类型       | 描述     | 备注                     |
|--------------------|----------|--------|------------------------|
| _id                | VARCHAR  | 主键     | 雪花ID                   |
| name               | VARCHAR  | 表单编码   | 唯一标识                   |
| display_name       | VARCHAR  | 显示名称   | 用于UI展示                 |
| description        | VARCHAR  | 描述     | 详细说明                   |
| entity_id          | VARCHAR  | 关联实体ID | 外键                     |
| version            | INTEGER  | 版本号    |                        |
| status             | VARCHAR  | 状态     | DRAFT, PUBLISHED       |
| layout_type        | VARCHAR  | 布局类型   | TABS, SECTIONS, CUSTOM |
| submit_button_text | VARCHAR  | 提交按钮文本 |                        |
| cancel_button_text | VARCHAR  | 取消按钮文本 |                        |
| success_message    | VARCHAR  | 成功消息   |                        |
| _create_by         | VARCHAR  | 创建人    |                        |
| _create_at         | DATETIME | 创建时间   |                        |
| _update_by         | VARCHAR  | 更新人    |                        |
| _update_at         | DATETIME | 更新时间   |                        |

### 3.2 表单字段 (form_field)

| 字段名              | 类型       | 描述      | 备注                           |
|------------------|----------|---------|------------------------------|
| _id              | VARCHAR  | 主键      | 雪花ID                         |
| form_id          | VARCHAR  | 所属表单ID  | 外键                           |
| field_id         | VARCHAR  | 关联字段ID  | 外键，可为空（自定义字段）                |
| name             | VARCHAR  | 字段编码    | 唯一标识                         |
| display_name     | VARCHAR  | 显示名称    | 用于UI展示                       |
| control_type     | VARCHAR  | 控件类型    | TEXT, NUMBER, DATE, SELECT 等 |
| section_id       | VARCHAR  | 所属分区ID  | 外键                           |
| tab_id           | VARCHAR  | 所属选项卡ID | 外键                           |
| order            | INTEGER  | 排序      |                              |
| width            | VARCHAR  | 宽度      | 百分比或像素                       |
| is_visible       | BOOLEAN  | 是否可见    |                              |
| is_editable      | BOOLEAN  | 是否可编辑   |                              |
| is_required      | BOOLEAN  | 是否必填    |                              |
| default_value    | VARCHAR  | 默认值     |                              |
| help_text        | VARCHAR  | 帮助文本    | 提示信息                         |
| placeholder      | VARCHAR  | 占位文本    | 输入框占位符                       |
| validation_rules | JSON     | 验证规则    | JSON格式                       |
| _create_by       | VARCHAR  | 创建人     |                              |
| _create_at       | DATETIME | 创建时间    |                              |
| _update_by       | VARCHAR  | 更新人     |                              |
| _update_at       | DATETIME | 更新时间    |                              |

### 3.3 表单布局 (form_layout)

| 字段名          | 类型       | 描述     | 备注                        |
|--------------|----------|--------|---------------------------|
| _id          | VARCHAR  | 主键     | 雪花ID                      |
| form_id      | VARCHAR  | 所属表单ID | 外键                        |
| type         | VARCHAR  | 布局类型   | SECTION, TAB, ROW, COLUMN |
| parent_id    | VARCHAR  | 父级布局ID | 外键                        |
| name         | VARCHAR  | 布局编码   |                           |
| display_name | VARCHAR  | 显示名称   | 用于UI展示                    |
| order        | INTEGER  | 排序     |                           |
| width        | VARCHAR  | 宽度     | 百分比或像素                    |
| height       | VARCHAR  | 高度     | 百分比或像素                    |
| css_class    | VARCHAR  | CSS类名  |                           |
| css_style    | VARCHAR  | CSS样式  |                           |
| _create_by   | VARCHAR  | 创建人    |                           |
| _create_at   | DATETIME | 创建时间   |                           |
| _update_by   | VARCHAR  | 更新人    |                           |
| _update_at   | DATETIME | 更新时间   |                           |

## 4. 页面设计模块

### 4.1 页面定义 (page_def)

| 字段名          | 类型       | 描述   | 备注                        |
|--------------|----------|------|---------------------------|
| _id          | VARCHAR  | 主键   | 雪花ID                      |
| name         | VARCHAR  | 页面编码 | 唯一标识                      |
| display_name | VARCHAR  | 显示名称 | 用于UI展示                    |
| description  | VARCHAR  | 描述   | 详细说明                      |
| type         | VARCHAR  | 页面类型 | LIST, DETAIL, DASHBOARD 等 |
| version      | INTEGER  | 版本号  |                           |
| status       | VARCHAR  | 状态   | DRAFT, PUBLISHED          |
| route_path   | VARCHAR  | 路由路径 |                           |
| icon         | VARCHAR  | 图标   | 图标名称或URL                  |
| is_home      | BOOLEAN  | 是否首页 |                           |
| _create_by   | VARCHAR  | 创建人  |                           |
| _create_at   | DATETIME | 创建时间 |                           |
| _update_by   | VARCHAR  | 更新人  |                           |
| _update_at   | DATETIME | 更新时间 |                           |

### 4.2 页面组件 (page_component)

| 字段名            | 类型       | 描述     | 备注                              |
|----------------|----------|--------|---------------------------------|
| _id            | VARCHAR  | 主键     | 雪花ID                            |
| page_id        | VARCHAR  | 所属页面ID | 外键                              |
| parent_id      | VARCHAR  | 父级组件ID | 外键                              |
| component_type | VARCHAR  | 组件类型   | CONTAINER, CARD, TABLE, CHART 等 |
| name           | VARCHAR  | 组件编码   |                                 |
| display_name   | VARCHAR  | 显示名称   | 用于UI展示                          |
| order          | INTEGER  | 排序     |                                 |
| x              | INTEGER  | X坐标    | 绝对布局适用                          |
| y              | INTEGER  | Y坐标    | 绝对布局适用                          |
| width          | VARCHAR  | 宽度     | 百分比或像素                          |
| height         | VARCHAR  | 高度     | 百分比或像素                          |
| data_source    | JSON     | 数据源    | JSON格式                          |
| properties     | JSON     | 属性     | JSON格式                          |
| events         | JSON     | 事件     | JSON格式                          |
| css_class      | VARCHAR  | CSS类名  |                                 |
| css_style      | VARCHAR  | CSS样式  |                                 |
| _create_by     | VARCHAR  | 创建人    |                                 |
| _create_at     | DATETIME | 创建时间   |                                 |
| _update_by     | VARCHAR  | 更新人    |                                 |
| _update_at     | DATETIME | 更新时间   |                                 |

## 5. 工作流设计模块

### 5.1 工作流定义 (flow_def)

| 字段名           | 类型       | 描述     | 备注               |
|---------------|----------|--------|------------------|
| _id           | VARCHAR  | 主键     | 雪花ID             |
| name          | VARCHAR  | 工作流编码  | 唯一标识             |
| display_name  | VARCHAR  | 显示名称   | 用于UI展示           |
| description   | VARCHAR  | 描述     | 详细说明             |
| entity_id     | VARCHAR  | 关联实体ID | 外键               |
| version       | INTEGER  | 版本号    |                  |
| status        | VARCHAR  | 状态     | DRAFT, PUBLISHED |
| start_node_id | VARCHAR  | 开始节点ID | 外键               |
| _create_by    | VARCHAR  | 创建人    |                  |
| _create_at    | DATETIME | 创建时间   |                  |
| _update_by    | VARCHAR  | 更新人    |                  |
| _update_at    | DATETIME | 更新时间   |                  |

### 5.2 工作流节点 (flow_node)

| 字段名            | 类型       | 描述      | 备注                                |
|----------------|----------|---------|-----------------------------------|
| _id            | VARCHAR  | 主键      | 雪花ID                              |
| flow_id        | VARCHAR  | 所属工作流ID | 外键                                |
| name           | VARCHAR  | 节点编码    |                                   |
| display_name   | VARCHAR  | 显示名称    | 用于UI展示                            |
| node_type      | VARCHAR  | 节点类型    | START, END, TASK, GATEWAY 等       |
| form_id        | VARCHAR  | 关联表单ID  | 外键                                |
| assignee_type  | VARCHAR  | 分配类型    | USER, ROLE, EXPRESSION            |
| assignee       | VARCHAR  | 分配对象    | 用户ID、角色ID或表达式                     |
| timeout        | INTEGER  | 超时时间    | 秒                                 |
| timeout_action | VARCHAR  | 超时动作    | NOTIFY, AUTO_APPROVE, AUTO_REJECT |
| x              | INTEGER  | X坐标     | 设计器中的位置                           |
| y              | INTEGER  | Y坐标     | 设计器中的位置                           |
| properties     | JSON     | 属性      | JSON格式                            |
| _create_by     | VARCHAR  | 创建人     |                                   |
| _create_at     | DATETIME | 创建时间    |                                   |
| _update_by     | VARCHAR  | 更新人     |                                   |
| _update_at     | DATETIME | 更新时间    |                                   |

### 5.3 工作流转换 (flow_transition)

| 字段名            | 类型       | 描述      | 备注     |
|----------------|----------|---------|--------|
| _id            | VARCHAR  | 主键      | 雪花ID   |
| flow_id        | VARCHAR  | 所属工作流ID | 外键     |
| source_node_id | VARCHAR  | 源节点ID   | 外键     |
| target_node_id | VARCHAR  | 目标节点ID  | 外键     |
| name           | VARCHAR  | 转换名称    |        |
| condition      | VARCHAR  | 条件表达式   |        |
| order          | INTEGER  | 优先级     | 条件评估顺序 |
| _create_by     | VARCHAR  | 创建人     |        |
| _create_at     | DATETIME | 创建时间    |        |
| _update_by     | VARCHAR  | 更新人     |        |
| _update_at     | DATETIME | 更新时间    |        |

## 6. 应用管理模块

### 6.1 应用定义 (app_def)

| 字段名          | 类型       | 描述   | 备注               |
|--------------|----------|------|------------------|
| _id          | VARCHAR  | 主键   | 雪花ID             |
| name         | VARCHAR  | 应用编码 | 唯一标识             |
| display_name | VARCHAR  | 显示名称 | 用于UI展示           |
| description  | VARCHAR  | 描述   | 详细说明             |
| icon         | VARCHAR  | 图标   | 图标名称或URL         |
| color        | VARCHAR  | 颜色   | 十六进制颜色值          |
| version      | VARCHAR  | 版本   |                  |
| status       | VARCHAR  | 状态   | DRAFT, PUBLISHED |
| home_page_id | VARCHAR  | 首页ID | 外键               |
| _create_by   | VARCHAR  | 创建人  |                  |
| _create_at   | DATETIME | 创建时间 |                  |
| _update_by   | VARCHAR  | 更新人  |                  |
| _update_at   | DATETIME | 更新时间 |                  |

### 6.2 应用模块 (app_module)

| 字段名          | 类型       | 描述     | 备注       |
|--------------|----------|--------|----------|
| _id          | VARCHAR  | 主键     | 雪花ID     |
| app_id       | VARCHAR  | 所属应用ID | 外键       |
| name         | VARCHAR  | 模块编码   |          |
| display_name | VARCHAR  | 显示名称   | 用于UI展示   |
| description  | VARCHAR  | 描述     | 详细说明     |
| icon         | VARCHAR  | 图标     | 图标名称或URL |
| order        | INTEGER  | 排序     |          |
| _create_by   | VARCHAR  | 创建人    |          |
| _create_at   | DATETIME | 创建时间   |          |
| _update_by   | VARCHAR  | 更新人    |          |
| _update_at   | DATETIME | 更新时间   |          |

### 6.3 应用菜单 (app_menu)

| 字段名          | 类型       | 描述     | 备注       |
|--------------|----------|--------|----------|
| _id          | VARCHAR  | 主键     | 雪花ID     |
| app_id       | VARCHAR  | 所属应用ID | 外键       |
| module_id    | VARCHAR  | 所属模块ID | 外键       |
| parent_id    | VARCHAR  | 父级菜单ID | 外键       |
| name         | VARCHAR  | 菜单编码   |          |
| display_name | VARCHAR  | 显示名称   | 用于UI展示   |
| icon         | VARCHAR  | 图标     | 图标名称或URL |
| route_path   | VARCHAR  | 路由路径   |          |
| page_id      | VARCHAR  | 关联页面ID | 外键       |
| order        | INTEGER  | 排序     |          |
| is_visible   | BOOLEAN  | 是否可见   |          |
| _create_by   | VARCHAR  | 创建人    |          |
| _create_at   | DATETIME | 创建时间   |          |
| _update_by   | VARCHAR  | 更新人    |          |
| _update_at   | DATETIME | 更新时间   |          |
