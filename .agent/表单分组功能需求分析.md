# 表单分组功能需求分析

## 1. 需求概述

实现表单控件的分组功能，支持通过 `control.layout.group` 属性将表单字段分组展示，提高表单的可读性和用户体验。

## 2. 现状分析

### 2.1 当前实现

目前的表单实现主要通过 `EntityForm` 组件渲染表单字段，使用 `FieldContainer` 包装每个字段，并通过 `EntityField`
组件渲染具体的表单控件。表单布局采用网格布局，支持通过 `control.layout.span` 和 `control.layout.order` 控制字段的宽度和顺序。

```tsx
// EntityForm.tsx 中的字段渲染逻辑
<div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
  {editableFields.map((field) => {
    // 判断字段是否应该占据整行
    const isFullWidth = field.type === 'TEXT' || 
                       field.type === 'JSON' || 
                       (field.control?.layout?.span === 2);

    // 使用 FieldContainer 组件包装字段，它会处理可见性
    return (
      <FieldContainer
        key={field._id}
        field={field}
        isFullWidth={isFullWidth}
      >
        <EntityField field={field} readOnly={readOnly} />
      </FieldContainer>
    )
  })}
</div>
```

### 2.2 现有类型定义

`FieldControl` 接口中已经包含了 `layout.group` 属性，但尚未在表单渲染中使用：

```typescript
export interface FieldControl {
  type?: string // 指定前端控件类型（覆盖默认映射）
  props?: Record<string, any> // 传递给控件的属性
  formatter?: string | { // 值格式化配置
    display?: string // 显示格式化（如日期格式）
    edit?: string // 编辑格式化
  }
  linkage?: LinkageRule[] // 字段联动规则
  layout?: { // 布局配置
    span?: number // 栅格跨度
    order?: number // 显示顺序
    group?: string // 分组名称
  }
  relation?: RelationConfig // 关联配置
}
```

## 3. 功能设计

### 3.1 分组方式

表单分组将采用以下两种方式之一实现：

1. **Card 分组**：使用 Card 组件将同一组的字段包装在一起，每个分组有独立的标题和内容区域。
2. **Tabs 分组**：使用 Tabs 组件将表单分为多个标签页，每个标签页对应一个分组。

考虑到表单的整体性和用户体验，推荐使用 Card 分组方式，这样用户可以一次性看到所有分组，而不需要切换标签页。

### 3.2 分组逻辑

1. 遍历所有字段，根据 `control.layout.group` 属性将字段分组
2. 对于没有指定分组的字段，可以放在默认分组中（如"基本信息"）
3. 按照分组顺序渲染各个分组，每个分组内部再按照字段的 `control.layout.order` 排序

### 3.3 分组样式

每个分组将使用 Card 组件包装，包含以下部分：

- CardHeader：显示分组标题
- CardContent：包含分组内的所有字段，保持原有的网格布局

## 4. 实现方案

### 4.1 组件结构

```
EntityForm
├── FieldGroup (新增)
│   ├── FieldGroupHeader
│   └── FieldGroupContent
│       └── FieldContainer
│           └── EntityField
└── 其他组件...
```

### 4.2 分组处理函数

新增一个函数用于处理字段分组：

```typescript
/**
 * 将字段按照分组进行分类
 * @param fields 字段列表
 * @returns 分组后的字段映射
 */
function groupFields(fields: EntityField[]): Record<string, EntityField[]> {
  const groups: Record<string, EntityField[]> = {};
  
  // 默认分组名称
  const defaultGroup = '基本信息';
  
  // 遍历所有字段，按照分组进行分类
  fields.forEach(field => {
    const groupName = field.control?.layout?.group || defaultGroup;
    
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    
    groups[groupName].push(field);
  });
  
  // 对每个分组内的字段按照 order 排序
  Object.keys(groups).forEach(groupName => {
    groups[groupName].sort((a, b) => {
      const orderA = a.control?.layout?.order || 0;
      const orderB = b.control?.layout?.order || 0;
      return orderA - orderB;
    });
  });
  
  return groups;
}
```

### 4.3 FieldGroup 组件

新增 FieldGroup 组件用于渲染分组：

```tsx
interface FieldGroupProps {
  title: string;
  fields: EntityField[];
  readOnly?: boolean;
}

function FieldGroup({ title, fields, readOnly = false }: FieldGroupProps) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          {fields.map((field) => {
            const isFullWidth = field.type === 'TEXT' || 
                               field.type === 'JSON' || 
                               (field.control?.layout?.span === 2);
            
            return (
              <FieldContainer
                key={field._id}
                field={field}
                isFullWidth={isFullWidth}
              >
                <EntityField field={field} readOnly={readOnly} />
              </FieldContainer>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 4.4 修改 EntityForm 组件

修改 EntityForm 组件，使用分组逻辑渲染字段：

```tsx
// 在 EntityForm 组件中
// 获取可编辑字段
const editableFields = getEditableFields(metadata);

// 将字段按照分组进行分类
const fieldGroups = groupFields(editableFields);

return (
  <OptionsProvider>
    <Form {...form}>
      <LinkageProvider
        fields={editableFields}
        formState={{ ...formState, mode: isEdit ? 'edit' : 'create' }}
        userRole={userRole}
        config={config}
        context={context}
      >
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6 pb-2'>
          {/* 渲染各个分组 */}
          {Object.entries(fieldGroups).map(([groupName, fields]) => (
            <FieldGroup
              key={groupName}
              title={groupName}
              fields={fields}
              readOnly={readOnly}
            />
          ))}
        </form>
      </LinkageProvider>
    </Form>
  </OptionsProvider>
);
```

## 5. 测试方案

### 5.1 测试用例

1. **基本分组测试**：创建包含多个分组的表单，验证字段是否正确分组
2. **默认分组测试**：验证未指定分组的字段是否正确归入默认分组
3. **排序测试**：验证分组内字段是否按照 order 属性正确排序
4. **可见性测试**：验证字段联动可见性在分组环境下是否正常工作
5. **响应式测试**：验证不同屏幕尺寸下分组布局是否正确

### 5.2 测试数据

创建包含多个分组的测试实体元数据：

```typescript
const testEntityMetadata: EntityMetadata = {
  name: 'test_entity',
  display_name: '测试实体',
  module: 'test',
  description: '用于测试表单分组功能',
  fields: [
    // 基本信息组
    {
      name: 'name',
      display_name: '名称',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'name_id',
      control: {
        layout: {
          group: '基本信息',
          order: 1
        }
      }
    },
    // 详细信息组
    {
      name: 'description',
      display_name: '描述',
      type: 'TEXT',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'description_id',
      control: {
        layout: {
          group: '详细信息',
          order: 1,
          span: 2
        }
      }
    },
    // 未指定分组（应归入默认分组）
    {
      name: 'status',
      display_name: '状态',
      type: 'ENUM',
      default_value: 'active',
      flags: [],
      _id: 'status_id',
      options: {
        values: [
          { label: '活跃', value: 'active' },
          { label: '禁用', value: 'disabled' }
        ]
      }
    }
  ],
  _id: 'test_entity_id'
};
```

## 6. 验收标准

1. 表单字段可以通过 `control.layout.group` 属性进行分组
2. 未指定分组的字段应归入默认分组
3. 分组内的字段应按照 `control.layout.order` 属性排序
4. 分组应有清晰的视觉边界和标题
5. 分组布局应适应不同屏幕尺寸
6. 字段联动机制在分组环境下正常工作
