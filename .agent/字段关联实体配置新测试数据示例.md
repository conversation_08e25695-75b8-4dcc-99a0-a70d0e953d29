# 字段关联实体配置新测试数据示例

## 1. 测试实体元数据

### 1.1 员工实体元数据 (src/features/metadata/mock/relation-test-metadata.ts)

```typescript
import { EntityMetadata } from '../types';
import { EntityRelation } from '../types/relation';

/**
 * 测试关联实体元数据 - 员工
 */
export const employeeMetadata: EntityMetadata = {
  name: 'employee',
  display_name: '员工',
  module: 'hr',
  description: '员工信息',
  _id: 'employee_metadata_id',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'employee_id_field',
    },
    {
      name: 'code',
      display_name: '工号',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_code_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE'],
        defaultOperator: '__EQ',
      },
    },
    {
      name: 'name',
      display_name: '姓名',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_name_field',
      condition: {
        queryable: true,
        operators: ['__EQ', '__LIKE', '__L_LIKE', '__R_LIKE'],
        defaultOperator: '__LIKE',
      },
    },
    {
      name: 'department_id',
      display_name: '部门',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'employee_department_id_field',
      condition: {
        queryable: true,
        operators: ['__EQ'],
        defaultOperator: '__EQ',
      },
    },
    {
      name: 'position_id',
      display_name: '职位',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'employee_position_id_field',
    },
    {
      name: 'manager_id',
      display_name: '直属上级',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'employee_manager_id_field',
    },
    {
      name: 'join_date',
      display_name: '入职日期',
      type: 'DATE',
      default_value: null,
      flags: [],
      _id: 'employee_join_date_field',
    },
    {
      name: 'status',
      display_name: '状态',
      type: 'VARCHAR',
      default_value: 'active',
      flags: [],
      _id: 'employee_status_field',
      options: [
        { label: '在职', value: 'active' },
        { label: '离职', value: 'inactive' },
        { label: '休假', value: 'leave' },
      ],
    },
  ],
  // 关联信息
  relations: [
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'department_info', // 自定义关联数据字段名
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'manager_name'],
        searchable: true,
      },
    },
    {
      field: 'position_id',
      entity: 'position',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      // 未指定 alias，将使用默认命名 position_id_relation
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'level', 'department_id'],
        searchable: true,
      },
    },
    {
      field: 'manager_id',
      entity: 'employee', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager', // 自定义关联数据字段名
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_id'],
        searchable: true,
      },
    },
    // 根据角色显示不同的部门信息
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'department_detail', // 自定义关联数据字段名
      condition: {
        type: 'role',
        roles: ['admin', 'hr_manager'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'manager_name', 'description'],
        searchable: true,
      },
    },
    // 根据表单模式显示不同的上级信息
    {
      field: 'manager_id',
      entity: 'employee', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager_detail', // 自定义关联数据字段名
      condition: {
        type: 'mode',
        modes: ['edit', 'view'],
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_id', 'department_id', 'join_date'],
        searchable: true,
      },
    },
  ],
};

/**
 * 测试关联实体元数据 - 部门
 */
export const departmentMetadata: EntityMetadata = {
  name: 'department',
  display_name: '部门',
  module: 'hr',
  description: '部门信息',
  _id: 'department_metadata_id',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'department_id_field',
    },
    {
      name: 'code',
      display_name: '部门编码',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_code_field',
    },
    {
      name: 'name',
      display_name: '部门名称',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_name_field',
    },
    {
      name: 'parent_id',
      display_name: '上级部门',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_parent_id_field',
    },
    {
      name: 'manager_id',
      display_name: '部门负责人',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_manager_id_field',
    },
    {
      name: 'manager_name',
      display_name: '负责人姓名',
      type: 'VARCHAR',
      default_value: null,
      flags: ['VIRTUAL'],
      _id: 'department_manager_name_field',
    },
    {
      name: 'description',
      display_name: '部门描述',
      type: 'TEXT',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_description_field',
    },
  ],
  // 关联信息
  relations: [
    {
      field: 'parent_id',
      entity: 'department', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'parent_department', // 自定义关联数据字段名
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
    },
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager_info', // 自定义关联数据字段名
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
    },
    // 根据表达式条件显示不同的负责人信息
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager_detail', // 自定义关联数据字段名
      condition: {
        type: 'expression',
        expression: 'userRole === "admin" || userRole === "hr_manager"',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_id', 'department_id', 'join_date', 'status'],
        searchable: true,
      },
    },
  ],
};
```

## 2. 测试数据

### 2.1 员工测试数据 (src/features/metadata/mock/relation-test-data.ts)

```typescript
/**
 * 测试部门数据
 */
export const mockDepartmentData = [
  {
    _id: 'dept_001',
    code: 'D001',
    name: '研发部',
    parent_id: null,
    manager_id: 'emp_001',
    manager_name: '张三',
    description: '负责产品研发',
  },
  {
    _id: 'dept_002',
    code: 'D002',
    name: '市场部',
    parent_id: null,
    manager_id: 'emp_005',
    manager_name: '王五',
    description: '负责市场营销',
  },
  {
    _id: 'dept_003',
    code: 'D003',
    name: '前端组',
    parent_id: 'dept_001',
    manager_id: 'emp_002',
    manager_name: '李四',
    description: '负责前端开发',
  },
  {
    _id: 'dept_004',
    code: 'D004',
    name: '后端组',
    parent_id: 'dept_001',
    manager_id: 'emp_003',
    manager_name: '赵六',
    description: '负责后端开发',
  },
];

/**
 * 测试职位数据
 */
export const mockPositionData = [
  {
    _id: 'pos_001',
    name: '技术总监',
    level: 'P8',
    department_id: 'dept_001',
  },
  {
    _id: 'pos_002',
    name: '前端负责人',
    level: 'P6',
    department_id: 'dept_003',
  },
  {
    _id: 'pos_003',
    name: '后端负责人',
    level: 'P6',
    department_id: 'dept_004',
  },
  {
    _id: 'pos_004',
    name: '前端工程师',
    level: 'P5',
    department_id: 'dept_003',
  },
  {
    _id: 'pos_005',
    name: '后端工程师',
    level: 'P5',
    department_id: 'dept_004',
  },
  {
    _id: 'pos_006',
    name: '市场总监',
    level: 'P7',
    department_id: 'dept_002',
  },
];

/**
 * 测试员工数据
 */
export const mockEmployeeData = [
  {
    _id: 'emp_001',
    code: 'E001',
    name: '张三',
    department_id: 'dept_001',
    position_id: 'pos_001',
    manager_id: null,
    join_date: '2020-01-01',
    status: 'active',
  },
  {
    _id: 'emp_002',
    code: 'E002',
    name: '李四',
    department_id: 'dept_003',
    position_id: 'pos_002',
    manager_id: 'emp_001',
    join_date: '2020-02-01',
    status: 'active',
  },
  {
    _id: 'emp_003',
    code: 'E003',
    name: '赵六',
    department_id: 'dept_004',
    position_id: 'pos_003',
    manager_id: 'emp_001',
    join_date: '2020-03-01',
    status: 'active',
  },
  {
    _id: 'emp_004',
    code: 'E004',
    name: '钱七',
    department_id: 'dept_003',
    position_id: 'pos_004',
    manager_id: 'emp_002',
    join_date: '2021-01-01',
    status: 'active',
  },
  {
    _id: 'emp_005',
    code: 'E005',
    name: '王五',
    department_id: 'dept_002',
    position_id: 'pos_006',
    manager_id: null,
    join_date: '2020-01-15',
    status: 'active',
  },
];
```
