# 实体表单关联选择功能需求分析

## 1. 需求概述

在低代码平台的实体表单中，需要实现关联选择功能，使用户能够在表单中选择其他实体的记录作为当前实体的关联字段值。这种关联关系在数据库中通常表现为外键关系，在前端表单中需要提供友好的选择界面，支持搜索、筛选和预览关联实体的详细信息。

## 2. 业务场景

### 2.1 单实体关联

- **部门选择**：在用户表单中，选择用户所属的部门
- **上级主管**：在员工表单中，选择员工的上级主管
- **客户选择**：在订单表单中，选择关联的客户

### 2.2 多实体关联

- **产品选择**：在订单表单中，选择多个产品
- **角色分配**：在用户表单中，分配多个角色
- **标签关联**：在文章表单中，关联多个标签

### 2.3 级联关联

- **地区选择**：省份、城市、区县的级联选择
- **组织架构**：公司、部门、团队的级联选择
- **产品分类**：类别、子类别、具体产品的级联选择

## 3. 功能需求

### 3.1 基础功能

1. **关联字段定义**
    - 在实体元数据中定义关联字段，指定关联的目标实体
    - 支持单选和多选两种关联模式
    - 支持指定关联实体的显示字段和值字段

2. **关联选择控件**
    - 实现关联选择控件，支持下拉选择、弹窗选择等多种交互方式
    - 支持搜索和筛选功能，方便用户快速找到目标记录
    - 支持分页加载，处理大量数据的情况

3. **数据展示**
    - 在表单中显示关联实体的关键信息
    - 在详情页面中显示关联实体的详细信息
    - 支持自定义显示格式

### 3.2 高级功能

1. **级联选择**
    - 支持多级关联选择，如选择省份后联动显示对应的城市
    - 支持动态加载下级选项，减少初始加载数据量

2. **关联创建**
    - 在选择界面中提供"新建"按钮，允许用户直接创建新的关联实体
    - 支持快速创建模式，只填写必要字段

3. **关联预览**
    - 提供关联实体的预览功能，无需跳转即可查看关联实体的详细信息
    - 支持在预览中进行简单的编辑操作

4. **权限控制**
    - 根据用户权限控制可选择的关联实体范围
    - 支持数据权限过滤，只显示用户有权访问的数据

## 4. 交互需求

### 4.1 单选模式

1. **下拉选择**
    - 适用于选项较少的场景
    - 支持搜索过滤
    - 显示关联实体的关键信息

2. **弹窗选择**
    - 适用于选项较多的场景
    - 提供表格视图，显示多个字段
    - 支持搜索、筛选和排序
    - 提供分页功能

### 4.2 多选模式

1. **标签式多选**
    - 已选项以标签形式展示
    - 支持快速删除已选项
    - 显示已选数量

2. **表格式多选**
    - 在弹窗中以表格形式展示可选项
    - 支持批量选择和取消
    - 显示已选和可选状态

### 4.3 级联选择

1. **联动下拉框**
    - 多个下拉框联动
    - 上级选择影响下级选项
    - 支持默认值和清空操作

2. **树形选择**
    - 以树形结构展示层级关系
    - 支持展开/折叠节点
    - 支持搜索定位节点

## 5. 技术要求

### 5.1 元数据扩展

需要扩展EntityField元数据结构，增加关联字段的配置：

```typescript
interface RelationConfig {
  entity: string;              // 关联实体名称
  module?: string;             // 关联实体所属模块
  valueField?: string;         // 值字段，默认为_id
  labelField?: string;         // 标签字段，默认为name
  displayFields?: string[];    // 显示字段列表
  multiple?: boolean;          // 是否多选
  cascade?: {                  // 级联配置
    parent?: string;           // 父级字段
    children?: string;         // 子级字段
  };
  filter?: {                   // 过滤条件
    condition?: string;        // 条件表达式
    dependsOn?: string[];      // 依赖字段
  };
  searchable?: boolean;        // 是否可搜索
  createable?: boolean;        // 是否可创建
  previewable?: boolean;       // 是否可预览
}

// 扩展FieldControl接口
interface FieldControl {
  // 现有属性...
  relation?: RelationConfig;   // 关联配置
}
```

### 5.2 控件实现

1. **基础组件**
    - 实现RelationSelect组件，支持单选和多选
    - 实现RelationSelectModal组件，提供弹窗选择界面
    - 实现RelationTag组件，展示已选关联项

2. **数据获取**
    - 实现关联数据的查询和加载
    - 支持条件过滤和分页
    - 处理大数据量的性能问题

3. **缓存机制**
    - 缓存已加载的关联数据，减少重复请求
    - 实现数据更新机制，确保数据一致性

## 6. 验收标准

1. **功能完整性**
    - 实现单选、多选和级联选择功能
    - 支持搜索、筛选和分页
    - 提供关联创建和预览功能

2. **易用性**
    - 交互流畅，操作简单直观
    - 提供适当的提示和帮助信息
    - 支持键盘操作和快捷键

3. **性能要求**
    - 加载速度快，响应及时
    - 处理大数据量时不卡顿
    - 资源占用合理

4. **兼容性**
    - 兼容主流浏览器
    - 适配不同屏幕尺寸
    - 支持移动设备操作
