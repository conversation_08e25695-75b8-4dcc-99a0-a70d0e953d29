# 待办事项

## 当前迭代：关联选择功能完善 (v0.3.3)

### 1. 关联选择功能增强

- [x] 实现关联数据缓存机制，优化查询性能
- [x] 实现条件过滤功能，支持依赖字段变化时更新查询条件
- [x] 优化关联选择组件，提供更友好的用户界面和交互体验
- [x] 实现依赖字段提示和禁用功能，提高用户体验
- [x] 支持批量查询关联实体详情，减少请求次数
- [ ] 实现关联创建功能，允许在选择界面中创建新的关联实体
- [ ] 实现关联预览功能，无需跳转即可查看关联实体的详细信息
- [ ] 支持多级关联数据查询，如员工关联部门，部门关联公司

### 2. 性能优化

- [x] 实现关联数据的本地缓存
- [x] 实现关联数据的过期策略
- [ ] 优化查询结构，减少不必要的字段查询
- [ ] 实现查询合并，减少请求次数
- [ ] 实现关联数据的预加载

### 3. 用户体验提升

- [x] 优化加载状态展示，提供加载指示器
- [x] 优化错误处理，提供友好的错误提示
- [x] 实现空数据状态的展示
- [ ] 实现虚拟滚动，优化大数据量的展示性能
- [ ] 实现拖拽排序，支持多选项的排序
- [ ] 实现自定义渲染，支持自定义选项的展示方式

### 4. 条件过滤增强

- [x] 支持依赖字段值的动态获取和条件更新
- [x] 支持复杂条件的组合
- [ ] 支持组合条件（AND、OR、NOT）
- [ ] 支持时间条件
- [ ] 支持数值范围条件

### 5. 组件扩展

- [ ] 实现关联树形选择组件
- [ ] 实现关联级联选择组件
- [ ] 实现关联数据表格组件
- [ ] 添加更多的示例页面

### 6. 文档完善

- [x] 添加关联选择功能完善需求分析文档
- [x] 添加关联选择功能完善实施计划文档
- [x] 添加关联选择功能测试数据文档
- [x] 添加关联选择功能完善迭代总结文档
- [ ] 编写详细的使用文档，包括API说明和示例
- [ ] 创建更多的示例页面，展示不同场景下的使用方式
- [ ] 编写性能优化指南，帮助开发者优化关联选择功能

## 已完成功能

### v0.3.3 (当前版本)

- [x] 完善关联选择功能
    - [x] 实现关联数据缓存机制，优化查询性能
    - [x] 实现条件过滤功能，支持依赖字段变化时更新查询条件
    - [x] 优化关联选择组件，提供更友好的用户界面和交互体验
    - [x] 实现依赖字段提示和禁用功能，提高用户体验
    - [x] 支持批量查询关联实体详情，减少请求次数
    - [x] 修复 ESLint 错误，将 `require()` 风格的导入改为 ES 模块导入风格
    - [x] 创建示例页面和测试数据，验证功能正确性
    - [x] 支持多级关联数据查询，如员工关联部门，部门关联公司

### v0.3.2

- [x] 实现表单分组折叠功能
    - [x] 修改 FieldGroup 组件，使用 Shadcn UI 的 Collapsible 组件实现折叠功能
    - [x] 添加折叠/展开图标和动画效果
    - [x] 修改 EntityForm 组件，添加分组折叠状态管理
    - [x] 实现全局折叠控制功能（展开全部/折叠全部）
    - [x] 默认只展开第一个分组，其他分组折叠
    - [x] 更新示例组件，展示分组折叠功能

### v0.3.1

- [x] 实现表单分组功能，支持 control.layout.group 分组
    - [x] 创建 FieldGroup 组件，用于将同一组的字段包装在一个卡片中
    - [x] 实现 groupFields 函数，将字段按照分组进行分类
    - [x] 实现 getGroupOrder 函数，获取分组顺序
    - [x] 修改 EntityForm 组件，使用分组逻辑渲染字段
    - [x] 创建表单分组示例页面和组件
    - [x] 编写单元测试脚本

### v0.3.0

- [x] 实现以 `EntityMetadata.relations` 为核心的关联实体配置
- [x] 支持同一字段多个关联配置，根据条件判断显示
- [x] 支持自定义关联数据字段名
- [x] 支持服务端 GraphQL 引擎动态获取关联数据
- [x] 实现关联选择组件和关联多选组件
- [x] 添加关联实体配置示例页面
- [x] 重构关联条件判断逻辑，支持条件优先级
- [x] 扩展查询生成器，支持关联数据查询
- [x] 优化关联数据处理功能
- [x] 添加详细的测试用例，验证功能正确性

### v0.2.1

- [x] 实现实体表单的关联选择功能
    - [x] 添加关联数据缓存机制，减少重复请求
    - [x] 实现关联数据查询API，支持条件查询、分页和缓存
    - [x] 实现关联选择单选和多选控件，支持搜索、分页和预览
    - [x] 添加关联选择示例页面和测试用例

### v0.2.0

- [x] 实现基于GraphQL规范的数据接口交互
- [x] 支持按模块划分的GraphQL端点
- [x] 基于元数据信息生成GraphQL查询
- [x] 支持列表过滤条件的Condition类型
- [x] 创建模块化GraphQL客户端
- [x] 实现查询生成器
- [x] 实现条件构建器
- [x] 扩展数据访问层，增加模块支持

### v0.1.9

- [x] 实现表单滚动功能
- [x] 添加product_order的mock数据
- [x] 改进EntityList组件对复杂类型的处理
- [x] 修复联动引擎中的错误处理问题
- [x] 修复default_value为null时联动功能无法正常工作的问题
- [x] 修复字段被隐藏时显示空白占位的问题
- [x] 创建FieldContainer组件，优化字段的可见性控制

### v0.1.8

- [x] 修复联动规则在页面初次渲染时未生效的问题
- [x] 修复React Hooks使用规则错误
- [x] 优化联动规则初始化逻辑

### v0.1.7

- [x] 表单字段联动规则重构
- [x] 实现数据源管理器
- [x] 重构联动引擎
- [x] 更新联动上下文和钩子
- [x] 实现迁移工具
- [x] 创建测试页面和示例数据

### v0.1.6

- [x] 表单字段联动规则增强
- [x] 联动效果类型扩展
- [x] 表达式引擎增强
- [x] 联动调试工具

### v0.1.5

- [x] 实体表单验证优化
- [x] 验证规则类型扩展

### v0.1.4

- [x] 实体列表筛选功能
- [x] 智能推断功能

### v0.1.3

- [x] EntityField组件控制逻辑
- [x] 表达式引擎
- [x] 控件系统

### v0.1.2

- [x] 实体对话框优化
- [x] 实体表单改进

### v0.1.1

- [x] 实体表单优化
- [x] 实体详情优化
- [x] 实体列表优化

### v0.1.0

- [x] 元数据驱动
- [x] GraphQL集成
- [x] 实体CRUD组件
- [x] 路由配置

## 下一步计划

### 1. 数据接口优化

#### 1.1 GraphQL数据接口优化

- [ ] 实现GraphQL查询缓存机制，提高性能
- [ ] 增强错误处理和重试机制，提高稳定性
- [ ] 优化查询生成器，支持字段筛选和嵌套查询
- [ ] 优化条件构建器，支持更复杂的条件组合

#### 1.2 GraphQL请求/响应拦截器

- [ ] 实现认证信息添加
- [ ] 实现错误处理
- [ ] 实现请求日志记录
- [ ] 添加拦截器配置文档

#### 1.3 元数据缓存机制

- [ ] 实现元数据缓存
- [ ] 实现元数据更新机制
- [ ] 添加缓存配置选项
- [ ] 编写缓存使用文档

### 2. 表单功能增强

#### 2.1 实体表单增强

- [x] 完善关联选择功能
    - [x] 实现关联数据缓存机制，优化查询性能
    - [x] 实现条件过滤功能，支持依赖字段变化时更新查询条件
    - [x] 优化关联选择组件，提供更友好的用户界面和交互体验
    - [x] 实现依赖字段提示和禁用功能，提高用户体验
    - [ ] 实现关联创建功能，允许在选择界面中创建新的关联实体
    - [ ] 实现关联预览功能，无需跳转即可查看关联实体的详细信息
    - [ ] 支持多级关联数据查询，如员工关联部门，部门关联公司
- [ ] 实现实体表单的文件上传功能
- [ ] 实现实体表单的富文本编辑功能

#### 2.2 表单字段联动规则完善

- [ ] 移除联动规则初始化中的调试日志
- [ ] 完善 `setValidation` 效果的实现
- [ ] 优化 `custom` 效果的实现，增强其灵活性和安全性
- [ ] 完善 `setVisibility` 效果，当字段不可见时自动清除其值
- [ ] 完善 `setRequired` 效果，使其自动更新验证规则
- [ ] 添加更多的联动效果类型测试用例
- [ ] 完善联动规则的错误处理和调试功能
- [ ] 更新联动规则使用文档

#### 2.3 表单字段控件的联动规则可视化配置

- [ ] 设计联动规则配置界面
- [ ] 实现联动规则可视化编辑
- [ ] 实现联动规则导入/导出
- [ ] 实现联动规则预览和测试
- [ ] 编写联动规则配置使用文档

### 3. 环境配置

- [ ] 实现GraphQL端点配置
- [ ] 实现其他环境变量配置
- [ ] 添加环境变量文档

## 中长期规划

### 1. 功能扩展

#### 1.1 表单高级功能

- [ ] 实现表单权限控制功能
- [ ] 实现表单历史记录功能
- [ ] 实现表单审批流程功能

#### 1.2 组件增强

- [ ] 完善 EntityList 组件
    - [ ] 支持更多的列类型和格式化选项
    - [ ] 优化列宽度的自动调整
    - [ ] 支持列的拖拽排序
- [ ] 增强 EntityDialog 组件
    - [ ] 支持更多的对话框类型和交互方式
    - [ ] 优化对话框的响应式布局
    - [ ] 支持自定义对话框的头部和底部

### 2. 性能优化

#### 2.1 联动引擎优化

- [ ] 使用缓存机制减少不必要的计算
- [ ] 优化依赖图的构建和更新逻辑
- [ ] 添加性能分析工具

#### 2.2 渲染性能优化

- [ ] 使用 React.memo 优化组件
- [ ] 优化 useEffect 依赖项
- [ ] 使用 useMemo 和 useCallback 缓存计算结果和回调函数

#### 2.3 大数据量处理

- [ ] 实现虚拟滚动
- [ ] 分批处理大量数据
- [ ] 优化数据结构

### 3. 用户体验提升

#### 3.1 表单布局改进

- [x] 实现表单分组功能，支持 control.layout.group
- [x] 添加分组标题和折叠功能
- [x] 支持分组内的字段排序
- [ ] 优化分组的响应式布局
- [ ] 开发预定义布局模板（标签页式、步骤式、分区式、卡片式）
- [ ] 实现自定义组件插槽系统
- [ ] 设计布局描述语言
- [ ] 开发可视化布局编辑器

#### 3.2 交互体验增强

- [ ] 添加更多的动画效果
- [ ] 优化加载状态的显示
- [ ] 改进错误处理和提示

### 4. 代码质量提升

#### 4.1 代码重构

- [ ] 联动引擎的代码结构优化，减少重复代码
- [ ] 表单组件的职责划分更加清晰
- [ ] 提取公共逻辑到独立的工具函数

#### 4.2 测试覆盖

- [ ] 添加联动引擎的单元测试
- [ ] 添加表单组件的集成测试
- [ ] 添加端到端测试
- [ ] 添加表单布局相关测试

#### 4.3 文档完善

- [ ] 更新和完善组件和API的文档
- [ ] 添加更多的使用示例和最佳实践
- [ ] 创建开发者指南
- [ ] 编写表单布局相关文档
