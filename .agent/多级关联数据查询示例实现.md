# 多级关联数据查询示例实现

## 1. 示例元数据定义

以下是包含多级关联的实体元数据示例，展示员工-部门-公司的多级关联关系：

### 1.1 员工元数据

```typescript
/**
 * 员工元数据
 */
export const employeeMetadata: EntityMetadata = {
  name: 'employee',
  display_name: '员工',
  module: 'hr',
  description: '员工信息',
  _id: 'employee_metadata_id',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'employee_id_field',
    },
    {
      name: 'code',
      display_name: '工号',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_code_field',
    },
    {
      name: 'name',
      display_name: '姓名',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'employee_name_field',
    },
    {
      name: 'department_id',
      display_name: '部门',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'employee_department_field',
    },
    {
      name: 'position_id',
      display_name: '职位',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'employee_position_field',
    },
    {
      name: 'manager_id',
      display_name: '上级',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'employee_manager_field',
    },
  ],
  // 关联信息
  relations: [
    {
      field: 'department_id',
      entity: 'department',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'department_info',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'manager_name'],
        searchable: true,
      },
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 2, // 最大嵌套深度
    },
    {
      field: 'position_id',
      entity: 'position',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'position_info',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'level', 'department_id'],
        searchable: true,
      },
      nestedRelations: false, // 不允许嵌套查询
    },
    {
      field: 'manager_id',
      entity: 'employee', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager_info',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'position_id'],
        searchable: true,
      },
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 1, // 限制嵌套深度，避免无限递归
    },
  ],
}
```

### 1.2 部门元数据

```typescript
/**
 * 部门元数据
 */
export const departmentMetadata: EntityMetadata = {
  name: 'department',
  display_name: '部门',
  module: 'hr',
  description: '部门信息',
  _id: 'department_metadata_id',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'department_id_field',
    },
    {
      name: 'code',
      display_name: '编码',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_code_field',
    },
    {
      name: 'name',
      display_name: '名称',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'department_name_field',
    },
    {
      name: 'parent_id',
      display_name: '上级部门',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_parent_field',
    },
    {
      name: 'manager_id',
      display_name: '部门经理',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'department_manager_field',
    },
    {
      name: 'company_id',
      display_name: '所属公司',
      type: 'VARCHAR',
      default_value: null,
      flags: [],
      _id: 'department_company_field',
    },
  ],
  // 关联信息
  relations: [
    {
      field: 'parent_id',
      entity: 'department', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'parent_department',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 3, // 允许查询更深的部门层级
    },
    {
      field: 'manager_id',
      entity: 'employee',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'manager_info',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
      nestedRelations: false, // 不允许嵌套查询，避免循环引用
    },
    {
      field: 'company_id',
      entity: 'company',
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'company_info',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code', 'address'],
        searchable: true,
      },
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 1, // 限制嵌套深度
    },
  ],
}
```

### 1.3 公司元数据

```typescript
/**
 * 公司元数据
 */
export const companyMetadata: EntityMetadata = {
  name: 'company',
  display_name: '公司',
  module: 'hr',
  description: '公司信息',
  _id: 'company_metadata_id',
  fields: [
    {
      name: '_id',
      display_name: 'ID',
      type: 'ID',
      default_value: null,
      flags: ['PRIMARY_KEY'],
      _id: 'company_id_field',
    },
    {
      name: 'code',
      display_name: '编码',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'company_code_field',
    },
    {
      name: 'name',
      display_name: '名称',
      type: 'VARCHAR',
      default_value: null,
      flags: ['KEY_INFO'],
      _id: 'company_name_field',
    },
    {
      name: 'address',
      display_name: '地址',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'company_address_field',
    },
    {
      name: 'parent_id',
      display_name: '母公司',
      type: 'VARCHAR',
      default_value: null,
      flags: ['NULLABLE'],
      _id: 'company_parent_field',
    },
  ],
  // 关联信息
  relations: [
    {
      field: 'parent_id',
      entity: 'company', // 自关联
      module: 'hr',
      type: 'one',
      foreignField: '_id',
      alias: 'parent_company',
      condition: {
        type: 'always',
      },
      control: {
        labelField: 'name',
        displayFields: ['name', 'code'],
        searchable: true,
      },
      nestedRelations: true, // 允许嵌套查询
      maxDepth: 2, // 限制嵌套深度
    },
  ],
}
```

## 2. 示例查询生成

以下是使用扩展后的查询生成器生成多级关联查询的示例：

```typescript
// 预加载关联实体元数据
await preloadRelationMetadata(employeeMetadata, { userRole: 'admin', mode: 'view' }, 3);

// 生成包含多级关联的查询
const query = QueryGenerator.generateDetailQuery(
  employeeMetadata,
  { userRole: 'admin', mode: 'view' },
  true, // 包含关联数据
  3 // 最大嵌套深度
);

console.log(query);
```

生成的查询示例：

```graphql
query GetemployeeDetail($_id: ID!) {
  employee_detail($_id: $_id) {
    _id
    code
    name
    department_id
    position_id
    manager_id
    department_info {
      _id
      code
      name
      manager_name
      parent_department {
        _id
        code
        name
      }
      company_info {
        _id
        code
        name
        address
      }
    }
    position_info {
      _id
      name
      level
      department_id
    }
    manager_info {
      _id
      name
      code
      position_id
      department_info {
        _id
        code
        name
        manager_name
      }
    }
  }
}
```

## 3. 示例数据处理

以下是使用扩展后的数据处理器处理多级关联数据的示例：

```typescript
// 原始数据（包含关联数据）
const rawData = {
  _id: 'emp_001',
  code: 'E001',
  name: '张三',
  department_id: 'dept_001',
  position_id: 'pos_001',
  manager_id: null,
  
  // 关联数据
  department_info: {
    _id: 'dept_001',
    code: 'D001',
    name: '研发部',
    manager_name: '张三',
    
    // 嵌套关联数据
    parent_department: {
      _id: 'dept_000',
      code: 'D000',
      name: '总部',
    },
    
    company_info: {
      _id: 'comp_001',
      code: 'C001',
      name: '示例科技有限公司',
      address: '北京市海淀区',
    },
  },
  
  position_info: {
    _id: 'pos_001',
    name: '技术总监',
    level: 'P8',
    department_id: 'dept_001',
  },
};

// 处理数据，包含多级关联
const processedData = processEntityData(
  rawData,
  employeeMetadata,
  { userRole: 'admin', mode: 'view' },
  3 // 最大处理深度
);

console.log(JSON.stringify(processedData, null, 2));
```

处理后的数据示例：

```json
{
  "_id": "emp_001",
  "code": "E001",
  "name": "张三",
  "department_id": "dept_001",
  "position_id": "pos_001",
  "manager_id": null,
  "department_info": {
    "_id": "dept_001",
    "code": "D001",
    "name": "研发部",
    "manager_name": "张三",
    "parent_department": {
      "_id": "dept_000",
      "code": "D000",
      "name": "总部"
    },
    "company_info": {
      "_id": "comp_001",
      "code": "C001",
      "name": "示例科技有限公司",
      "address": "北京市海淀区"
    }
  },
  "position_info": {
    "_id": "pos_001",
    "name": "技术总监",
    "level": "P8",
    "department_id": "dept_001"
  },
  "department_id_data": {
    "_id": "dept_001",
    "code": "D001",
    "name": "研发部",
    "manager_name": "张三",
    "parent_department": {
      "_id": "dept_000",
      "code": "D000",
      "name": "总部"
    },
    "company_info": {
      "_id": "comp_001",
      "code": "C001",
      "name": "示例科技有限公司",
      "address": "北京市海淀区"
    },
    "parent_id_data": {
      "_id": "dept_000",
      "code": "D000",
      "name": "总部"
    },
    "company_id_data": {
      "_id": "comp_001",
      "code": "C001",
      "name": "示例科技有限公司",
      "address": "北京市海淀区"
    }
  },
  "position_id_data": {
    "_id": "pos_001",
    "name": "技术总监",
    "level": "P8",
    "department_id": "dept_001"
  }
}
```
