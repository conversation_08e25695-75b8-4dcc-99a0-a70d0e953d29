# 低代码平台前端需求分析和技术方案

## 1. 项目概述

基于vibe-admin项目开发一个低代码平台前端，该平台采用元数据驱动的开发方式，使用GraphQL规范与后端进行通信，并实现统一范式的CRUD组件。

## 2. 需求分析

### 2.1 元数据驱动

项目需要基于元数据驱动开发，元数据将从服务端获取。主要元数据类型为：

- `def_entity`：描述数据库结构，包含实体的基本信息（名称、显示名称、模块等）以及字段定义（名称、显示名称、类型、默认值、标志等）。

### 2.2 统一范式CRUD组件

需要实现基于`def_entity`元数据的统一范式CRUD组件，包括：

- 列表页（分页查询）
- 详情页
- 新增表单
- 编辑表单
- 删除功能

### 2.3 GraphQL接口规范

后端接口采用GraphQL规范，包括以下接口：

- 获取元数据：`_metadata(key: ID!): JSON`
- 获取业务数据详情：`{entity_name}_detail(_id: ID!): {entity_name}`
- 获取业务数据分页：`{entity_name}_page(condition: JSON, page: Int, page_size: Int): {entity_name}_page`
- 新增业务数据：`{entity_name}_insert(input: {entity_name}_input!): {entity_name}`
- 更新业务数据：`{entity_name}_update(_id: ID!, input: {entity_name}_input!): {entity_name}`
- 删除业务数据：`{entity_name}_delete(_id: ID!): Boolean`

## 3. 技术方案设计

### 3.1 技术栈

当前项目使用的主要技术栈：

- **UI框架**：React 19 + ShadcnUI (基于TailwindCSS和RadixUI)
- **构建工具**：Vite
- **路由**：TanStack Router
- **状态管理**：Zustand
- **表单处理**：React Hook Form + Zod
- **数据获取**：TanStack Query (React Query)
- **HTTP客户端**：Axios
- **表格组件**：TanStack Table
- **类型检查**：TypeScript

新增技术栈：

- **GraphQL客户端**：graphql-request

### 3.2 项目结构

基于现有项目结构，我们添加以下模块：

```
src/
├── features/
│   ├── metadata/           # 元数据相关功能
│   │   ├── api/            # 元数据API调用
│   │   ├── hooks/          # 元数据相关钩子函数
│   │   ├── types/          # 元数据类型定义
│   │   └── utils/          # 元数据工具函数
│   │
│   ├── entity/             # 实体通用功能
│       ├── api/            # 实体API调用
│       ├── components/     # 通用CRUD组件
│       │   ├── EntityList.tsx       # 实体列表组件
│       │   ├── EntityDetail.tsx     # 实体详情组件
│       │   ├── EntityForm.tsx       # 实体表单组件
│       │   ├── EntityField.tsx      # 实体字段组件
│       │   └── EntityDialog.tsx     # 实体对话框组件
│       ├── hooks/          # 实体相关钩子函数
│       └── utils/          # 实体工具函数
│
├── lib/
│   ├── graphql/            # GraphQL相关功能
│   │   ├── client.ts       # GraphQL客户端配置
│   │   ├── queries.ts      # 查询定义
│   │   └── mutations.ts    # 变更定义
│   │
│   └── metadata/           # 元数据处理工具
│       ├── parser.ts       # 元数据解析器
│       └── validator.ts    # 元数据验证器
│
├── routes/                 # 路由定义
│   ├── _authenticated/     # 需要认证的路由
│   │   ├── entity/         # 实体相关路由
│   │   │   ├── $name/      # 实体列表路由
│   │   │   │   └── $id/    # 实体详情路由
│   │   │
│   │   └── metadata/       # 元数据管理路由
```

### 3.3 元数据处理

#### 3.3.1 元数据类型定义

```typescript
// src/features/metadata/types/index.ts

export interface EntityField {
  name: string;
  display_name: string;
  type: string;
  default_value: string | null;
  flags: string[];
  _id: string;
}

export interface EntityMetadata {
  name: string;
  display_name: string;
  module: string;
  description: string | null;
  fields: EntityField[];
  _id: string;
}

export interface PageResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
}
```

#### 3.3.2 元数据解析器

元数据解析器用于解析元数据，提取有用的信息，如：

- 获取字段类型对应的表单控件类型
- 判断字段是否为主键
- 判断字段是否为内部字段
- 判断字段是否可为空
- 获取实体中的可编辑字段
- 获取实体中的可显示字段

#### 3.3.3 元数据验证器

元数据验证器用于生成表单验证规则，如：

- 根据字段类型生成对应的Zod验证规则
- 根据实体元数据生成表单验证Schema
- 生成表单默认值

### 3.4 GraphQL客户端

使用graphql-request作为GraphQL客户端，配置如下：

- 创建GraphQL客户端实例
- 自动添加认证token到请求头
- 定义查询和变更操作

### 3.5 实体API

实现以下实体API调用：

- 获取实体列表（分页）
- 获取实体详情
- 创建实体
- 更新实体
- 删除实体

### 3.6 实体组件

#### 3.6.1 实体列表组件

实体列表组件用于展示实体数据列表，支持分页、排序、筛选等功能。

#### 3.6.2 实体详情组件

实体详情组件用于展示实体详细信息。

#### 3.6.3 实体表单组件

实体表单组件用于创建和编辑实体。

#### 3.6.4 实体字段组件

实体字段组件用于根据字段类型渲染不同的表单控件。

##### 3.6.4.1 字段类型映射

组件通过`getFieldControlType`函数将数据库字段类型映射为表单控件类型：

| 数据库字段类型          | 表单控件类型   | 对应UI组件             |
|------------------|----------|--------------------|
| VARCHAR          | text     | Input              |
| TEXT             | textarea | Textarea           |
| INT/FLOAT/DOUBLE | number   | Input[type=number] |
| BOOLEAN          | checkbox | Checkbox           |
| DATE             | date     | Calendar           |
| DATE_TIME        | datetime | Calendar           |
| ENUM             | select   | Select             |

##### 3.6.4.2 控制逻辑

**渲染控制逻辑**：

1. 根据字段类型选择合适的表单控件
2. 显示字段的display_name作为标签
3. 根据NULLABLE标志决定是否显示必填标记（*）
4. 在只读模式下禁用控件并添加样式

**数据绑定逻辑**：

1. 使用React Hook Form的`useFormContext`获取表单上下文
2. 通过`FormField`组件将字段与表单状态绑定
3. 处理不同类型的值转换（如数字、日期、布尔等）

**验证逻辑**：

1. 根据字段类型和标志生成Zod验证规则
2. 对非NULLABLE字段添加必填验证
3. 对数字类型添加范围验证
4. 使用FormMessage组件显示验证错误

##### 3.6.4.3 待实现功能

**高级字段类型**：

1. 富文本编辑器：支持HTML或Markdown格式
2. 文件上传：支持单文件和多文件上传
3. 关联选择器：支持从关联实体中选择数据
4. 地址/位置选择器：集成地图组件

**交互增强**：

1. 条件显示：根据其他字段的值动态显示或隐藏字段
2. 动态选项：支持从API动态加载选项数据
3. 即时验证：支持自定义验证规则和跨字段验证

**可访问性增强**：

1. 键盘导航：确保所有控件可通过键盘访问和操作
2. 屏幕阅读器支持：添加适当的ARIA属性
3. 高对比度模式：支持高对比度主题

#### 3.6.5 实体对话框组件

实体对话框组件用于创建、编辑和删除实体。

### 3.7 路由配置

配置以下路由：

- `/entity/:name`：实体列表页
- `/entity/:name/:id`：实体详情页
- `/metadata`：元数据管理页

## 4. 实现计划

### 4.1 第一阶段：基础架构搭建

- 创建元数据类型定义
- 创建GraphQL客户端配置
- 创建GraphQL查询和变更操作
- 创建元数据API调用
- 创建元数据处理工具
- 创建实体API调用

### 4.2 第二阶段：组件开发

- 创建实体表格工具
- 创建实体表单工具
- 创建实体组件
- 创建数据表格组件

### 4.3 第三阶段：路由配置

- 创建实体路由
- 创建元数据管理页面
- 更新侧边栏导航

### 4.4 第四阶段：功能优化

- 实现环境变量配置
- 实现GraphQL请求拦截器
- 实现GraphQL响应拦截器
- 实现元数据缓存机制
- 实现实体列表页面的高级筛选功能
- 实现实体表单的自定义验证规则
- 实现实体表单的自定义字段类型

### 4.5 第五阶段：测试与部署

- 编写单元测试
- 编写集成测试
- 编写端到端测试
- 配置CI/CD流程
- 配置部署环境

## 5. EntityField组件架构设计

### 5.1 组件拆分

#### 5.1.1 基础控件封装

为每种控件类型创建专门的组件，统一处理值转换、格式化和验证：

```typescript
// 示例：数字字段组件
export function NumberField({ field, ...props }) {
  return (
    <Input
      type = "number"
  onChange = {(e)
=>
  props.onChange(e.target.valueAsNumber || 0)
}
  {...
    props
  }
  />
)
  ;
}
```

#### 5.1.2 复合控件抽象

将复杂控件（如日期选择器）抽象为独立组件：

```typescript
// 示例：日期选择器组件
export function DatePickerField({ value, onChange, readOnly, ...props }) {
  return (
    <Popover>
      <PopoverTrigger asChild >
    <Button
      variant = "outline"
  disabled = { readOnly }
  {...
    props
  }
>
  <CalendarIcon className = "mr-2 h-4 w-4" / >
    {
      value ? format(value, 'yyyy-MM-dd') : <span>请选择日期 < /span>}
        < /Button>
        < /PopoverTrigger>
        < PopoverContent className = "w-auto p-0" >
      <Calendar
        mode = "single"
      selected = { value }
      onSelect = { onChange }
      initialFocus
    / >
    </PopoverContent>
    < /Popover>
)
  ;
}
```

#### 5.1.3 字段工厂

创建字段工厂函数，根据字段类型返回相应的组件：

```typescript
export function createFieldComponent(field: EntityField) {
  const controlType = getFieldControlType(field);

  switch (controlType) {
    case 'text':
      return TextField;
    case 'textarea':
      return TextareaField;
    case 'number':
      return NumberField;
    // ...其他类型
    default:
      return TextField;
  }
}
```

### 5.2 状态管理

#### 5.2.1 表单上下文

使用React Context共享表单配置和状态：

```typescript
const EntityFormContext = createContext<{
  metadata: EntityMetadata;
  readOnly: boolean;
  // 其他表单配置
}>(null);

export function useEntityFormContext() {
  return useContext(EntityFormContext);
}
```

#### 5.2.2 字段依赖关系

实现字段之间的依赖关系管理：

```typescript
function useFieldDependencies(field: EntityField, form: any) {
  const watchFields = [/* 依赖字段列表 */];
  const dependencies = form.watch(watchFields);

  // 根据依赖字段的值计算当前字段的状态
  const isVisible = useMemo(() => {
    // 计算是否显示字段
    return true; // 默认显示
  }, [dependencies]);

  return { isVisible };
}
```

### 5.3 扩展性设计

#### 5.3.1 插件系统

设计插件接口允许添加自定义字段类型：

```typescript
interface FieldPlugin {
  type: string; // 字段类型
  component: React.ComponentType<any>; // 字段组件
  validator?: (field: EntityField) => any; // 验证规则生成器
  formatter?: (value: any) => any; // 值格式化器
  parser?: (value: any) => any; // 值解析器
}

// 注册插件
function registerFieldPlugin(plugin: FieldPlugin) {
  // 将插件添加到插件注册表中
}
```

#### 5.3.2 主题定制

支持通过主题配置自定义外观：

```typescript
interface FieldTheme {
  container?: string; // 容器样式
  label?: string; // 标签样式
  input?: string; // 输入框样式
  error?: string; // 错误消息样式
  // 其他样式配置
}

const defaultTheme: FieldTheme = {
  container: 'space-y-2',
  label: 'text-sm font-medium',
  input: 'mt-1',
  error: 'text-destructive text-sm',
};

// 使用主题
function useFieldTheme() {
  // 从上下文中获取主题配置
  return defaultTheme;
}
```

### 5.4 性能优化

#### 5.4.1 组件缓存

使用React.memo减少不必要的重渲染：

```typescript
export const EntityField = React.memo(function EntityField(props: EntityFieldProps) {
  // 组件实现
});
```

#### 5.4.2 延迟加载

实现复杂控件的延迟加载：

```typescript
const RichTextEditor = React.lazy(() => import('./RichTextEditor'));

function RichTextField(props) {
  return (
    <React.Suspense fallback = { < div > 加载中
...
  </div>}>
  < RichTextEditor
  {...
    props
  }
  />
  < /React.Suspense>
)
  ;
}
```

## 6. 技术挑战与解决方案

### 6.1 动态生成表单

**挑战**：根据元数据动态生成表单，支持不同类型的字段。

**解决方案**：

- 使用React Hook Form和Zod进行表单处理和验证
- 创建通用的表单字段组件，根据字段类型渲染不同的控件
- 使用元数据解析器和验证器生成表单配置

### 5.2 动态生成GraphQL查询

**挑战**：根据实体名称动态生成GraphQL查询。

**解决方案**：

- 创建查询和变更操作模板
- 根据实体名称动态替换模板中的占位符
- 使用graphql-request发送查询

### 5.3 性能优化

**挑战**：处理大量数据时的性能问题。

**解决方案**：

- 使用React Query进行数据缓存和失效处理
- 实现分页、排序和筛选等功能，减少数据传输量
- 使用虚拟滚动技术，只渲染可见区域的数据

### 5.4 用户体验优化

**挑战**：提供良好的用户体验，特别是在表单操作和数据加载方面。

**解决方案**：

- 使用骨架屏和加载指示器提示用户数据正在加载
- 使用Toast提示用户操作结果
- 实现表单验证和错误提示
- 使用对话框进行确认操作

## 6. 未来扩展

### 6.1 高级功能

- 实现实体表单的文件上传功能
- 实现实体表单的富文本编辑功能
- 实现实体表单的关联选择功能
- 实现实体表单的多语言支持
- 实现实体表单的权限控制
- 实现实体表单的工作流支持
- 实现实体表单的版本控制
- 实现实体表单的审计日志

### 6.2 可视化配置

- 实现可视化的元数据编辑器
- 实现可视化的表单设计器
- 实现可视化的列表设计器
- 实现可视化的仪表盘设计器

### 6.3 集成与扩展

- 集成第三方组件库
- 支持自定义组件
- 支持插件系统
- 支持主题定制
