# 低代码平台前端产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品背景

随着企业数字化转型的加速，对内部管理系统和业务应用的需求不断增长。传统的开发方式难以满足快速变化的业务需求，低代码平台作为一种高效的应用开发方式，能够显著提高开发效率，降低技术门槛，使业务人员也能参与到应用构建中。

### 1.2 产品定位

本产品是一个基于元数据驱动的低代码平台前端，专注于通用管理后台场景，采用GraphQL规范与后端通信，提供统一范式的CRUD组件，帮助开发者快速构建企业级应用。

### 1.3 目标用户

- 前端开发人员：使用平台快速构建管理后台
- 后端开发人员：通过配置元数据快速生成前端界面
- 业务分析人员：参与应用设计和配置
- 系统管理员：管理和维护基于平台构建的应用

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 元数据驱动

- **元数据定义**：支持通过EntityMetadata定义实体结构，包括字段类型、显示名称、验证规则等
- **元数据管理**：提供元数据的查询、缓存和更新机制
- **元数据解析**：根据元数据自动生成UI组件和交互逻辑

#### 2.1.2 统一范式CRUD组件

- **实体列表**：支持分页、排序、筛选、导出等功能
- **实体详情**：支持查看实体详细信息，包括表格视图和表单视图
- **实体表单**：支持创建和编辑实体，包括各种类型的表单控件
- **实体操作**：支持删除、批量操作等功能

#### 2.1.3 表单字段控件

- **基础控件**：支持文本、数字、日期、选择、开关等基础控件
- **高级控件**：支持富文本、文件上传、关联选择等高级控件
- **自定义控件**：支持注册和使用自定义控件

#### 2.1.4 表单验证规则

- **基础验证**：支持必填、长度、范围、格式等基础验证
- **自定义验证**：支持条件验证、依赖字段验证、异步验证等
- **验证消息**：支持动态错误消息，包括模板插值

#### 2.1.5 表单字段联动规则

- **联动效果**：支持设置值、设置选项、设置可见性、设置必填、设置禁用等联动效果
- **联动来源**：支持表单字段、表单状态、用户角色、全局配置等多种联动来源
- **联动条件**：支持条件表达式，决定何时触发联动
- **联动配置**：支持可视化配置联动规则

#### 2.1.6 实体列表筛选

- **关键字搜索**：支持对关键信息字段进行模糊查询
- **字段筛选**：支持对特定字段进行精确筛选
- **筛选配置**：支持自定义显示的筛选字段
- **高级筛选**：支持复杂的条件组合

### 2.2 扩展功能

#### 2.2.1 多语言支持

- 支持界面元素的多语言配置
- 支持数据内容的多语言存储和显示
- 支持语言切换

#### 2.2.2 权限控制

- 支持基于角色的权限控制
- 支持字段级别的权限控制
- 支持操作级别的权限控制

#### 2.2.3 工作流支持

- 支持工作流状态管理
- 支持工作流流转
- 支持审批流程

#### 2.2.4 版本控制

- 支持数据版本管理
- 支持版本比较
- 支持版本回滚

#### 2.2.5 审计日志

- 支持操作日志记录
- 支持日志查询和展示
- 支持日志导出

## 3. 非功能需求

### 3.1 性能需求

- 页面加载时间：首次加载不超过3秒，后续操作响应时间不超过1秒
- 数据处理能力：能够处理大量数据（10000+条记录）的列表展示和操作
- 并发用户数：支持100+用户同时在线操作

### 3.2 可用性需求

- 界面设计：符合现代UI设计规范，提供良好的用户体验
- 响应式设计：支持桌面端和移动端的自适应布局
- 可访问性：符合WCAG 2.1 AA级别的可访问性标准

### 3.3 安全需求

- 认证与授权：支持多种认证方式，实现细粒度的权限控制
- 数据安全：敏感数据加密存储和传输
- 防攻击：防止XSS、CSRF等常见Web攻击

### 3.4 可扩展性需求

- 插件机制：支持通过插件扩展平台功能
- 主题定制：支持自定义主题和样式
- API集成：支持与第三方系统集成

## 4. 用户界面需求

### 4.1 总体布局

- 顶部导航栏：显示应用名称、用户信息、全局搜索等
- 侧边菜单：显示应用模块和功能入口
- 主内容区：显示当前功能的内容
- 底部状态栏：显示版本信息、版权信息等

### 4.2 实体列表页

- 顶部工具栏：包含新增、批量操作、导出等按钮
- 筛选区域：包含关键字搜索和字段筛选
- 数据表格：显示实体数据，支持排序、分页
- 操作列：包含查看、编辑、删除等操作按钮

### 4.3 实体表单页

- 表单区域：根据元数据生成的表单控件，支持分组和布局
- 底部按钮区：包含保存、取消等按钮
- 验证提示：显示表单验证错误信息
- 联动效果：根据联动规则动态调整表单行为

### 4.4 实体详情页

- 详情视图：以表格或表单形式显示实体详细信息
- 相关数据：显示与当前实体相关的其他数据
- 操作按钮：包含编辑、删除等操作按钮

## 5. 技术要求

### 5.1 前端技术栈

- **UI框架**：React 19 + ShadcnUI (基于TailwindCSS和RadixUI)
- **构建工具**：Vite
- **路由**：TanStack Router
- **状态管理**：Zustand
- **表单处理**：React Hook Form + Zod
- **数据获取**：TanStack Query (React Query)
- **HTTP客户端**：Axios
- **表格组件**：TanStack Table
- **类型检查**：TypeScript
- **GraphQL客户端**：graphql-request

### 5.2 后端接口要求

- **接口规范**：GraphQL
- **元数据接口**：`_metadata(key: ID!): JSON`
- **实体接口**：
    - 获取详情：`{entity_name}_detail(_id: ID!): {entity_name}`
    - 获取分页：`{entity_name}_page(condition: JSON, page: Int, page_size: Int): {entity_name}_page`
    - 创建：`{entity_name}_insert(input: {entity_name}_input!): {entity_name}`
    - 更新：`{entity_name}_update(_id: ID!, input: {entity_name}_input!): {entity_name}`
    - 删除：`{entity_name}_delete(_id: ID!): Boolean`

### 5.3 部署要求

- 支持静态文件部署
- 支持环境变量配置
- 支持CI/CD集成

## 6. 项目规划

### 6.1 开发阶段

1. **阶段一：基础架构搭建**（已完成）
    - 元数据类型定义
    - GraphQL客户端配置
    - 实体API调用
    - 实体组件开发
    - 路由配置

2. **阶段二：核心功能开发**（已完成）
    - 实体表单优化
    - 实体列表优化
    - 实体对话框优化
    - EntityField组件控制逻辑
    - 实体列表筛选功能
    - 实体表单验证优化
    - 表单字段联动规则
    - 表单字段联动规则重构

3. **阶段三：功能完善**（进行中）
    - 环境变量配置
    - GraphQL请求/响应拦截器
    - 元数据缓存机制
    - 表单字段联动规则完善
    - 表单字段联动规则可视化配置

4. **阶段四：高级功能开发**（计划中）
    - 实体表单的自定义字段类型
    - 实体表单的文件上传功能
    - 实体表单的富文本编辑功能
    - 实体表单的关联选择功能

5. **阶段五：扩展功能开发**（计划中）
    - 实体表单的多语言支持
    - 实体表单的权限控制
    - 实体表单的工作流支持
    - 实体表单的版本控制
    - 实体表单的审计日志

### 6.2 测试阶段

- 单元测试
- 集成测试
- 端到端测试
- 性能测试
- 安全测试
- 可访问性测试

### 6.3 文档阶段

- 开发文档
- 用户手册
- API文档

### 6.4 部署阶段

- CI/CD配置
- 环境部署
- 监控配置
- 日志配置
- 备份配置

## 7. 风险与挑战

### 7.1 技术风险

- **动态生成表单**：根据元数据动态生成表单，支持不同类型的字段
- **字段联动规则**：实现复杂的字段联动规则，支持多种联动效果和数据源
- **性能优化**：处理大量数据时的性能问题
- **用户体验**：提供良好的用户体验，特别是在表单操作和数据加载方面

### 7.2 解决方案

- 使用React Hook Form和Zod进行表单处理和验证
- 创建通用的表单字段组件，根据字段类型渲染不同的控件
- 使用"拉"模式的联动规则，在字段上配置"本字段受哪些来源影响"
- 实现数据源管理器，支持多种类型的数据源
- 使用React Query进行数据缓存和失效处理
- 实现分页、排序和筛选等功能，减少数据传输量
- 使用虚拟滚动技术，只渲染可见区域的数据
- 使用骨架屏和加载指示器提示用户数据正在加载

## 8. 附录

### 8.1 术语表

- **低代码平台**：一种通过可视化配置和少量代码即可快速开发应用的平台
- **元数据**：描述数据结构和行为的数据
- **EntityMetadata**：描述实体结构的元数据
- **CRUD**：创建（Create）、读取（Read）、更新（Update）、删除（Delete）的缩写
- **GraphQL**：一种用于API的查询语言和运行时
- **联动规则**：定义字段之间交互关系的规则
- **验证规则**：定义字段值有效性的规则
