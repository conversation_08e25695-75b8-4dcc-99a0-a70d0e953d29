# 基于GraalVM.js的业务逻辑共用实现方案

## 1. 方案概述

本方案旨在利用GraalVM.js实现前后端业务逻辑共用，每个业务实体对应一个JavaScript文件，通过统一的JavaScript语言实现前后端大部分业务逻辑的共用。

### 1.1 核心思想

1. **业务逻辑集中化**：将业务逻辑集中在JavaScript文件中，避免在前端和后端重复实现
2. **统一语言**：使用JavaScript作为业务逻辑的统一语言，降低开发者认知负担
3. **灵活性**：利用JavaScript的动态特性，实现灵活的业务规则配置
4. **可维护性**：业务逻辑与技术实现分离，提高代码可维护性

### 1.2 技术基础

- **后端**：GraalVM.js引擎，用于执行JavaScript代码
- **前端**：直接使用JavaScript业务逻辑文件
- **通信**：GraphQL作为前后端数据交互接口

## 2. 系统架构

### 2.1 整体架构

```
+------------------+        +------------------+
|    前端应用      |        |    后端应用      |
|  (React + JS)    |        |  (Spring Boot)   |
+--------+---------+        +--------+---------+
         |                           |
         v                           v
+--------+---------+        +--------+---------+
|  业务逻辑JS文件  | <----> |  GraalVM.js引擎  |
+------------------+        +------------------+
                                     |
                                     v
                            +--------+---------+
                            |   Java业务对象   |
                            +------------------+
```

### 2.2 组件关系

1. **业务逻辑JS文件**：包含实体的业务规则、验证逻辑、计算逻辑等
2. **前端应用**：直接导入业务逻辑JS文件，用于表单验证、数据计算等
3. **后端应用**：通过GraalVM.js引擎加载和执行业务逻辑JS文件
4. **Java业务对象**：提供给JavaScript代码访问的Java对象和方法

## 3. 业务逻辑JS文件设计

### 3.1 文件组织结构

```
/business-logic/
  /entity1/
    index.js       # 实体1的所有业务逻辑
  /entity2/
    index.js       # 实体2的所有业务逻辑
  /common/
    utils.js       # 通用工具函数
```

### 3.2 业务逻辑JS文件模板

```javascript
/**
 * 实体业务逻辑定义
 */
export default {
  /**
   * 元数据
   */
  metadata: {
    // 实体名称
    name: "entity_name",
    // 显示名称
    displayName: "实体显示名称",
    // 描述
    description: "实体描述"
  },
  
  /**
   * 验证规则
   */
  validation: {
    /**
     * 字段验证
     * @param {string} field - 字段名
     * @param {any} value - 字段值
     * @param {object} record - 完整记录
     * @returns {object} 验证结果
     */
    validateField(field, value, record) {
      // 返回验证结果
      return { valid: true, message: "" };
    },
    
    /**
     * 表单验证
     * @param {object} record - 完整记录
     * @returns {object} 验证结果
     */
    validateForm(record) {
      const errors = [];
      
      // 执行验证逻辑
      
      return { valid: errors.length === 0, errors };
    }
  },
  
  /**
   * 计算规则
   */
  calculation: {
    /**
     * 计算字段值
     * @param {string} field - 字段名
     * @param {object} record - 完整记录
     * @returns {any} 计算结果
     */
    calculateField(field, record) {
      // 根据字段名执行不同的计算逻辑
      switch (field) {
        case "field1":
          // 计算field1的值
          return someCalculation(record);
        case "field2":
          // 计算field2的值
          return anotherCalculation(record);
        default:
          return null;
      }
    }
  },
  
  /**
   * 业务规则
   */
  rules: {
    /**
     * 前置规则（保存前执行）
     * @param {object} record - 记录对象
     * @returns {object} 处理后的记录
     */
    beforeSave(record) {
      // 修改record或返回新record
      return record;
    },
    
    /**
     * 后置规则（保存后执行）
     * @param {object} record - 记录对象
     */
    afterSave(record) {
      // 执行后续操作
    }
  },
  
  /**
   * 自定义函数
   */
  functions: {
    /**
     * 自定义业务函数
     * @param {object} params - 参数
     * @returns {any} 结果
     */
    customFunction(params) {
      // 返回结果
      return result;
    }
  }
};
```

## 4. 后端实现

### 4.1 JavaScript引擎管理器

```java
@Component
@Slf4j
public class JsEngineManager {
    private final Map<String, Context> contextCache = new ConcurrentHashMap<>();
    private final Map<String, Value> scriptCache = new ConcurrentHashMap<>();
    
    /**
     * 加载并执行JS文件
     * @param entityName 实体名称
     * @return JavaScript对象
     */
    public Value loadScript(String entityName) {
        try {
            if (scriptCache.containsKey(entityName)) {
                return scriptCache.get(entityName);
            }
            
            // 读取JS文件内容
            String scriptPath = "/business-logic/" + entityName + "/index.js";
            String scriptContent = readScriptContent(scriptPath);
            
            // 创建执行上下文
            Context context = Context.newBuilder("js")
                .allowAllAccess(true)
                .build();
            
            // 执行脚本
            Value bindings = context.getBindings("js");
            context.eval("js", "var exports = {};");
            Value result = context.eval("js", scriptContent);
            
            // 获取默认导出
            Value defaultExport = bindings.getMember("exports").getMember("default");
            
            // 缓存结果
            contextCache.put(entityName, context);
            scriptCache.put(entityName, defaultExport);
            
            return defaultExport;
        } catch (Exception e) {
            log.error("Failed to load script for entity: " + entityName, e);
            throw new RuntimeException("Failed to load script for entity: " + entityName, e);
        }
    }
    
    /**
     * 获取实体的业务逻辑
     * @param entityName 实体名称
     * @return 业务逻辑对象
     */
    public Value getEntityLogic(String entityName) {
        return scriptCache.computeIfAbsent(entityName, this::loadScript);
    }
    
    /**
     * 执行业务逻辑函数
     * @param entityName 实体名称
     * @param functionPath 函数路径（如 "rules.beforeSave"）
     * @param args 参数
     * @return 执行结果
     */
    public Object executeFunction(String entityName, String functionPath, Object... args) {
        try {
            Value entityLogic = getEntityLogic(entityName);
            
            // 解析函数路径
            String[] pathParts = functionPath.split("\\.");
            Value function = entityLogic;
            
            for (String part : pathParts) {
                function = function.getMember(part);
            }
            
            if (function.canExecute()) {
                return function.execute(args).as(Object.class);
            } else {
                log.warn("Function {} is not executable in entity {}", functionPath, entityName);
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to execute function {} for entity {}", functionPath, entityName, e);
            throw new RuntimeException("Failed to execute function", e);
        }
    }
    
    /**
     * 读取脚本内容
     * @param scriptPath 脚本路径
     * @return 脚本内容
     */
    private String readScriptContent(String scriptPath) throws IOException {
        try (InputStream is = getClass().getResourceAsStream(scriptPath)) {
            if (is == null) {
                throw new FileNotFoundException("Script file not found: " + scriptPath);
            }
            return new String(is.readAllBytes(), StandardCharsets.UTF_8);
        }
    }
    
    /**
     * 关闭所有上下文
     */
    @PreDestroy
    public void closeAll() {
        for (Context context : contextCache.values()) {
            try {
                context.close();
            } catch (Exception e) {
                log.error("Error closing context", e);
            }
        }
        contextCache.clear();
        scriptCache.clear();
    }
}
```

### 4.2 JavaScript业务逻辑扩展

```java
@Component
@RequiredArgsConstructor
public class JsBusinessLogicExtension implements IMutationExtension {
    private final JsEngineManager jsEngineManager;
    
    @Override
    public String supportEntity() {
        return EngineConstants.DEFAULT_EXTENSION_ENTITY;
    }
    
    @Override
    public int getOrder() {
        return 1000;
    }
    
    @Override
    public void preSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
        try {
            // 执行前置业务规则
            Object result = jsEngineManager.executeFunction(
                entity.getName(), 
                "rules.beforeSave", 
                new ProxyRecord(data)
            );
            
            // 如果返回了新记录，更新数据
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                    data.set(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("Error executing beforeSave for entity: " + entity.getName(), e);
            throw new RuntimeException("Business logic execution failed", e);
        }
    }
    
    @Override
    public void postSave(ExtensionContext<IRecord> context, DefEntity entity, IRecord data) {
        try {
            // 执行后置业务规则
            jsEngineManager.executeFunction(
                entity.getName(), 
                "rules.afterSave", 
                new ProxyRecord(data)
            );
        } catch (Exception e) {
            log.error("Error executing afterSave for entity: " + entity.getName(), e);
            // 不抛出异常，避免影响事务
        }
    }
    
    /**
     * 记录代理类，用于在JavaScript中访问Java记录
     */
    private static class ProxyRecord implements ProxyObject {
        private final IRecord record;
        
        public ProxyRecord(IRecord record) {
            this.record = record;
        }
        
        @Override
        public Object getMember(String key) {
            return record.get(key);
        }
        
        @Override
        public Object getMemberKeys() {
            return record.keySet();
        }
        
        @Override
        public boolean hasMember(String key) {
            return record.containsKey(key);
        }
        
        @Override
        public void putMember(String key, Value value) {
            ((IModifyRecord) record).set(key, value.as(Object.class));
        }
    }
}
```

### 4.3 表单验证服务

```java
@Service
@RequiredArgsConstructor
public class JsValidationService {
    private final JsEngineManager jsEngineManager;
    
    /**
     * 验证记录
     * @param entityName 实体名称
     * @param record 记录
     * @return 验证结果
     */
    public ValidationResult validateRecord(String entityName, IRecord record) {
        try {
            Object result = jsEngineManager.executeFunction(
                entityName, 
                "validation.validateForm", 
                new ProxyRecord(record)
            );
            
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                boolean valid = (boolean) resultMap.getOrDefault("valid", true);
                List<Map<String, Object>> errors = (List<Map<String, Object>>) resultMap.getOrDefault("errors", List.of());
                
                return new ValidationResult(valid, errors);
            }
            
            return new ValidationResult(true, List.of());
        } catch (Exception e) {
            log.error("Error validating record for entity: " + entityName, e);
            return new ValidationResult(false, List.of(Map.of(
                "field", "_global",
                "message", "验证过程发生错误: " + e.getMessage()
            )));
        }
    }
    
    /**
     * 验证结果类
     */
    @Data
    @AllArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private List<Map<String, Object>> errors;
    }
}
```

## 5. 前端实现

### 5.1 业务逻辑加载器

```typescript
// src/lib/business-logic/loader.ts

/**
 * 业务逻辑加载器
 */
export const loadEntityLogic = async (entityName: string) => {
  try {
    // 动态导入业务逻辑文件
    const module = await import(`/business-logic/${entityName}/index.js`);
    return module.default;
  } catch (error) {
    console.error(`Failed to load business logic for entity ${entityName}`, error);
    return null;
  }
};

/**
 * 业务逻辑缓存
 */
const logicCache: Record<string, any> = {};

/**
 * 获取实体业务逻辑
 */
export const getEntityLogic = async (entityName: string) => {
  if (!logicCache[entityName]) {
    logicCache[entityName] = await loadEntityLogic(entityName);
  }
  return logicCache[entityName];
};

/**
 * 执行业务逻辑函数
 */
export const executeLogicFunction = async (
  entityName: string,
  functionPath: string,
  ...args: any[]
) => {
  const logic = await getEntityLogic(entityName);
  if (!logic) return null;
  
  // 解析函数路径
  const pathParts = functionPath.split('.');
  let func = logic;
  
  for (const part of pathParts) {
    func = func[part];
    if (!func) return null;
  }
  
  if (typeof func === 'function') {
    return func(...args);
  }
  
  return null;
};
```

### 5.2 表单验证集成

```typescript
// src/lib/business-logic/validation.ts
import { z } from 'zod';
import { executeLogicFunction } from './loader';

/**
 * 创建表单验证schema
 */
export const createValidationSchema = (entityName: string) => {
  return z.object({}).superRefine(async (data, ctx) => {
    // 调用业务逻辑中的表单验证
    const validationResult = await executeLogicFunction(
      entityName,
      'validation.validateForm',
      data
    );
    
    // 处理验证结果
    if (validationResult && !validationResult.valid) {
      for (const error of validationResult.errors) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: [error.field],
          message: error.message,
        });
      }
    }
  });
};

/**
 * 表单验证钩子
 */
export const useFormValidation = (entityName: string, formValues: any) => {
  const validateForm = async () => {
    return executeLogicFunction(entityName, 'validation.validateForm', formValues);
  };
  
  return { validateForm };
};
```

### 5.3 计算规则集成

```typescript
// src/lib/business-logic/calculation.ts
import { useEffect } from 'react';
import { executeLogicFunction } from './loader';

/**
 * 表单字段计算钩子
 */
export const useFieldCalculation = (
  entityName: string,
  formValues: any,
  onChange: (field: string, value: any) => void
) => {
  useEffect(() => {
    const calculateFields = async () => {
      const logic = await getEntityLogic(entityName);
      if (!logic || !logic.calculation) return;
      
      // 获取需要计算的字段
      const fieldsToCalculate = Object.keys(formValues).filter(
        field => typeof logic.calculation.calculateField === 'function'
      );
      
      // 计算字段值
      for (const field of fieldsToCalculate) {
        const value = await executeLogicFunction(
          entityName,
          'calculation.calculateField',
          field,
          formValues
        );
        
        if (value !== undefined && value !== formValues[field]) {
          onChange(field, value);
        }
      }
    };
    
    calculateFields();
  }, [entityName, formValues, onChange]);
};
```

### 5.4 业务规则集成

```typescript
// src/lib/business-logic/rules.ts
import { executeLogicFunction } from './loader';

/**
 * 执行保存前规则
 */
export const executeBeforeSaveRules = async (entityName: string, record: any) => {
  return executeLogicFunction(entityName, 'rules.beforeSave', record);
};

/**
 * 执行保存后规则
 */
export const executeAfterSaveRules = async (entityName: string, record: any) => {
  return executeLogicFunction(entityName, 'rules.afterSave', record);
};

/**
 * 保存钩子
 */
export const useSaveRules = (entityName: string) => {
  const beforeSave = async (record: any) => {
    return executeBeforeSaveRules(entityName, record);
  };
  
  const afterSave = async (record: any) => {
    return executeAfterSaveRules(entityName, record);
  };
  
  return { beforeSave, afterSave };
};
```

## 6. 示例：订单实体业务逻辑

```javascript
// /business-logic/order/index.js
export default {
  // 元数据
  metadata: {
    name: "order",
    displayName: "订单",
    description: "客户订单信息"
  },
  
  // 验证规则
  validation: {
    validateForm(record) {
      const errors = [];
      
      // 验证订单项不能为空
      if (!record.items || record.items.length === 0) {
        errors.push({ field: 'items', message: '订单项不能为空' });
      }
      
      // 验证订单总金额
      if (record.total_amount <= 0) {
        errors.push({ field: 'total_amount', message: '订单总金额必须大于0' });
      }
      
      // 验证客户信息
      if (!record.customer_id) {
        errors.push({ field: 'customer_id', message: '客户不能为空' });
      }
      
      return { valid: errors.length === 0, errors };
    }
  },
  
  // 计算规则
  calculation: {
    calculateField(field, record) {
      if (field === 'total_amount') {
        // 计算订单总金额
        return (record.items || []).reduce((sum, item) => sum + (item.price * item.quantity), 0);
      }
      
      if (field === 'discount_amount') {
        // 计算折扣金额
        const totalAmount = this.calculateField('total_amount', record);
        return record.discount_rate ? totalAmount * record.discount_rate / 100 : 0;
      }
      
      if (field === 'payable_amount') {
        // 计算应付金额
        const totalAmount = this.calculateField('total_amount', record);
        const discountAmount = this.calculateField('discount_amount', record);
        return totalAmount - discountAmount;
      }
      
      return null;
    }
  },
  
  // 业务规则
  rules: {
    beforeSave(record) {
      // 设置订单编号
      if (!record.order_no) {
        record.order_no = 'ORD' + new Date().getTime();
      }
      
      // 计算金额
      record.total_amount = this.calculation.calculateField('total_amount', record);
      record.discount_amount = this.calculation.calculateField('discount_amount', record);
      record.payable_amount = this.calculation.calculateField('payable_amount', record);
      
      // 设置订单状态
      if (!record.status) {
        record.status = 'PENDING';
      }
      
      return record;
    },
    
    afterSave(record) {
      // 可以在这里执行后续操作，如发送通知等
      console.log(`Order ${record.order_no} saved successfully`);
    }
  },
  
  // 自定义函数
  functions: {
    // 计算订单折扣
    calculateDiscount(order, discountRate) {
      const totalAmount = this.calculation.calculateField('total_amount', order);
      return totalAmount * discountRate / 100;
    },
    
    // 生成订单摘要
    generateSummary(order) {
      return {
        orderNo: order.order_no,
        customerName: order.customer_name,
        totalAmount: order.total_amount,
        itemCount: (order.items || []).length,
        status: order.status
      };
    }
  }
};
```

## 7. 部署与维护

### 7.1 JavaScript文件部署

1. **开发环境**：
    - 前端：直接从源代码目录加载
    - 后端：从classpath资源目录加载

2. **生产环境**：
    - 前端：打包到静态资源目录
    - 后端：打包到JAR文件的资源目录

### 7.2 版本管理

1. **版本控制**：将JavaScript业务逻辑文件纳入版本控制系统
2. **版本标记**：在JavaScript文件中添加版本信息
3. **版本迁移**：提供版本迁移工具和脚本

### 7.3 监控与日志

1. **执行日志**：记录JavaScript代码的执行情况
2. **性能监控**：监控JavaScript代码的执行性能
3. **错误追踪**：捕获和记录JavaScript执行错误

## 8. 安全考虑

### 8.1 JavaScript沙箱

1. **资源限制**：限制JavaScript代码的执行资源（内存、CPU等）
2. **超时控制**：设置JavaScript代码的执行超时时间
3. **访问控制**：限制JavaScript代码对Java对象的访问权限

### 8.2 代码审计

1. **静态分析**：对JavaScript代码进行静态分析，检查潜在问题
2. **代码审查**：实施代码审查流程，确保代码质量和安全性
3. **安全测试**：进行安全测试，防止恶意代码注入

## 9. 总结

基于GraalVM.js实现前后端业务逻辑共用是一种高效的开发方式，特别适合低代码平台的业务场景。通过合理的架构设计和实现，可以实现业务逻辑的高度复用，提高开发效率，降低维护成本。

本方案的核心优势在于：

1. **统一语言**：使用JavaScript作为业务逻辑的统一语言
2. **代码复用**：前后端共用同一套业务逻辑代码
3. **灵活性**：利用JavaScript的动态特性，实现灵活的业务规则
4. **可维护性**：业务逻辑与技术实现分离，提高代码可维护性

通过本方案，可以显著提高低代码平台的开发效率和业务灵活性，为用户提供更强大的业务能力。
